const fs = require("fs")
const path = require("path")

function getPackageContent(packagePath) {
  const packageJsonPath = path.join(__dirname, packagePath)
  const packageJsonContent = fs.readFileSync(packageJsonPath, "utf8")
  const packageJson = JSON.parse(packageJsonContent)
  return packageJson
}

const mainPackageName = "apollo-ui"

const mainJsonPath = path.join(
  __dirname,
  `../packages/${mainPackageName}/package.json`
)
const legacyMainJsonPath = path.join(__dirname, `../packages/ui/package.json`)
const coreMainJsonPath = path.join(
  __dirname,
  `../packages/apollo-core/package.json`
)
const storefrontMainJsonPath = path.join(
  __dirname,
  `../packages/apollo-storefront/package.json`
)

const outputPackageFilePath = mainJsonPath
const outputLegacyPackageFilePath = legacyMainJsonPath
const outputCorePackageFilePath = coreMainJsonPath
const outputStorefrontPackageFilePath = storefrontMainJsonPath

const packageJson = getPackageContent(
  `../packages/${mainPackageName}/package.json`
)

const tokenPackageJson = getPackageContent(
  "../packages/apollo-token/package.json"
)
const iconPackageJson = getPackageContent("../packages/icons/package.json")
const legacyTokenPackageJson = getPackageContent(
  "../packages/tokens/package.json"
)
const legacyPackageJson = getPackageContent("../packages/ui/package.json")
const corePackageJson = getPackageContent(
  "../packages/apollo-core/package.json"
)
const storefrontPackageJson = getPackageContent(
  "../packages/apollo-storefront/package.json"
)

const command = process.env.COMMAND || "dev"

// Adjust the dependencies of legacy package
if (command === "build") {
  legacyPackageJson.main = "./dist/index.cjs"
  legacyPackageJson.module = "./dist/index.js"
  legacyPackageJson.types = "./dist/index.d.ts"

  legacyPackageJson.exports = {
    ".": {
      types: "./dist/index.d.ts",
      import: "./dist/index.js",
      require: "./dist/index.cjs",
    },
    "./theme.css": "./dist/theme.css",
    "./theme": {
      import: "./dist/theme.js",
      require: "./dist/theme.cjs",
    },
    "./package.json": "./package.json",
  }

  legacyPackageJson.dependencies["@apollo/core"] = corePackageJson.version
  legacyPackageJson.dependencies["@design-systems/apollo-icons"] =
    iconPackageJson.version
  legacyPackageJson.dependencies["@design-systems/tokens"] =
    legacyTokenPackageJson.version

  fs.writeFileSync(
    outputLegacyPackageFilePath,
    JSON.stringify(legacyPackageJson, null, 2)
  )
}

// Adjust the dependencies of core package
if (command === "build") {
  corePackageJson.main = "./dist/index.umd.cjs"
  corePackageJson.module = "./dist/index.js"
  corePackageJson.types = "./dist/index.d.ts"
  corePackageJson.exports = {
    ".": {
      import: "./dist/index.js",
      require: "./dist/index.umd.cjs",
      types: "./dist/index.d.ts",
    },
  }
  fs.writeFileSync(
    outputCorePackageFilePath,
    JSON.stringify(corePackageJson, null, 2)
  )
}

// Adjust the dependencies of storefront package
if (command === "build") {
  storefrontPackageJson.main = "./dist/index.umd.cjs"
  storefrontPackageJson.module = "./dist/index.js"
  storefrontPackageJson.types = "./dist/index.d.ts"
  storefrontPackageJson.exports = {
    ".": {
      import: "./dist/index.js",
      require: "./dist/index.umd.cjs",
      types: "./dist/index.d.ts",
    },
    "./style.css": "./dist/index.css",
  }

  // Replace the dependencies with latest version
  storefrontPackageJson.dependencies["@apollo/core"] = corePackageJson.version
  storefrontPackageJson.dependencies["@apollo/token"] = tokenPackageJson.version
  storefrontPackageJson.dependencies["@apollo/ui"] = packageJson.version
  storefrontPackageJson.publishedAt = new Date().toISOString()
  fs.writeFileSync(
    outputStorefrontPackageFilePath,
    JSON.stringify(storefrontPackageJson, null, 2)
  )
}

if (command === "build") {
  packageJson.main = "./dist/index.umd.cjs"
  packageJson.module = "./dist/index.js"
  packageJson.types = "./dist/index.d.ts"
  packageJson.exports = {
    ".": {
      import: "./dist/index.js",
      require: "./dist/index.umd.cjs",
      types: "./dist/index.d.ts",
    },
    "./legacy": {
      types: "./dist/legacy/index.d.ts",
      import: "./dist/legacy.js",
      require: "./dist/legacy.umd.cjs",
    },
    "./style.css": "./dist/index.css",
  }

  // Replace the dependencies with latest version
  packageJson.dependencies["@design-systems/apollo-icons"] =
    iconPackageJson.version
  packageJson.dependencies["@design-systems/apollo-ui"] =
    legacyPackageJson.version
  packageJson.dependencies["@apollo/token"] = tokenPackageJson.version
  packageJson.publishedAt = new Date().toISOString()
  fs.writeFileSync(outputPackageFilePath, JSON.stringify(packageJson, null, 2))
}
