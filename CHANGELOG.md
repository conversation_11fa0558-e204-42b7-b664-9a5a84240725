# Changelog

## TODO: THIS FILE IS UNCLEAN

All notable changes to this project will be documented in this file.

## [unreleased]

### 🚀 Features

- Sidebar improvement
- Implement pagination component - [UXUI-57]
- Add sorting icon component - [UXUI-243]
- Add MenuOption and MenuList component - [UXUI-244]
- Update Sidebar to be collapsible - [UXUI-266]
- Add DateRange function into DateInput
- Add FlagIcon
- Update DateInput to be able to typing the value (only SingleDate mode)
- Autocomplete improvement - [UXUI-374, UXUI-449]
- Update DateInput with range mode to be able to type the values
- Add apollo/ui package and update token compatible
- New docs update to integrate with tailwind
- Add sandpack
- Document playgroup [wip]
- Update component preview UI
- Implement Accordion
- Add apollo-token module
- Add apollo-token package
- Update url config
- Update version
- Update new apollo docs

### 🐛 Bug Fixes

- Minor update Checkbox styling to be align with Figma
- Clean up warning
- Test failed caused by react-is
- Test failed
- Token type issue
- Mistaken import file
- Type cause build failed
- Crashing type
- Typing error
- Type error
- Codesandbox failed caused by vite.config.js

### 📚 Documentation

- Add getting started content
- Initial roadmap file
- Add roadmap 2025 article
- Fix build failed caused by broken link
- Rename the update title
- Update landing page
- Add workflow documentation

### 🧪 Testing

- Update unit-test

### ⚙️ Miscellaneous Tasks

- Update version
- Test deploy script for document site
- Fix invalid target
- Add missing dependecies
- Sync lock file
- Trigger build
- Update script
- Update docs
- Add build script for new packages
- Sync lock file
- Rename step
- Update script about registry
- Trigger build
- Fix build script for token
- Fix build script
- Fix failed script
- Clean up
- Remove unused deps
- Sync lockfile
- Trigger build
- Update build script node version

## [1.10.0] - 2024-11-01

### 🚀 Features

- Sidebar component [clean up]

## [1.10.0-beta.2] - 2024-10-30

### 🐛 Bug Fixes

- Sidebar component - export missing props

## [1.9.3] - 2024-10-23

### 🚀 Features

- Add onDelete and onCancelUpload prop to the hook of file download

### 🐛 Bug Fixes

- Minor issue
- Autocomplete styling are not correct

## [1.9.2] - 2024-10-17

### 💼 Other

- Update tokens bundler config

## [1.9.1] - 2024-10-17

### 🐛 Bug Fixes

- Autocomplete with form issues and minor bugs

### ⚙️ Miscellaneous Tasks

- Update package version

## [1.9.0] - 2024-10-10

### 🚀 Features

- Add UploadBox component and fixing minor issues

### 🐛 Bug Fixes

- Autocomplete minor issue

## [1.8.1] - 2024-09-23

### 🐛 Bug Fixes

- Minor issue

## [1.8.0] - 2024-09-13

### 🚀 Features

- Add IconButton component

### 🐛 Bug Fixes

- Date-input calendar not working fine
- Minor bug of date-input

### ⚙️ Miscellaneous Tasks

- Clean up
- Clean up
- Update netlify-cli version
- Update deps
- Temporary disabled audit step
- Test ci
- Update next version
- Enable audit
- Sync lock file
- Clear all audit override
- Sync code

## [1.7.1] - 2024-09-11

### 🚀 Features

- Update date-input to be able use as Month and Year Picker
- Update tab component and clean up docs

### 📚 Documentation

- Update missing case of locale on dateinput component
- Add deprecated tag to datepicker
- Update missing prop on the product card

### ⚙️ Miscellaneous Tasks

- Patch issue lib
- Update lock file
- Update main package version

## [1.7.0] - 2024-09-09

### 🚀 Features

- Add DateInput component [wip]
- Update date-input
- Add dateinput component
- Add DateInput component

### 🐛 Bug Fixes

- Add new variant modal and remove hover style on mobile size screen
- Product-card and autocomplete minor bug
- Calendar button not work as expected

### ⚙️ Miscellaneous Tasks

- Fix publish script failed
- Fix invalid build script step
- Clean up
- Update version
- Clean up
- Clean up

## [1.7.0-rc.2] - 2024-09-03

### 🐛 Bug Fixes

- Minor issue
- Build issue

### ⚙️ Miscellaneous Tasks

- Update build script
- Update gitignore file
- Optimize build script
- Optimize api
- Temporary disabled api
- Update build script
- Update script
- Update ignore file
- Update build script
- Update deploy script
- Update version
- Fix build failed
- Update target env
- Fix mispell script
- Fix deploy script failed
- Fix build script
- Add missing script
- Add missing script
- Update build script
- Fix deploy failed
- Build script failed
- Fix crash ci
- Fix crash script
- Add global turbo for cli
- Update build script
- Update script
- Fix build script
- Update build script
- Update script
- Update script
- Update netlify config
- Update build script
- Add installing bash script
- Fix failed build script
- Fix ci failed
- Update scirpt
- Fix failed build
- Fix build failed
- (temp) test build script
- (temp) test build script
- Update build script
- Update build script
- Test script
- Test script
- Test script
- Add deno
- Add deno
- Add deno
- Add deno
- Add deno
- Add deno
- Add deno
- Add deno
- Revert all change
- Update script
- Test script
- Test script
- Fix git script failed

## [1.7.0-rc.1] - 2024-08-30

### 🚀 Features

- Update auto complete [wip]
- Add load more feature
- Update auto complete docs

### 🐛 Bug Fixes

- Autocomplete type error

### 📚 Documentation

- Update colors document to have hex code
- Update docs and add classes

### ⚙️ Miscellaneous Tasks

- Fix deps
- Update lint stage file
- Fix script
- Clean up
- Clean up
- Remove log
- Update version as rc

## [1.6.1] - 2024-08-28

### 🚀 Features

- Add data test id to input and date picker seperatedly

### ⚙️ Miscellaneous Tasks

- Update package version

## [1.6.0] - 2024-08-23

### 🚀 Features

- Update product-card styling
- Pass div prop into the container
- Implement gemini ai
- Update cache checking
- Improvement speed and cache logic
- Update script ot generate ai seed
- Tune AI to response more precise
- Update AI
- Optimise ai response
- Update AI
- Add FloatButton component [wip]

### 🐛 Bug Fixes

- Api creash
- Mispell name
- Ci crash

### 💼 Other

- Update deps
- Update config

### 📚 Documentation

- Update ProductCard documentation
- Update document and add unit-test
- Update content
- Update description

### ⚙️ Miscellaneous Tasks

- Update script to update version of deps and add publish script for design token package
- Sync lock file
- Make build trigger for every push
- Script is crash
- Remove script
- Add jsdoc comment to product-card props
- Clean up
- Optimize ai api
- Remove AI menu
- Update script
- Update build script
- Clean up
- Update script
- Update script
- Update script
- Update script
- Update script
- Update before_script
- Fix failed pipeline
- Fix crash pipeline
- Fix script failed
- Fix script failed
- Update script
- Clean up
- Make sync-with-github script only work for main branch
- Fix build failed
- Clean up
- Update changes
- Update package version
- Skip audit for temporary
- Fix invalid syntax

## [1.5.0] - 2024-08-09

### 🚀 Features

- Update navbar to have package selector [wip]
- Add token package and update structure of design tokens
- Add capsule tab

### 📚 Documentation

- Fix searching function not working
- Add report issue button
- Update menu
- Update button document
- Update input document and styling to aligned with Figma
- Update misleading content

### 🎨 Styling

- Update modal mobile style
- Update navbar style

### ⚙️ Miscellaneous Tasks

- Fix wrong style
- Clean up
- Update missing deps
- Make build step to install deps everytime before build the pacakge
- Update build config
- Update ci
- Build failed because of missing file
- Update package file
- Update lock file to be sync
- Add comment into utils types
- Clean up
- Capsule tab add className props
- Add optional chaning symbol to CapsuleTab
- Clean up and update docs

## [1.4.0] - 2024-07-30

### 🚀 Features

- Add ProductCard component [wip]
- Update image overlay prop name
- Update product card
- Update product card title to be ReactNode to allowed customization
- Add navbar component [wip]
- Add Navbar component
- Update navigation bar styling

### 📚 Documentation

- Update changelog page
- Update sidemenu structure
- Update misleading information
- Make font style more bold and add figma ref
- Replace too common css selector
- Update codeblock to be monospace font
- Add missing description

### 🎨 Styling

- Update navbar style

### 🧪 Testing

- Add unit-test to product-card
- Add unit-test for navbar

### ⚙️ Miscellaneous Tasks

- Replace manual fetch to react-query
- Error handling
- Error handling
- Enable ci to build when push to any branch
- Trigger build / test pipeline when ci file is changed
- Update turbo file
- Using mdx files instead normal next page
- Update changelog page description
- Clean up
- Clean up
- Update document
- Update props and type checked
- Check more type to display typo
- Update version
- Clean up
- Export more props type

## [1.3.0] - 2024-07-17

### 🚀 Features

- Add modal component

### 🐛 Bug Fixes

- Color prop not working on apollo-icons
- Minor bug on select

### 💼 Other

- Add \_redirects file to make all request redirect to index.html file

### 🚜 Refactor

- Select component

### 📚 Documentation

- Make document to be only light mode for now since some component are not support dark mode

### ⚙️ Miscellaneous Tasks

- Update icon package version
- Remove unused props
- Update pnpm version
- Update document and type description
- Clean up
- Fix typo
- Clean up
- Update type to be more safe
- Clean up
- Add modal test file
- Clean up
- Add pre-commit to run auditting
- Update missing note on the modal document
- Clean up
- Update version

## [1.2.0] - 2024-07-08

### 🚀 Features

- Add Radio and Radio group component
- Add missing use cases to radio
- Add accordion component

### 🐛 Bug Fixes

- [issue-#63] clear warning about passing wrong attribute into the DOM
- Placeholder not showing up on select

### 💼 Other

- Update version

### ⚙️ Miscellaneous Tasks

- Update version
- Restructure files
- Clean up types
- Add unit-test for radio and radio-group
- Format and fix import part causing test failed
- Clean up
- Format and update import part of typography
- Make accordion header ellipsis when text is too long
- Replace div with semantic html
- Clean up
- Clean up
- Update typedef
- Clean up
- Update doc

## [1.1.0] - 2024-06-22

### 🐛 Bug Fixes

- Remove unnecessary boolean
- Cannot click label to toggle the checkbox
- _(chip)_ Remove unnecessary id
- Add missing "indeterminate" at the input element

### 💼 Other

- [PMS05][125351][126772, 126845] fix chip component
- [PMS04][125351][125585] implement tab component
- [PMS04][125351][125584] implement Checkbox component
- Rm some deps from external option in rollup

### 🚜 Refactor

- Remove useEffect and apply the same behavior as MUI
- Update the auto gen ID string
- Move useForwardedRef to /hooks/useForwardedRef.tsx

### 📚 Documentation

- Add changelog page
- Update docs for Tabs and Checkbox

### ⚙️ Miscellaneous Tasks

- Add pipeline test report(jest-junit) #52
- Change default DatePicker's format #55
- Use placeholder when Input is disabled #47
- Display icons's name #57
- Update apollo-tokens to v0.0.1
- Fix vuln deps
- Fix vuln deps
- Fix merge conflict
- Update packages version to v1.1.0

## [1.0.1] - 2024-05-21

### 🐛 Bug Fixes

- Broken DatePicker due to transpiling(Rollup) #53

### 💼 Other

- Update v1.0.1 (fix DatePicker)

## [1.0.0] - 2024-05-20

### 🚀 Features

- _(Toast)_ Separate className from other props and use it

### 🐛 Bug Fixes

- Breadcrumbs change div to nav element
- Eslint
- Vulnerability (lodash.template)
- Vulnerabilities again
- Remove forwardRef since we didn't use it
- _(playground)_ Add padding-bottom to prevent toasts overlapping issue

### 💼 Other

- [PMS04][125351][125352] Implement switch
- [PMS04][125351][125694] Implement Toast component
- [PMS04][125351][125354] implement chip component

### 📚 Documentation

- Updates
- Add apollo-token informations for more clearly using
- Update docs for Toast/Alert/Chip/Switch

### ⚙️ Miscellaneous Tasks

- Refactoring to use extends instead
- Add default merge request template & update issue templates
- Run build on default branch only #49
- Remove formatOnSave in setting.json
- Fix merging Alert conflict
- Update version to v1.0.0

## [1.0.0-rc.2] - 2024-04-04

### 🚀 Features

- Add Typography component
- Add development playground for dev mode to avoid conflict code when contributing
- Update <Input/> [skip ci]
- Add icon component and icon-builder package
- Init DatePicker

### 🐛 Bug Fixes

- Bundle ui component
- Commented out npm config set in gitlab-ci
- Change CI_PROJECT_ID to TOKENS_PROJECT_ID
- Pipeline
- Pipeline
- Pipeline
- Pipeline
- Try to use pipeline cache
- Package checksum & rm swc lib
- Add assetPreix to next.config.js
- Comment out unused component
- Tailwind config glob pattern
- Pnpm-lock
- Gitlab-ci
- Select use truncate class
- Wrap Icon component with Fragment just in case
- Annoying tsconfig rootDir error
- Normalizing svg icon [#34]
- Add id to breadcrumbs
- Jest test failed due to importing
- Breadcrumbs change div to nav element
- Jest test failed due to importing

### 💼 Other

- Add dev script & remove unused in packages/ui
- Add spack.config.js to bundle token value into /dist
- Export static asset for www && add serve script
- Add gitlab-ci and add test pipeline
- Add build pipeline
- Use rollup instead of swc && update build/dev scripts
- Resolve alias path in tsconfig && add prettier.config.js
- Update resolving path alias when bundling
- Add clean&install script & rm unused autoprefixer for now
- Update linting & add lint-stage
- Move design-token to devDependencies
- Change tailwind content path for @design-systems/ui && move deps to devDependencies
- Update basePath & update docs
- Update Button and docs & rm unused
- Update Button & update docs
- Switch off dev mode
- Update docs & rm unused
- Add build:publish script and update deploy:publish pipeline
- Add peerDependencies to packages/ui & update build:publish to run only at packages/ui
- Move mui package to dependencies instead of peerDev
- Add Typography and update docs
- Add externals list to rollup & fix slow compile time by specify extenstion in glob pattern
- Update build icon script & add all icons
- Normalize svg fill to currentColor when run build-icons
- Fix regex pattern when building icons
- Initial breadcrumbs
- Fix rootDir in tsconfig
- Bump version 0.0.2
- Revise building icon
- Separate icon package to @design-systems/icons
- Edit rootDir in tsconfig
- Handle dynamicImport for packages/icons
- Rm and ignore dynamicImport file(only being used when building pipeline)
- Eclips for hidden
- Breadcrumbs document
- Add hover show bg
- Setup rollup bundle to suport code-splitting
- Use icon component from apolo
- Exclude test file and scripts folder when building
- Update apollo-icons package version
- Update paths to match with new git path(official project name) [#30]
- Update (docs) version based-on package.json
- Write tokens to local file & change withUI.js -> with-ui.ts
- Move tailwind custom plugin to separated files
- V1.0.0-rc.1
- Fix bundling
- Fix rollup bundling again
- Reduce bundle size
- Add releaseTag.js & add release script in package.json [#44]
- Out folder in turbo.json
- Write tokens to local file & change withUI.js -> with-ui.ts
- Move tailwind custom plugin to separated files
- V1.0.0-rc.1
- Fix bundling
- Fix rollup bundling again
- Reduce bundle size
- Add releaseTag.js & add release script in package.json [#44]
- Out folder in turbo.json

### 🚜 Refactor

- Remove unused svg & renaming
- Rm unused svg again & ignore source icons in /packages/icons

### 📚 Documentation

- Update doc(www) for icon and icon-collections
- Disable navbar theme toggle #35 and update DatePicker docs
- Display labelVersion for apollo-tokens
- Update docs for apollo-icons and apollo-tokens [#33]
- Update doc for test publishing registry to local proxy [#45]
- Updates
- Display labelVersion for apollo-tokens
- Update docs for apollo-icons and apollo-tokens [#33]
- Update doc for test publishing registry to local proxy [#45]

### 🧪 Testing

- Config test
- Add tests for Button & update jest config
- Add tests for Input & change rootDir in tsconfig
- Add tests for DatePicker [skip ci]

### ⚙️ Miscellaneous Tasks

- Add commitlint
- Try to use swc to bundle files(unfinish)
- Update REAME
- Remove unused & update README
- Update project structure in README
- Update README
- Remove unused
- Try npm config set in ci
- Rm gitlab-ci.yml from file change test pipeline
- Ignore changes in _.md and _.example
- Fix ignore changes
- Fix ignore changes
- Fix ignore changes
- Fix ignore changes
- Fix ignore changes
- Try to fix build error(spack)
- Fix ignore changes
- Try to fix build error(spack)
- Add deploy pipeline
- Fix deploy pipeline
- Fix deploy pipeline
- Linting
- Add deploy publish package pipeline & disable redirect in next.config.js
- Fix publish pipeline
- Fix publish pipeline
- Fix publish pipeline & rename scope registry name for packages/ui
- Fix mv folder
- Fix publish pipeline by moving before_script to scripts
- Clear www cache before building & rename scope's name
- Update Button & add syntax highlight for code snippet (docs)
- Update Button variant danger
- Detect dev/production mode in /www
- Update docs
- Add issue report template/add CONTRIBUTING.md,update README.md/rm unused syntaxhighlight lib
- Update color docs
- Update color docs for alias token
- Update pnpm lockfile in root workspace
- Fix image src by using next/image & update docs
- Add prefix to Image's src since assetPrefix/basePath not working with next/image
- Enable auto deploy(www) & update docs & update @design-systems/tokens version
- Update <Input/> docs
- Add deepmerge lib and limit build pipeline to only main branch
- Set h-auto for playground code editor
- Add eslint for packages/build-icons & rm unused
- Fix height in playground code editor
- Revise re-exporting createIcon function
- Update eslint
- Add createPlugin fuction for creating tailwind plugin instead of importing from external
- Add missing deps
- Update gitlab-ci cache
- Move minimist from devDep to dep
- Improve button #27
- Running test in pre-commit lint-stage
- Add type in tsconfig to support Jest & add size variant to Icon
- Organize folder structure for tools & update docs for icon-collections
- Merge from main
- Merge remote-tracking branch origin into feat-datepicker
- Update DatePicker styles
- FullWidth datepicker & use IBM Plex Sans Thai font
- Upate tokens docs #37, update DatePicker docs, use display grid for calendar
- Update DatePicker and docs
- Add playground page & update DatePicker docs
- Main datepicker to breadcrumb
- Format code breadcrumbs
- Add example using icon
- Add select docs
- Update select
- Add usecase select docs
- Update Icon & handle rendering icon differently if it's an Apollo icons(our icons)
- Update typos in Select docs & hide theme selector for now
- Merge main and use icon component
- Add rodo
- Update modify script & fix typos in docs
- Update @design-systems/ui version to 0.0.3
- Fix building ui
- Rename project's name to official name [#30]
- Update gitlab-ci to match with official project name
- Add peerDeps & formatting Breadcrumbs codes & rm redundant .npmignore
- Update lock file
- Update TimePicker styles
- Add audit and test package ui to pipeline
- Update dependency of sharp to fix vulnerability issue
- Build icons when test:ui stage
- Change hoc from 'withUI' to 'withApollo' [#43]
- Turn off sourcemap for building apollo-icons for now
- Add publish:tag pipeline & rename pipelines
- Add git & since before_script is deprecated, use default instead
- Force build to avoid cache & move apk add git to publish:tag only
- Set git config in publish:tag pipeline
- Fix publish:tag pipeline
- Fix publish:tag pipeline
- Run some pipeline only if some folders/files changed
- Fix publish:tag pipeline
- Bring back .pipeline for now
- Publish:tag
- Refactoring to use extends instead
- Add peerDeps & formatting Breadcrumbs codes & rm redundant .npmignore
- Update lock file
- Update TimePicker styles
- Add audit and test package ui to pipeline
- Update dependency of sharp to fix vulnerability issue
- Build icons when test:ui stage
- Change hoc from 'withUI' to 'withApollo' [#43]
- Turn off sourcemap for building apollo-icons for now
- Add publish:tag pipeline & rename pipelines
- Add git & since before_script is deprecated, use default instead
- Force build to avoid cache & move apk add git to publish:tag only
- Set git config in publish:tag pipeline
- Fix publish:tag pipeline
- Fix publish:tag pipeline
- Run some pipeline only if some folders/files changed
- Fix publish:tag pipeline
- Bring back .pipeline for now
- Publish:tag

<!-- generated by git-cliff -->
