"use client"

import { useState } from "react"
import { <PERSON>complete, Button, Modal, Select, Typography } from "@apollo/ui"
import { InfoCircle } from "@design-systems/apollo-icons"

import { ComponentGroup } from "./common"

export function ModalDemo() {
  const [open, setOpen] = useState(false)
  return (
    <ComponentGroup>
      <Button onClick={() => setOpen(true)}>Open Modal</Button>
      <Modal.Root
        open={open}
        onOpenChange={(open) => {
          setOpen(open)
        }}
        className="max-w-[400px]"
      >
        <Modal.Header icon={<InfoCircle size={20} />}>
          <Typography level="titleMedium">คุณกำลังส่งสร้างโปรโมชั่นใช่หรือไม่ ?คุณกำลังส่งสร้างโปรโมชั่นใช่หรือไม่ ?คุณกำลังส่งสร้างโปรโมชั่นใช่หรือไม่ ?</Typography>
          <Modal.CloseButton />
        </Modal.Header>
        <Modal.Content>
          <Typography level="bodyLarge">
            Once upon a time, there was a forest where plenty of birds lived and
            built their nests on the trees.
          </Typography>
          <Select label="Fruits">
            <Select.Option label="Apple" value="apple" />
            <Select.Option label="Banana" value="banana" />
            <Select.Option label="Cherry" value="cherry" />
          </Select>
          <Autocomplete
            label="Country"
            options={[
              { label: "United States", value: "us" },
              { label: "Canada", value: "ca" },
              { label: "United Kingdom", value: "uk" },
              { label: "Australia", value: "au" },
            ]}
            placeholder="Select your country"
          />
        </Modal.Content>
        <Modal.Footer>
          <Button className="self-stretch" color="negative">
            Button
          </Button>
          <Button className="self-stretch">Button</Button>
        </Modal.Footer>
      </Modal.Root>
    </ComponentGroup>
  )
}
