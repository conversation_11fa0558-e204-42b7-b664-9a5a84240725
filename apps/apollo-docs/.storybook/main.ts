import { createRequire } from "module"
import { dirname, join } from "path"
import type { StorybookConfig } from "@storybook/nextjs-vite"

// Create require function for ES modules
const require = createRequire(import.meta.url)

/**
 * This function is used to resolve the absolute path of a package.
 * It is needed in projects that use Yarn PnP or are set up within a monorepo.
 */
function getAbsolutePath(value: string): any {
  return dirname(require.resolve(join(value, "package.json")))
}

// Check if building for GitLab Pages
const isGitLabPages = process.env.********************** === "true"
const basePath = isGitLabPages ? "/design-systems/apollo/" : "/"

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    // Conditionally exclude Chromatic addon for Pages build
    ...(isGitLabPages ? [] : [getAbsolutePath("@chromatic-com/storybook")]),
    getAbsolutePath("@storybook/addon-docs"),
    getAbsolutePath("@storybook/addon-a11y"),
    getAbsolutePath("@storybook/addon-vitest"),
    getAbsolutePath("@storybook/addon-designs"),
    getAbsolutePath("storybook-addon-tag-badges"),
  ],
  framework: {
    name: getAbsolutePath("@storybook/nextjs-vite"),
    options: {},
  },
  staticDirs: ["../public"],
  viteFinal: async (config) => {
    if (isGitLabPages) {
      // Set base path for GitLab Pages
      config.base = basePath
    }
    return config
  },
  managerHead: (head) => {
    if (isGitLabPages) {
      return `
        ${head}
        <base href="${basePath}">
      `
    }
    return head
  },
}

export default config
