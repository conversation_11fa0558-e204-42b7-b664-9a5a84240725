import {
  Accordion,
  Alert,
  Autocomplete,
  Badge,
  Breadcrumbs,
  Button,
  CapsuleTab,
  Checkbox,
  Chip,
  DateInput,
  FloatButton,
  IconButton,
  Input,
  Pagination,
  Radio,
  RadioGroup,
  Select,
  SortingIcon,
  Switch,
  Tabs,
  Textarea,
  Typography,
  UploadBox,
} from "@apollo/ui"
import {
  Search,
  Heart,
  Setting,
  Plus,
} from "@design-systems/apollo-icons"

const apolloComponents = [
  // INPUTS CATEGORY
  {
    title: "Button",
    href: "@apollo∕ui/Components/Inputs/Button",
    description: "Primary action component with multiple variants and sizes",
    keywords: ["button", "action", "click", "primary", "negative", "filled", "outline", "text"],
    component: (
      <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
        <Button variant="filled">Filled</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="text">Text</Button>
      </div>
    ),
  },
  {
    title: "Icon Button",
    href: "@apollo∕ui/Components/Inputs/IconButton",
    description: "Compact button with icon for actions in limited space",
    keywords: ["icon", "button", "compact", "action", "small"],
    component: (
      <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
        <IconButton><Heart size={20} /></IconButton>
        <IconButton color="negative"><Setting size={20} /></IconButton>
        <IconButton><Plus size={20} /></IconButton>
      </div>
    ),
  },
  {
    title: "Float Button",
    href: "@apollo∕ui/Components/Inputs/FloatButton",
    description: "Floating action button for primary actions",
    keywords: ["float", "button", "floating", "action", "fab"],
    component: (
      <FloatButton icon={<Plus size={24} />} label="Add" />
    ),
  },
  {
    title: "Input",
    href: "@apollo∕ui/Components/Inputs/Input",
    description: "Text input field with validation and decorators",
    keywords: ["input", "text", "field", "form", "validation"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", width: "200px" }}>
        <Input placeholder="Basic input..." />
        <Input placeholder="With icon..." startDecorator={<Search size={16} />} />
      </div>
    ),
  },
  {
    title: "Textarea",
    href: "@apollo∕ui/Components/Inputs/Textarea",
    description: "Multi-line text input for longer content",
    keywords: ["textarea", "text", "multiline", "input", "form"],
    component: (
      <Textarea
        placeholder="Enter your message..."
        style={{ width: "200px", minHeight: "80px" }}
      />
    ),
  },
  {
    title: "Checkbox",
    href: "@apollo∕ui/Components/Inputs/Checkbox",
    description: "Binary choice input with optional label",
    keywords: ["checkbox", "check", "toggle", "boolean", "form"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Checkbox label="Option 1" defaultChecked />
        <Checkbox label="Option 2" />
        <Checkbox label="Disabled" disabled />
      </div>
    ),
  },
  {
    title: "Radio",
    href: "@apollo∕ui/Components/Inputs/Radio",
    description: "Single choice selection from multiple options",
    keywords: ["radio", "choice", "selection", "option", "form"],
    component: (
      <RadioGroup defaultValue="option1">
        <Radio value="option1">Option 1</Radio>
        <Radio value="option2">Option 2</Radio>
        <Radio value="option3">Option 3</Radio>
      </RadioGroup>
    ),
  },
  {
    title: "Switch",
    href: "@apollo∕ui/Components/Inputs/Switch",
    description: "Toggle switch for binary settings",
    keywords: ["switch", "toggle", "boolean", "setting", "on", "off"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        <Switch label="Enable notifications" defaultChecked />
        <Switch label="Dark mode" />
      </div>
    ),
  },
  {
    title: "Select",
    href: "@apollo∕ui/Components/Inputs/Select",
    description: "Dropdown selection from multiple options",
    keywords: ["select", "dropdown", "option", "choice", "picker"],
    component: (
      <div style={{ width: "180px" }}>
        <Select placeholder="Choose option...">
          <Select.Option value="option1" label="Option 1" />
          <Select.Option value="option2" label="Option 2" />
          <Select.Option value="option3" label="Option 3" />
        </Select>
      </div>
    ),
  },
  {
    title: "Autocomplete",
    href: "@apollo∕ui/Components/Inputs/Autocomplete",
    description: "Input with searchable dropdown suggestions",
    keywords: ["autocomplete", "search", "suggestions", "dropdown", "filter"],
    component: (
      <Autocomplete
        placeholder="Search..."
        style={{ width: "200px" }}
        options={[
          { label: "Apple", value: "apple" },
          { label: "Banana", value: "banana" },
          { label: "Cherry", value: "cherry" }
        ]}
      />
    ),
  },
  {
    title: "Date Input",
    href: "@apollo∕ui/Components/Inputs/DateInput",
    description: "Date picker input with calendar interface",
    keywords: ["date", "picker", "calendar", "time", "input"],
    component: (
      <div style={{ width: "180px" }}>
        <DateInput placeholder="Select date..." />
      </div>
    ),
  },
  {
    title: "Upload Box",
    href: "@apollo∕ui/Components/Inputs/UploadBox",
    description: "File upload area with drag and drop support",
    keywords: ["upload", "file", "drag", "drop", "attachment"],
    component: (
      <UploadBox label="Upload File" />
    ),
  },

  // DATA DISPLAY CATEGORY
  {
    title: "Typography",
    href: "@apollo∕ui/Components/Data Display/Typography",
    description: "Text component with consistent styling and hierarchy",
    keywords: ["text", "typography", "heading", "body", "title"],
    component: (
      <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
        <Typography level="headlineSmall">Headline</Typography>
        <Typography level="bodyLarge">Body text example</Typography>
        <Typography level="bodyMedium" style={{ color: "#6c757d" }}>Secondary text</Typography>
      </div>
    ),
  },
  {
    title: "Badge",
    href: "@apollo∕ui/Components/Data Display/Badge",
    description: "Small status indicator or count display",
    keywords: ["badge", "status", "count", "indicator", "notification"],
    component: (
      <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
        <Badge label="New" color="success" />
        <Badge label="Error" color="error" />
        <Badge label="Warning" color="warning" />
      </div>
    ),
  },
  {
    title: "Chip",
    href: "@apollo∕ui/Components/Data Display/Chip",
    description: "Compact element for tags, filters, or selections",
    keywords: ["chip", "tag", "filter", "selection", "removable"],
    component: (
      <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
        <Chip label="Tag 1" />
        <Chip label="Removable" onClose={() => {}} />
        <Chip label="Disabled" disabled />
      </div>
    ),
  },
    {
    title: "Sorting Icon",
    href: "@apollo∕ui/Components/Data Display/SortingIcon",
    description: "Icon indicating sort direction in tables",
    keywords: ["sorting", "icon", "table", "direction", "asc", "desc"],
    component: (
      <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
        <SortingIcon status="asc" />
        <SortingIcon status="desc" />
        <SortingIcon />
      </div>
    ),
  },

  // NAVIGATION CATEGORY
  {
    title: "Breadcrumbs",
    href: "@apollo∕ui/Components/Navigation/Breadcrumbs",
    description: "Navigation trail showing current page location",
    keywords: ["breadcrumbs", "navigation", "trail", "path", "hierarchy"],
    component: (
      <Breadcrumbs>
        <Typography level="bodyMedium">Home</Typography>
        <Typography level="bodyMedium">Category</Typography>
        <Typography level="bodyMedium">Current</Typography>
      </Breadcrumbs>
    ),
  },
  {
    title: "Pagination",
    href: "@apollo∕ui/Components/Navigation/Pagination",
    description: "Navigation control for paginated content",
    keywords: ["pagination", "navigation", "pages", "next", "previous"],
    component: (
      <Pagination
        count={5}
        defaultPage={2}
        onChange={() => {}}
      />
    ),
  },

  // FEEDBACK CATEGORY
  {
    title: "Alert",
    href: "@apollo∕ui/Components/Feedback/Alert",
    description: "Important messages and notifications",
    keywords: ["alert", "message", "notification", "warning", "error", "success"],
    component: (
      <div><Alert type="information" title="Information" description="This is an informational alert" /></div>
    ),
  },
  {
    title: "Toast",
    href: "@apollo∕ui/Components/Feedback/Toast",
    description: "Temporary notification messages",
    keywords: ["toast", "notification", "temporary", "message", "popup"],
    component: (
      <div><Alert type="information" title="Information" description="This is an informational alert" onClose={() => {}}/></div>
    ),
  },
  {
    title: "Modal",
    href: "@apollo∕ui/Components/Feedback/Modal",
    description: "Dialog overlay for focused interactions",
    keywords: ["modal", "dialog", "overlay", "popup", "focus"],
    component: (
       <img src="/assets/apollo-ui/modal/action-do.png" alt="modal without close button" style={{ maxWidth: "100%", padding: 8 }} />
    ),
  },

  // LAYOUT CATEGORY
  {
    title: "Accordion",
    href: "@apollo∕ui/Components/Layout/Accordion",
    description: "Collapsible content sections",
    keywords: ["accordion", "collapsible", "expand", "collapse", "sections"],
    component: (
      <Accordion label="Section 1" style={{ width: "200px" }}>
        <Typography level="bodyMedium">Content for section 1</Typography>
      </Accordion>
    ),
  },
    {
    title: "Tabs",
    href: "@apollo∕ui/Components/Layout/Tabs",
    description: "Tabbed interface for organizing content sections",
    keywords: ["tabs", "navigation", "sections", "content", "switch"],
    component: (
      <Tabs.Root defaultValue="tab1" style={{ width: "200px" }}>
        <Tabs.List>
          <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
          <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value="tab1">Content 1</Tabs.Panel>
        <Tabs.Panel value="tab2">Content 2</Tabs.Panel>
      </Tabs.Root>
    ),
  },
  {
    title: "Capsule Tab",
    href: "@apollo∕ui/Components/Layout/CapsuleTab",
    description: "Pill-style tab navigation with rounded appearance",
    keywords: ["capsule", "tab", "pill", "navigation", "rounded"],
    component: (
      <CapsuleTab
        tabs={[
          { id: "all", label: "All" },
          { id: "active", label: "Active" },
          { id: "completed", label: "Completed" }
        ]}
      />
    ),
  },

  // UTILITIES CATEGORY
  {
    title: "Portal",
    href: "@apollo∕ui/Components/Utilities/Portal",
    description: "Render content outside component tree",
    keywords: ["portal", "render", "outside", "tree", "teleport"],
    component: null
  },
]

export default apolloComponents
