import React from "react"
import { Typography } from "@apollo/ui"
import { Meta } from "@storybook/addon-docs/blocks"

import CautionCard from "../../../components/caution-card/CautionCard"

<Meta title="@apollo∕storefront/Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Get started with Apollo Storefront and build amazing customer UIs!</Typography>
</div>


Currently our Apollo Storefront is a specialized component library for customer website experiences, built on top of the Apollo Design System.

To install the `@apollo/storefront` package, we need to configure the `.npmrc` file (create this file in the project root) to point to our custom private registry on GitLab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

## Install

<CautionCard>
If you're outside CJ network, VPN is needed to install this package.
</CautionCard>


```bash
# with pnpm (recommended)
pnpm add @apollo/storefront

# with yarn
yarn add @apollo/storefront

# with npm
npm install @apollo/storefront
```

## Setup

Apollo Storefront components are styled using CSS variables. To ensure consistent styling, you need to use the `import "@apollo/storefront/style.css".

Place a `<ApolloProvider />` at the root of your app and pass theme as a prop.

```jsx title="App.js"
import React from 'react';
import { Theme, createThemeV2 } from "@apollo/ui"

import "@apollo/storefront/style.css"

const appTheme = createThemeV2()

function App({ children }) {
  return <Theme theme={appTheme}>{children}</Theme>
}
```

## Usage

You can import and use components directly:

```jsx title="Product Example"
import React from 'react'
import { ProductCard, ProductCardContent, ProductPrice } from "@apollo/storefront"

export default function Example() {
  return (
    <ProductCard>
      <img src="/product.jpg" alt="Product" />
      <ProductCardContent>
        <h3>Product Name</h3>
        <ProductPrice price={99} currency="$" />
      </ProductCardContent>
    </ProductCard>
  )
}
```

