import React from "react"
import { Typography } from "@apollo/ui"
import { Shop, Gift, ShoppingCart, Star, Mobile } from "@design-systems/apollo-icons"
import ResourceCard from "../../../components/resource-card/ResourceCard"
import { Meta } from "@storybook/addon-docs/blocks"

<Meta title="@apollo∕storefront/Introduction" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >@apollo/storefront</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Specialized components for customer facing website experiences</Typography>
  <div
    style={{
      display: "flex",
      gap: 8,
      flexWrap: "wrap",
      justifyContent: "center",
      padding: "32px",
      minWidth: "460px",
    }}
  >
    <Shop size={64} style={{ color: "#27CA40" }} />
  </div>
</div>

## What is Apollo Storefront?

<Typography
  level="bodyLarge"
  style={{
    color: "var(--sb-secondary-text-color)",
    marginBottom: 32,
  }}
>
  Apollo Storefront is a specialized component library designed for building modern website experiences, particularly optimized for mobile development. Built on top of <code style={{ background: "rgb(247, 250, 252)", color: "rgba(46, 52, 56, 0.9)", padding: "3px 5px", borderRadius: 3 }}>@apollo/ui</code>, it provides a curated set of components specifically tailored for storefront interfaces, product displays, and shopping experiences.
</Typography>

## Key Features

<div style={{ marginBottom: 32 }}>
  <ul style={{
    listStyle: "none",
    padding: 0,
    display: "grid",
    gap: 16,
    gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))"
  }}>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Mobile size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Web-First Design</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Optimized for website shopping experiences with touch-friendly interactions
        </span>
      </div>
    </li>
    <li style={{
      display: "flex",
      alignItems: "flex-start",
      gap: 12,
      padding: 16,
      borderRadius: 8,
      border: "1px solid var(--sb-ui-border-color)",
    }}>
      <Star size={24} style={{ color: "#27CA40", flexShrink: 0, marginTop: 2 }} />
      <div>
        <strong>Design System Integration</strong>
        <br />
        <span style={{ color: "var(--sb-secondary-text-color)" }}>
          Seamlessly integrates with Apollo design tokens and theming system
        </span>
      </div>
    </li>
  </ul>
</div>
