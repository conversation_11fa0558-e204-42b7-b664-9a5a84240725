import React, { useState } from "react"
import { ComponentRules, UsageGuidelines } from "@/components"
import { CategoryMenuItem } from "@apollo/storefront"
import { Typography } from "@apollo/ui"
import {
  Gift,
  Shop,
  Shopping,
  Tag,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * CategoryMenuItem component
 *
 * The CategoryMenuItem component displays a clickable menu item with an image or icon
 * and a label. It's designed for category navigation in e-commerce and storefront
 * applications. The component supports both images and SVG icons, with automatic
 * text truncation for consistent layout.
 *
 * Notes:
 * - Supports both images (via imageSrc/imageAlt) and SVG icons (via icon prop);
 * - Automatic text truncation with line-clamp for consistent layout;
 * - Hover states and disabled states for better user experience;
 * - Flexible sizing with min/max width constraints;
 * - Built with accessibility in mind with proper alt text support.
 */
const meta = {
  title: "@apollo∕storefront/Components/Navigation/CategoryMenuItem",
  component: CategoryMenuItem,
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=6096-7954&m=dev",
    },
    docs: {
      description: {
        component:
          "The CategoryMenuItem component displays a clickable menu item with an image or icon and a label. Perfect for category navigation in e-commerce applications with support for images, icons, and automatic text truncation.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { CategoryMenuItem } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="categorymenuitem-props">Props</h2>
          <ArgTypes />
          <h2 id="categorymenuitem-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use CategoryMenuItem for: Category navigation, product type selection, and menu organization in customer website applications",
              "Keep labels short and descriptive, as text is not truncated",
              "Use high-quality, consistent image sizes (recommended: 32x32px) for better visual alignment",
              "Provide meaningful alt text for images to ensure accessibility for screen readers",
              "Use SVG icons for scalable, crisp graphics that work well at different sizes",
              "Consider the disabled state for categories that are temporarily unavailable",
              "Use consistent visual style (all images or all icons) within the same menu group",
            ]}
          />
          <h2 id="categorymenuitem-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive <code>imageAlt</code> prop for
                images to ensure their purpose is clear to screen reader users.
              </>,
              <>
                When using SVG icons, assign SVG in <code>icon</code> prop.
              </>,
              <>
                Use the <code>disabled</code> prop to disable menu items that
                are not currently actionable, ensuring they are not focusable.
              </>,
              <>Use <code>maxLines</code> prop to control text truncation.</>,
              <>
                Ensure labels are descriptive and meaningful, as the component
                will automatically truncate long text for visual consistency.
              </>,
            ]}
          />
          <h2 id="categorymenuitem-examples">Examples</h2>
          <Stories title="" />
          <h2 id="categorymenuitem-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
                        imageAlt="Electronics category"
                        label="Electronics"
                      />
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
                        imageAlt="Fashion category"
                        label="Fashion"
                      />
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
                        imageAlt="Home category"
                        label="Home"
                      />
                    </div>
                  ),
                  description:
                    "Use high-quality, consistent images with descriptive alt text and clear, concise labels",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=20&h=40&fit=crop"
                        imageAlt=""
                        label="Category 1 with very long text that will be truncated"
                      />
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=50&h=20&fit=crop"
                        imageAlt=""
                        label="Cat 2"
                      />
                    </div>
                  ),
                  description:
                    "Avoid inconsistent image sizes, missing alt text, and generic labels like 'Category 1'",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        icon={<Shop size={24} />}
                        label="Shop"
                      />
                      <CategoryMenuItem
                        icon={<Shopping size={24} />}
                        label="Orders"
                      />
                      <CategoryMenuItem
                        icon={<Gift size={24} />}
                        label="Gifts"
                      />
                    </div>
                  ),
                  description:
                    "Use consistent, well-designed SVG icons for scalable graphics",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
                        imageAlt="Mixed"
                        label="Image"
                      />
                      <CategoryMenuItem
                        icon={
                          <div
                            style={{
                              width: 20,
                              height: 20,
                              background: "#ccc",
                            }}
                          >
                            ?
                          </div>
                        }
                        label="Icon"
                      />
                    </div>
                  ),
                  description:
                    "Avoid mixing different visual styles (photos and placeholder graphics) within the same menu group",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    imageSrc: {
      control: { type: "text" },
      description: "Image source URL for the category icon.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    imageSrcSet: {
      control: { type: "text" },
      description: "Image srcSet for responsive images.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    imageAlt: {
      control: { type: "text" },
      description:
        "Alternative text for the image (required when using imageSrc).",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    icon: {
      control: false,
      description: "SVG icon or React element to display instead of an image.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    label: {
      control: { type: "text" },
      description:
        "Text label for the category (automatically truncated to 2 lines).",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the menu item is disabled.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    maxLines: {
      control: { type: "number", min: 1, max: 5, step: 1 },
      description:
        "Maximum number of lines for the label text (controls text truncation).",
      table: {
        type: { summary: "number" },
        defaultValue: { summary: "-" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
  },
  args: {
    label: "Category",
  },
} satisfies Meta<typeof CategoryMenuItem>

export default meta

type Story = StoryObj<typeof CategoryMenuItem>

/** Default CategoryMenuItem (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview CategoryMenuItem with default settings. Shows a category menu item with an image and label, demonstrating the basic functionality.",
      },
    },
  },
  args: {
    imageSrc:
      "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop",
    imageAlt: "Electronics category",
    label: "Electronics",
  },
}

/** CategoryMenuItem with different content types and variations */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A comprehensive showcase of CategoryMenuItem variants including images, icons, text truncation, and disabled states.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 32,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            With Images
          </Typography>
          <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
              imageAlt="Electronics"
              label="Electronics"
            />
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
              imageAlt="Fashion"
              label="Fashion"
            />
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
              imageAlt="Home"
              label="Home & Garden"
            />
          </div>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            With Icons
          </Typography>
          <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
            <CategoryMenuItem icon={<Shop size={24} />} label="Shop" />
            <CategoryMenuItem icon={<Shopping size={24} />} label="Orders" />
            <CategoryMenuItem icon={<Gift size={24} />} label="Gifts" />
          </div>
        </div>
      </div>
    )
  },
}

/** CategoryMenuItem with disabled state demonstration */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem in disabled state. Disabled items are not clickable and have reduced opacity to indicate they are not available.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 24,
        alignItems: "flex-start",
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Normal vs Disabled States
        </Typography>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
            imageAlt="Electronics"
            label="Electronics"
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
            imageAlt="Electronics"
            label="Electronics"
            disabled
          />
        </div>
        <Typography level="bodySmall" style={{ color: "#6b7280" }}>
          Left: Normal state, Right: Disabled state
        </Typography>
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Disabled with Icons
        </Typography>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <CategoryMenuItem icon={<Shop size={24} />} label="Shop" />
          <CategoryMenuItem icon={<Shop size={24} />} label="Shop" disabled />
          <CategoryMenuItem icon={<Gift size={24} />} label="Gifts" disabled />
        </div>
        <Typography level="bodySmall" style={{ color: "#6b7280" }}>
          Disabled items maintain visual hierarchy while indicating
          unavailability
        </Typography>
      </div>
    </div>
  ),
}

/** CategoryMenuItem with different maxLines configurations */
export const MaxLines: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem with different maxLines settings to control text truncation. Useful for handling varying label lengths while maintaining consistent layout.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 24,
        alignItems: "flex-start",
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Two Lines (maxLines: 2)
        </Typography>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
            imageAlt="Electronics"
            label="Electronics & Technology"
            maxLines={2}
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
            imageAlt="Fashion"
            label="Fashion & Accessories"
            maxLines={2}
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
            imageAlt="Home"
            label="Home & Garden Supplies"
            maxLines={2}
          />
        </div>
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Three Lines (maxLines: 3)
        </Typography>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
            imageAlt="Electronics"
            label="Electronics & Technology Gadgets"
            maxLines={3}
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
            imageAlt="Fashion"
            label="Fashion & Accessories for All Seasons"
            maxLines={3}
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
            imageAlt="Home"
            label="Home & Garden Supplies and Decor"
            maxLines={3}
          />
        </div>
        <Typography level="bodySmall" style={{ color: "#6b7280" }}>
          Higher maxLines values allow more text but may affect layout
          consistency
        </Typography>
      </div>
    </div>
  ),
}

/** CategoryMenuItem with click handlers and interactive behavior */
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem with click handlers demonstrating interactive behavior. Click on any item to see the action in the console.",
      },
    },
  },
  render: () => {
    const [clickedCategory, setClickedCategory] = useState<string | null>(null)
    const handleCategoryClick = (category: string) => {
      console.log(`Clicked on ${category} category`)
      setClickedCategory(category)
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 16,
        }}
      >
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Interactive Category Menu
        </Typography>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <CategoryMenuItem
            as="button"
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
            imageAlt="Electronics"
            label="Electronics"
            onClick={() => handleCategoryClick("Electronics")}
            style={{ cursor: "pointer" }}
          />
          <CategoryMenuItem
            as="button"
            imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
            imageAlt="Fashion"
            label="Fashion"
            onClick={() => handleCategoryClick("Fashion")}
            style={{ cursor: "pointer" }}
          />
          <CategoryMenuItem
            as="button"
            imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
            imageAlt="Home"
            label="Home & Garden"
            onClick={() => handleCategoryClick("Home & Garden")}
            style={{ cursor: "pointer" }}
          />
        </div>
        <Typography level="bodyMedium" color="primary">
          Clicked Category: {clickedCategory || "None"}
        </Typography>
      </div>
    )
  },
}

/** CategoryMenuItem in a list layout for mobile-friendly display */
export const ListLayout: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem arranged in a responsive list layout, perfect for mobile category browsing with multiple items.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        flexWrap: "wrap",
        gap: 16,
        alignItems: "flex-start",
        alignContent: "flex-start",
        flexShink: 0,
        padding: "0 8px",
      }}
    >
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
        imageAlt="Electronics"
        label="Electronics"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=60&h=60&fit=crop"
        imageAlt="Beverages"
        label="Beverages"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=60&h=60&fit=crop"
        imageAlt="Snacks"
        label="Snacks"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
        imageAlt="Health"
        label="Health Care"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=60&h=60&fit=crop"
        imageAlt="Home"
        label="Home Goods"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=60&h=60&fit=crop"
        imageAlt="Pets"
        label="Pet Supplies"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=60&h=60&fit=crop"
        imageAlt="Electronics"
        label="Electronics"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
        imageAlt="Fashion"
        label="Fashion"
      />
    </div>
  ),
}
