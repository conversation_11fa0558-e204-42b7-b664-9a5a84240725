import React, { useState } from "react"
import { UsageGuidelines } from "@/components"
import { BottomSheet, BottomSheetContent } from "@apollo/storefront"
import { Button, Input, Typography } from "@apollo/ui"
import {
  Heart,
  Shopping,
  Star,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * BottomSheetContent component
 *
 * The BottomSheetContent component provides a structured layout for content within
 * a BottomSheet. It handles the content area with optional footer, ensuring proper
 * scrolling behavior and consistent spacing. The content area is scrollable while
 * the footer remains sticky at the bottom.
 *
 * Notes:
 * - Automatically handles scrolling for content that exceeds the available space
 * - Optional footer that remains sticky at the bottom
 * - Consistent padding and spacing throughout the content area
 * - Optimized for mobile touch scrolling with -webkit-overflow-scrolling
 * - Flexible layout that adapts to different content types
 */
const meta = {
  title: "@apollo∕storefront/Components/Data Display/BottomSheetContent",
  component: BottomSheetContent,
  tags: ["autodocs", "new"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=6076-13562&m=dev",
    },
    docs: {
      description: {
        component:
          "The BottomSheetContent component provides a structured layout for content within a BottomSheet, with scrollable content area and optional sticky footer.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { BottomSheetContent } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="bottomsheetcontent-props">Props</h2>
          <ArgTypes />
          <h2 id="bottomsheetcontent-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use BottomSheetContent inside BottomSheet for consistent content layout and spacing",
              "Provide footer prop for actions that should remain visible while scrolling",
              "Content area automatically handles overflow with smooth scrolling behavior",
              "Use proper semantic HTML structure within children for accessibility",
              "Keep footer content concise - typically 1-2 action buttons work best",
              "Consider content hierarchy when designing the layout within children",
            ]}
          />
          <h2 id="bottomsheetcontent-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Use proper heading hierarchy within the content area for screen
                reader navigation.
              </>,
              <>
                Ensure interactive elements in the footer have appropriate focus
                management and keyboard navigation.
              </>,
              <>
                The component maintains proper scrolling behavior for assistive
                technologies with touch scrolling optimization.
              </>,
              <>
                Use semantic HTML elements within children for better screen
                reader understanding.
              </>,
              <>
                Consider the reading order when placing content - footer actions
                should logically follow the main content.
              </>,
            ]}
          />
          <h2 id="bottomsheetcontent-examples">Examples</h2>
          <Stories title="" />
        </>
      ),
    },
  },
  argTypes: {
    children: {
      control: false,
      description: "The main content to display in the scrollable area.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    footer: {
      control: false,
      description: "Optional footer content that remains sticky at the bottom.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
  },
  args: {},
} satisfies Meta<typeof BottomSheetContent>

export default meta

type Story = StoryObj<typeof BottomSheetContent>

/** Default BottomSheetContent (demonstrates basic content layout) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Basic BottomSheetContent with simple content. Shows how the component handles content layout and spacing within a BottomSheet.",
      },
    },
  },
  render: function OverviewDemo(args) {
    const [isOpen, setIsOpen] = useState(false)

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Open Content Example</Button>
        
        <BottomSheet
          open={isOpen}
          onOpenChange={setIsOpen}
          onClose={() => setIsOpen(false)}
          title="Content Layout"
          snapPoints={[40, 70]}
          defaultSnap={70}
        >
          <BottomSheetContent {...args}>
            <div style={{ padding: "16px 0" }}>
              <Typography level="titleLarge" style={{ marginBottom: 16 }}>
                Welcome to BottomSheetContent
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                This component provides a structured layout for content within a BottomSheet.
                The content area is scrollable and maintains consistent spacing.
              </Typography>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                You can include any type of content here - text, forms, lists, images, or
                interactive elements. The layout automatically adapts to your content.
              </Typography>
              <Typography level="bodyMedium">
                The component ensures proper scrolling behavior and maintains accessibility
                standards for mobile and desktop experiences.
              </Typography>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}

/** BottomSheetContent with footer for actions */
export const WithFooter: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with a sticky footer containing action buttons. The footer remains visible while the content area scrolls.",
      },
    },
  },
  render: function WithFooterDemo() {
    const [isOpen, setIsOpen] = useState(false)
    const [formData, setFormData] = useState({ name: "", email: "", message: "" })

    const handleSubmit = () => {
      console.log("Form submitted:", formData)
      setIsOpen(false)
      setFormData({ name: "", email: "", message: "" })
    }

    const footer = (
      <div style={{ display: "flex", gap: 12, padding: "16px 0" }}>
        <Button
          variant="outline"
          onClick={() => setIsOpen(false)}
          style={{ flex: 1 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          style={{ flex: 1 }}
          disabled={!formData.name || !formData.email}
        >
          Submit
        </Button>
      </div>
    )

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Open Form with Footer</Button>
        
        <BottomSheet
          open={isOpen}
          onOpenChange={setIsOpen}
          onClose={() => setIsOpen(false)}
          title="Contact Form"
          snapPoints={[50, 80]}
          defaultSnap={80}
        >
          <BottomSheetContent footer={footer}>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyMedium" style={{ marginBottom: 24 }}>
                Fill out the form below to get in touch with us. All fields marked with * are required.
              </Typography>
              
              <div style={{ display: "flex", flexDirection: "column", gap: 16 }}>
                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Name *
                  </Typography>
                  <Input
                    placeholder="Enter your full name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    fullWidth
                  />
                </div>
                
                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Email *
                  </Typography>
                  <Input
                    type="email"
                    placeholder="Enter your email address"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    fullWidth
                  />
                </div>
                
                <div>
                  <Typography level="bodyMedium" style={{ marginBottom: 8, fontWeight: 600 }}>
                    Message
                  </Typography>
                  <textarea
                    placeholder="Enter your message (optional)"
                    value={formData.message}
                    onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                    style={{
                      width: "100%",
                      minHeight: 100,
                      padding: 12,
                      border: "1px solid #ccc",
                      borderRadius: 8,
                      fontFamily: "inherit",
                      fontSize: "inherit",
                      resize: "vertical",
                    }}
                  />
                </div>
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}

/** BottomSheetContent with scrollable content */
export const ScrollableContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent with long scrollable content. Demonstrates how the component handles overflow and maintains smooth scrolling behavior.",
      },
    },
  },
  render: function ScrollableContentDemo() {
    const [isOpen, setIsOpen] = useState(false)

    const getIcon = (index: number) => {
      if (index % 3 === 0) return <Star width={20} height={20} />
      if (index % 3 === 1) return <Heart width={20} height={20} />
      return <Shopping width={20} height={20} />
    }

    const items = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      title: `Item ${i + 1}`,
      description: `This is the description for item ${i + 1}. It contains some sample text to demonstrate content layout.`,
      icon: getIcon(i),
    }))

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Open Scrollable Content</Button>

        <BottomSheet
          open={isOpen}
          onOpenChange={setIsOpen}
          onClose={() => setIsOpen(false)}
          title="Scrollable List"
          snapPoints={[30, 60, 90]}
          defaultSnap={60}
        >
          <BottomSheetContent>
            <div style={{ padding: "16px 0" }}>
              <Typography level="bodyMedium" style={{ marginBottom: 16 }}>
                This list contains many items to demonstrate scrolling behavior.
                Try resizing the sheet to see how the content adapts.
              </Typography>

              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                {items.map((item) => (
                  <div
                    key={item.id}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: 12,
                      padding: 16,
                      border: "1px solid #eee",
                      borderRadius: 8,
                      backgroundColor: "#fafafa",
                    }}
                  >
                    <div style={{ color: "var(--apl-alias-color-primary-primary)" }}>
                      {item.icon}
                    </div>
                    <div style={{ flex: 1 }}>
                      <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 4 }}>
                        {item.title}
                      </Typography>
                      <Typography level="bodySmall" style={{ color: "#666" }}>
                        {item.description}
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}

/** BottomSheetContent with mixed content types */
export const MixedContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "BottomSheetContent showcasing various content types including text, images, forms, and interactive elements all within a single layout.",
      },
    },
  },
  render: function MixedContentDemo() {
    const [isOpen, setIsOpen] = useState(false)
    const [rating, setRating] = useState(0)
    const [selectedSize, setSelectedSize] = useState("M")

    const sizes = ["XS", "S", "M", "L", "XL"]

    const footer = (
      <div style={{ display: "flex", gap: 12, padding: "16px 0" }}>
        <Button
          variant="outline"
          onClick={() => setIsOpen(false)}
          style={{ flex: 1 }}
        >
          Close
        </Button>
        <Button
          onClick={() => {
            console.log("Added to cart:", { rating, selectedSize })
            setIsOpen(false)
          }}
          style={{ flex: 1 }}
        >
          Add to Cart
        </Button>
      </div>
    )

    return (
      <div style={{ padding: 20, minHeight: "100vh" }}>
        <Button onClick={() => setIsOpen(true)}>Open Product Details</Button>

        <BottomSheet
          open={isOpen}
          onOpenChange={setIsOpen}
          onClose={() => setIsOpen(false)}
          title="Product Details"
          snapPoints={[40, 70, 95]}
          defaultSnap={70}
        >
          <BottomSheetContent footer={footer}>
            <div style={{ padding: "16px 0" }}>
              {/* Product Image Placeholder */}
              <div
                style={{
                  width: "100%",
                  height: 200,
                  backgroundColor: "#f0f0f0",
                  borderRadius: 8,
                  marginBottom: 24,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  border: "2px dashed #ccc",
                }}
              >
                <Typography level="bodyMedium" style={{ color: "#666" }}>
                  Product Image Placeholder
                </Typography>
              </div>

              {/* Product Info */}
              <div style={{ marginBottom: 24 }}>
                <Typography level="titleLarge" style={{ marginBottom: 8 }}>
                  Premium Cotton T-Shirt
                </Typography>
                <Typography level="bodyLarge" style={{ color: "var(--apl-alias-color-primary-primary)", fontWeight: 600, marginBottom: 16 }}>
                  $29.99
                </Typography>
                <Typography level="bodyMedium" style={{ lineHeight: 1.6, marginBottom: 16 }}>
                  Made from 100% organic cotton, this comfortable t-shirt features a classic fit
                  and premium quality construction. Perfect for everyday wear with a soft,
                  breathable fabric that gets better with every wash.
                </Typography>
              </div>

              {/* Size Selection */}
              <div style={{ marginBottom: 24 }}>
                <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 12 }}>
                  Size
                </Typography>
                <div style={{ display: "flex", gap: 8 }}>
                  {sizes.map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      style={{
                        padding: "8px 16px",
                        border: `2px solid ${selectedSize === size ? "var(--apl-alias-color-primary-primary)" : "#ccc"}`,
                        borderRadius: 6,
                        backgroundColor: selectedSize === size ? "var(--apl-alias-color-primary-primary-container)" : "transparent",
                        cursor: "pointer",
                        fontWeight: selectedSize === size ? 600 : 400,
                      }}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              {/* Rating */}
              <div style={{ marginBottom: 24 }}>
                <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 12 }}>
                  Rate this product
                </Typography>
                <div style={{ display: "flex", gap: 4 }}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() => setRating(star)}
                      style={{
                        background: "none",
                        border: "none",
                        cursor: "pointer",
                        padding: 4,
                      }}
                    >
                      <Star
                        width={24}
                        height={24}
                        style={{
                          color: star <= rating ? "var(--apl-alias-color-warning-warning)" : "#ccc",
                        }}
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Features List */}
              <div>
                <Typography level="bodyMedium" style={{ fontWeight: 600, marginBottom: 12 }}>
                  Features
                </Typography>
                <ul style={{ paddingLeft: 20, margin: 0 }}>
                  <li style={{ marginBottom: 8 }}>
                    <Typography level="bodyMedium">100% organic cotton</Typography>
                  </li>
                  <li style={{ marginBottom: 8 }}>
                    <Typography level="bodyMedium">Machine washable</Typography>
                  </li>
                  <li style={{ marginBottom: 8 }}>
                    <Typography level="bodyMedium">Classic fit design</Typography>
                  </li>
                  <li>
                    <Typography level="bodyMedium">Available in multiple colors</Typography>
                  </li>
                </ul>
              </div>
            </div>
          </BottomSheetContent>
        </BottomSheet>
      </div>
    )
  },
}
