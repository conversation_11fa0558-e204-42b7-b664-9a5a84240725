import React, { useState } from "react"
import { Button, Input, Typography, createTheme, ThemeProvider } from "@apollo/ui/legacy"
import { Meta } from "@storybook/addon-docs/blocks"

import CautionCard from "../../../components/caution-card/CautionCard"

<Meta title="@design-systems∕apollo-ui/Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="h1"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="h4"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Get started with the legacy Apollo Design System components!</Typography>
</div>

<CautionCard>
This is the legacy version of Apollo Design System. For new projects, consider using <code>@apollo/ui</code> instead.
</CautionCard>

## Installation

To install the `@apollo/ui/legacy` package, you need to configure the `.npmrc` file (create this file in the project root) to point to our custom private registry on GitLab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

<CautionCard>
If you're outside CJ network, VPN is needed to install this package.
</CautionCard>

```bash
# with pnpm (recommended)
pnpm add @apollo/ui

# with yarn
yarn add @apollo/ui

# with npm
npm install @apollo/ui
```

## Tailwind Configuration

Apollo UI legacy components are built with TailwindCSS. You need to configure Tailwind to include the Apollo preset:

```js title="tailwind.config.js"
const { withApollo } = require("@apollo/ui/legacy")
 
// any additional Tailwind's config
// can be passed as an argument
// and it'll be merged to UI library's preset
module.exports = withApollo({
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}", // depends on the setup/folder structure
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
})
```

## Theme Setup

Apollo legacy components require a theme provider to work correctly. Wrap your app with the `ThemeProvider` and create a theme:

```jsx title="App.js"
import React from 'react';
import { ThemeProvider, createTheme } from "@apollo/ui/legacy"

const appTheme = createTheme()

function App({ children }) {
  return (
    <ThemeProvider theme={appTheme}>
      {children}
    </ThemeProvider>
  )
}
```

## Basic Usage

You can import and use components directly:

```jsx title="Button Example"
import React from 'react'
import { Button, ThemeProvider, createTheme } from "@apollo/ui/legacy"

const theme = createTheme()

export default function Example() {
  return (
    <ThemeProvider theme={theme}>
      <Button variant="filled">Hello World</Button>
    </ThemeProvider>
  )
}
```