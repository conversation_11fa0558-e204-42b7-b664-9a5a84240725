{"name": "@apollo/storefront", "version": "1.0.0-beta.3", "type": "module", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "require": "./dist/index.umd.cjs"}, "./style.css": "./dist/index.css"}, "scripts": {"build:main": "TARGET_LIB=main vite build", "build": "tsc -b && pnpm run build:main", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "publishConfig": {"@apollo:registry": "https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "dependencies": {"@apollo/core": "workspace:*", "@apollo/token": "workspace:*", "@apollo/ui": "workspace:*", "class-variance-authority": "0.7.1", "classnames": "2.5.1", "date-fns": "2.30.0", "react-compiler-runtime": "19.0.0-beta-714736e-20250131", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "9.17.0", "@figma/code-connect": "^1.3.1", "@modelcontextprotocol/sdk": "^1.12.1", "@testing-library/jest-dom": "^6.6.3", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@vitejs/plugin-react": "4.3.4", "@vitejs/plugin-react-swc": "3.5.0", "@vitest/browser": "^3.0.6", "babel-plugin-react-compiler": "19.0.0-beta-714736e-20250131", "chalk": "^5.4.1", "commander": "^14.0.0", "eslint": "9.17.0", "eslint-plugin-react-compiler": "19.0.0-beta-714736e-20250131", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-refresh": "0.4.16", "globals": "15.14.0", "ora": "^8.2.0", "playwright": "^1.50.1", "typescript": "5.6.2", "typescript-eslint": "8.18.2", "vite": "6.0.5", "vite-plugin-dts": "4.5.0", "vitest": "^3.0.6", "vitest-browser-react": "^0.1.1"}}