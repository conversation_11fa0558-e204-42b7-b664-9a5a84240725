.bottomSheetContentRoot {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--apl-alias-spacing-gap-gap3, 4px);
    align-self: stretch;
    width: 100%;
    height: 100%;
    min-height: 0; /* allow inner flex child to shrink and scroll */
}

.bottomSheetContent {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    min-height: 0; /* ensure container can actually scroll in flex layout */
}

.bottomSheetFooter {
    width: 100%;
    position: sticky;
    bottom: 0;
    background: inherit;
    z-index: 1;
}
