/* Tab Root Styles */
.tabWithImageRoot {
    --apl-tabs-item-border-bottom-color: transparent;
    --apl-tabs-item-padding: 0;
}

/* Tab List Styles - Scrollable */
.tabWithImageList {
    display: flex;
    align-items: stretch;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    position: relative;
    flex-wrap: nowrap;
    border-bottom: none !important;

    &::-webkit-scrollbar {
        display: none;
    }
}

/* Tab Item Styles */
.tabWithImageItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start !important;
    flex-shrink: 0;
    min-width: 60px;
    max-width: 440px;
    min-height: 42px;
    border-bottom: none;
}

/* Tab Indicator Styles */
.tabWithImageIndicator {
    height: 3px;
    width: var(--active-tab-width) !important;
    left: var(--active-tab-left) !important;
}

.tabWithImageCategoryMenuItem {
    width: 100%;
    color: inherit;
     gap: var(--apl-alias-spacing-gap-gap3, 4px);
    &:hover {
        border-radius: none;
    }

}