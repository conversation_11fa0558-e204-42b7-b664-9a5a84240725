.categoryMenuItemRoot {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start !important;
    gap: var(--apl-alias-spacing-gap-gap6, 10px);
    padding: var(--apl-alias-spacing-padding-padding5, 8px) var(--apl-alias-spacing-padding-padding3, 4px);
    flex-shrink: 0;
    min-width: 60px;
    min-height: 42px;
    border-bottom: none;
    color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

    &:not([data-disabled]):hover {
        background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        border-radius: var(--apl-alias-radius-radius4, 8px);
    }

    &[data-disabled] {
        color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }
}

.categoryMenuItemImageRoot {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 32px;
    height: 32px;
}

.categoryMenuItemImage {
    object-fit: cover;
    border-radius: var(--apl-alias-radius-radius2, 4px);
    display: block;

    &[data-disabled] {
        opacity: 0.5;
    }
}

.categoryMenuItemLabel {
    text-align: center;
    color: inherit;
    width: 100%;
    max-width: 68px;
}

.categoryMenuItemLabelMaxLines {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}