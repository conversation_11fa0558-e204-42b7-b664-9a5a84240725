import { Typography } from "@apollo/ui"
import classNames from "classnames"

import styles from "./category-menu-item.module.css"
import type { CategoryMenuItemProps } from "./CategoryMenuItemProps"

export const CategoryMenuItem = ({
  imageSrc,
  imageSrcSet,
  imageAlt,
  icon,
  label,
  maxLines,
  disabled,
  className,
  ...props
}: CategoryMenuItemProps) => {
  return (
    <div
      {...props}
      className={classNames(
        "ApolloCategoryMenuItem-root",
        styles.categoryMenuItemRoot,
        className
      )}
      data-disabled={disabled}
    >
      <div
        className={classNames(
          "ApolloCategoryMenuItem-imageRoot",
          styles.categoryMenuItemImageRoot
        )}
        {...props}
      >
        {imageSrc && imageAlt && (
          <img
            src={imageSrc}
            srcSet={imageSrcSet}
            alt={imageAlt}
            className={classNames(
              "ApolloCategoryMenuItem-img",
              styles.categoryMenuItemImage
            )}
            data-disabled={disabled}
            // style={{ opacity: disabled ? 0.5 : 1 }}
          />
        )}
        {icon}
      </div>
      {label && (
        <Typography
          className={classNames(
            "ApolloCategoryMenuItem-label",
            styles.categoryMenuItemLabel,
            {
              [styles.categoryMenuItemLabelMaxLines]: maxLines,
            }
          )}
          level="bodySmall"
          style={{
            WebkitLineClamp: maxLines,
          }}
          align="center"
        >
          {label}
        </Typography>
      )}
    </div>
  )
}
