// Custom plugin to generate theme.js with CSS as a string

import fs from "fs"
import path from "path"

export const generateCssStringModule = () => {
  return {
    name: "generate-css-string-module",
    writeBundle: {
      sequential: true,
      order: "post",
      handler: () => {
        const cssPath = path.resolve(__dirname, "dist/theme.css")
        if (fs.existsSync(cssPath)) {
          const cssContent = fs.readFileSync(cssPath, "utf8")
          const jsContent = `export const cssString = ${JSON.stringify(cssContent)};\n`

          fs.writeFileSync(path.resolve(__dirname, "dist/theme.js"), jsContent)

          // Also create CJS version
          fs.writeFileSync(
            path.resolve(__dirname, "dist/theme.cjs"),
            `"use strict";\nObject.defineProperty(exports, "__esModule", { value: true });\nexports.cssString = ${JSON.stringify(cssContent)};\n`
          )

          console.log(
            "✅ Generated theme.js and theme.cjs with CSS content as string"
          )
        } else {
          console.error("❌ Could not find theme.css file to generate theme.js")
        }
      },
    },
  }
}
