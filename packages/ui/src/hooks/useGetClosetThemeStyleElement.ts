export function useGetClosetThemeStyleElement(
  ref: Element | React.RefObject<Element> | null = null,
  enabled = true
): Element | null {
  if (typeof document === "undefined" || !enabled) return null

  // Start from the current modal element or document body
  let currentElement: Element | null = null

  // Handle ref properly - check if it's a ref object or callback ref
  if (ref && typeof ref === "object" && "current" in ref) {
    currentElement = ref.current
  }

  // Fallback to document body if no ref element
  if (!currentElement) {
    currentElement = document.body
  }

  // Traverse up the DOM tree to find the closest theme element
  while (currentElement && currentElement !== document.documentElement) {
    // Check all attributes of the current element
    if (currentElement.hasAttributes()) {
      const attributes = Array.from(currentElement.attributes)

      // Look for data-cjx attribute ending with '-theme'
      const themeAttribute = attributes.find(
        (attr: Attr) =>
          attr.name === "data-cjx" && attr.value.endsWith("-theme")
      )

      const isScope = attributes.find(
        (attr: Attr) => attr.name === "data-cjxisscope" && attr.value === "true"
      )

      if (themeAttribute && isScope) {
        return currentElement
      }
    }

    currentElement = currentElement.parentElement
  }

  // Fallback: look for any theme style element in the document
  const themeStyleElement = document.querySelector('style[data-cjx$="-theme"]')
  return themeStyleElement
}
