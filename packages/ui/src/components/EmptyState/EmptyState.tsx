import { useMemo, type <PERSON>actNode } from "react"
import { mergeClass } from "@/utils"
import {
  EmptyState as CoreEmptyState,
  type EmptyStateProps as CoreEmptyStateProps,
} from "@apollo/core"

import { isTextElement } from "@/utils/helpers"

import { Typography } from "../Typography"
import type { EmptyStateProps } from "./EmptyStateProps"

export function EmptyState(props: EmptyStateProps) {
  const titleSlot = useMemo(
    () =>
      ({
        render: ({ title, className }) =>
          title ? (
            <Typography
              align="center"
              className={mergeClass(className, "text-content-default")}
              level="h2"
            >
              {title}
            </Typography>
          ) : null,
      }) as CoreEmptyStateProps["slots"]["title"],
    []
  )
  const descriptionSlot = useMemo(
    () =>
      ({
        render: ({ className, description }) =>
          description ? (
            <Typography
              align="center"
              className={mergeClass(className, "text-content-description")}
              level="body-1"
            >
              {description}
            </Typography>
          ) : null,
      }) as CoreEmptyStateProps["slots"]["description"],
    []
  )

  const containerSlot = useMemo(
    () => ({
      className:
        "flex flex-col items-center justify-center gap-4 w-full max-w-[300px]",
    }),
    []
  )

  const contentWrapperSlot = useMemo(
    () => ({
      className: "flex flex-col items-center justify-center gap-2",
    }),
    []
  )

  const illustrationSlot = useMemo(
    () => ({
      className: "w-[114px] h-[auto] text-content-placeholder",
    }),
    []
  )

  const actionWrapperSlot = useMemo(
    () =>
      ({
        className: "flex flex-col items-center justify-center gap-2 w-full",
        render: ({ children, className }) =>
          children ? (
            <div className={mergeClass([className, "w-full flex-col"])}>
              {children as ReactNode}
            </div>
          ) : null,
      }) as CoreEmptyStateProps["slots"]["actionWrapper"],
    []
  )

  const errorMessageSlot = useMemo(
    () =>
      ({
        render: ({ className, errorMessage }) =>
          errorMessage ? (
            isTextElement(errorMessage) ? (
              <Typography
                align="center"
                className={mergeClass(className, "text-content-danger-default")}
                level="h3"
              >
                {errorMessage}
              </Typography>
            ) : (
              errorMessage
            )
          ) : null,
      }) as CoreEmptyStateProps["slots"]["errorMessage"],
    []
  )

  return (
    <CoreEmptyState
      {...props}
      slots={{
        title: titleSlot,
        container: containerSlot,
        description: descriptionSlot,
        illustration: illustrationSlot,
        contentWrapper: contentWrapperSlot,
        actionWrapper: actionWrapperSlot,
        errorMessage: errorMessageSlot,
        ...props?.slots,
      }}
    />
  )
}
