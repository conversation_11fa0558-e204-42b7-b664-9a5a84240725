#!/usr/bin/env node

/**
 * Transform Figma JSON tokens to Tailwind config format
 *
 * This script processes JSON files from the figma folder and generates
 * Tailwind-compatible TypeScript configuration files.
 *
 * Usage: node figma-to-tailwind.js
 *
 * For each subfolder in figma/:
 * - Combines global.json (base tokens) and system.json (alias tokens)
 * - Generates a corresponding .tailwind.ts file in the output directory
 */

const fs = require("fs")
const path = require("path")

// Configuration
const FIGMA_DIR = path.join(__dirname, "../figma")
const OUTPUT_DIR = path.join(
  __dirname,
  "../src/generated-files/tailwind-preset"
)

// Ensure output directory exists
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true })
}

/**
 * Sanitize token names for CSS/Tailwind usage
 */
function sanitizeTokenName(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\-\/]/g, "-")
    .replace(/\/+/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "")
}

/**
 * Convert token name to CSS variable format
 * Based on the actual pattern in _variables.css:
 * - Primitive: --apl-color-{name}
 * - Base: --apl-base-{category}-{name}
 * - Alias: --apl-alias-{category}-{name}
 */
function toCSSVariable(
  tokenName,
  collectionName,
  collectionType,
  prefix = "apl"
) {
  const sanitizedName = sanitizeTokenName(tokenName)

  if (collectionType === "global") {
    // For global/base tokens, determine if it's primitive or base level
    const collectionLower = collectionName.toLowerCase()

    if (collectionLower.includes("base color")) {
      // This is a primitive color token
      return `var(--${prefix}-color-${sanitizedName})`
    } else {
      // This is a base semantic token
      const category = getTokenCategory(collectionName, "color").replace(
        /s$/,
        ""
      ) // remove plural
      return `var(--${prefix}-base-${category}-${sanitizedName})`
    }
  } else {
    // For alias tokens
    const category = getTokenCategory(collectionName, "color").replace(/s$/, "") // remove plural
    return `var(--${prefix}-alias-${category}-${sanitizedName})`
  }
}

/**
 * Process token name into nested object path
 */
function getTokenPath(name, collectionName) {
  // Clean up the token name and split into segments
  let cleanName = name

  // Remove collection-specific prefixes
  const collectionLower = collectionName.toLowerCase()
  if (collectionLower.includes("alias")) {
    cleanName = cleanName.replace(
      /^(Alias Color\/|Alias Typography\/|Alias Spacing\/|Alias Radius\/|Alias Elevations?\/)/i,
      ""
    )
  }

  cleanName = cleanName.replace(/\/+/g, "/").trim()

  // Split into segments and filter out empty ones
  let segments = cleanName
    .split("/")
    .map((segment) => sanitizeTokenName(segment.trim()))
    .filter((segment) => segment.length > 0)

  // Remove 'schemes' level if it exists
  segments = segments.filter((segment) => segment !== "schemes")

  return segments
}

/**
 * Determine token category from collection name and type
 */
function getTokenCategory(collectionName, type) {
  const collectionLower = collectionName.toLowerCase()

  if (collectionLower.includes("color")) return "colors"
  if (collectionLower.includes("spacing")) return "spacing"
  if (collectionLower.includes("typography")) return "typography"
  if (collectionLower.includes("radius")) return "borderRadius"
  if (collectionLower.includes("elevation")) return "boxShadow"

  // Fallback to type-based categorization
  if (type === "color") return "colors"
  if (type === "spacing" || type === "dimension") return "spacing"
  if (type === "typography") return "typography"
  if (type === "borderRadius") return "borderRadius"
  if (type === "boxShadow") return "boxShadow"

  return "colors" // default fallback
}

/**
 * Set nested object value using path array
 */
function setNestedValue(obj, pathArray, value) {
  let current = obj

  for (let i = 0; i < pathArray.length - 1; i++) {
    const key = pathArray[i]
    if (!(key in current)) {
      current[key] = {}
    }
    current = current[key]
  }

  const finalKey = pathArray[pathArray.length - 1]
  current[finalKey] = value
}

/**
 * Process a collection of tokens
 */
function processCollection(collection, isAlias = false, projectName = "") {
  const result = {}

  if (!collection.modes || !Array.isArray(collection.modes)) {
    return result
  }

  collection.modes.forEach((mode) => {
    if (!mode.variables || !Array.isArray(mode.variables)) {
      return
    }

    mode.variables.forEach((variable) => {
      const { name, type, value, isAlias: tokenIsAlias } = variable

      if (!name || !type) {
        return
      }

      // Determine the collection type
      const collectionType = collection.name.toLowerCase().includes("alias")
        ? "alias"
        : "global"

      // Create path segments
      const pathSegments = getTokenPath(name, collection.name)

      if (pathSegments.length === 0) {
        return
      }

      // Determine the category
      const category = getTokenCategory(collection.name, type)

      // Create the CSS variable name
      const tokenPath = pathSegments.join("-")
      const cssVarName = toCSSVariable(
        tokenPath,
        collection.name,
        collectionType,
        "apl"
      )

      // Build the nested structure
      const fullPath = [collectionType, category, ...pathSegments]
      setNestedValue(result, fullPath, cssVarName)
    })
  })

  return result
}

/**
 * Process a single project (subfolder)
 */
function processProject(projectName) {
  const projectDir = path.join(FIGMA_DIR, projectName)
  const globalFile = path.join(projectDir, "global.json")
  const systemFile = path.join(projectDir, "system.json")

  console.log(`Processing project: ${projectName}`)

  let combinedTokens = {}

  // Process global.json (base tokens)
  if (fs.existsSync(globalFile)) {
    console.log(`  Reading global.json...`)
    try {
      const globalData = JSON.parse(fs.readFileSync(globalFile, "utf8"))

      if (globalData.collections && Array.isArray(globalData.collections)) {
        globalData.collections.forEach((collection) => {
          const processed = processCollection(collection, false, projectName)
          combinedTokens = mergeDeep(combinedTokens, processed)
        })
      }
    } catch (error) {
      console.error(`  Error processing global.json: ${error.message}`)
    }
  }

  // Process system.json (alias tokens)
  if (fs.existsSync(systemFile)) {
    console.log(`  Reading system.json...`)
    try {
      const systemData = JSON.parse(fs.readFileSync(systemFile, "utf8"))

      if (systemData.collections && Array.isArray(systemData.collections)) {
        systemData.collections.forEach((collection) => {
          const processed = processCollection(collection, true, projectName)
          combinedTokens = mergeDeep(combinedTokens, processed)
        })
      }
    } catch (error) {
      console.error(`  Error processing system.json: ${error.message}`)
    }
  }

  return combinedTokens
}

/**
 * Deep merge objects
 */
function mergeDeep(target, ...sources) {
  if (!sources.length) return target
  const source = sources.shift()

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        mergeDeep(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }

  return mergeDeep(target, ...sources)
}

function isObject(item) {
  return item && typeof item === "object" && !Array.isArray(item)
}

/**
 * Generate TypeScript file content
 */
function generateTailwindConfig(tokens, projectName) {
  // Create a valid JavaScript identifier for the config name
  const sanitizedProjectName = sanitizeTokenName(projectName).replace(/-/g, "")
  const configName = `${sanitizedProjectName}TailwindConfig`

  return `// NOTE: This file is auto-generated. Do not modify it.
export const ${configName} = ${JSON.stringify(tokens, null, 2)};
`
}

/**
 * Generate CSS utilities for typography variants
 */
function generateTypographyVariants(tokens, projectName) {
  // Create a valid JavaScript identifier for the variant name
  const sanitizedProjectName = sanitizeTokenName(projectName).replace(/-/g, "")
  const variantName = `${sanitizedProjectName}TypographyVariant`
  let variants = {}

  // Extract typography tokens and create CSS class variants
  if (tokens.global && tokens.global.typography) {
    Object.entries(tokens.global.typography).forEach(([key, value]) => {
      variants[`.typography-${key}`] = value
    })
  }

  if (tokens.alias && tokens.alias.typography) {
    Object.entries(tokens.alias.typography).forEach(([key, value]) => {
      variants[`.typography-${key}`] = value
    })
  }

  return `// NOTE: This file is auto-generated. Do not modify it.
export const ${variantName} = ${JSON.stringify(variants, null, 2)};
`
}

/**
 * Main execution
 */
function main() {
  console.log("Starting Figma to Tailwind transformation...")

  // Get all subdirectories in the figma folder
  const figmaSubdirs = fs
    .readdirSync(FIGMA_DIR, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => dirent.name)

  if (figmaSubdirs.length === 0) {
    console.log("No subdirectories found in figma folder.")
    return
  }

  figmaSubdirs.forEach((projectName) => {
    console.log(`\n--- Processing ${projectName} ---`)

    const tokens = processProject(projectName)

    if (Object.keys(tokens).length === 0) {
      console.log(`  No tokens found for ${projectName}`)
      return
    }

    const projectFolder = `${OUTPUT_DIR}/${projectName}`
    if (!fs.existsSync(projectFolder)) {
      fs.mkdirSync(projectFolder, { recursive: true })
    }

    // Generate main tailwind config file
    const mainConfigContent = generateTailwindConfig(tokens, projectName)
    const mainConfigFile = path.join(projectFolder, `tokens.tailwind.ts`)

    fs.writeFileSync(mainConfigFile, mainConfigContent)
    console.log(`  Generated: ${path.relative(process.cwd(), mainConfigFile)}`)

    // Generate typography variants file if typography tokens exist
    const hasTypography =
      (tokens.global && tokens.global.typography) ||
      (tokens.alias && tokens.alias.typography)

    if (hasTypography) {
      const typographyContent = generateTypographyVariants(tokens, projectName)
      const typographyFile = path.join(projectFolder, `typography-variant.ts`)

      fs.writeFileSync(typographyFile, typographyContent)
      console.log(
        `  Generated: ${path.relative(process.cwd(), typographyFile)}`
      )
    }
  })

  console.log("\nTransformation completed successfully!")
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = {
  processProject,
  generateTailwindConfig,
  generateTypographyVariants,
}
