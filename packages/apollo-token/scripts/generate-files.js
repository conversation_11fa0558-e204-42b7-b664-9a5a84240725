const fs = require("fs")
const path = require("path")

const outDist = "./src/generated-files"
const tokenPrefix = "apl"

function mapProperties(properties, lowerCaseKey = false) {
  return Object.entries(properties).reduce(
    (propertyObj, [property, config]) => {
      const isNested = !config?.name
      const capitalizeKey = lowerCaseKey ? property.toLowerCase() : property

      if (isNested) {
        return {
          ...propertyObj,
          [capitalizeKey]: mapProperties(config, property === "typography"),
        }
      }
      return {
        ...propertyObj,
        [capitalizeKey]: `var(--${config.name})`,
      }
    },
    {}
  )
}

function convertToCSSClasses(obj) {
  const result = {}

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const cssClass = `.typography-${key}`
      result[cssClass] = obj[key]
    }
  }

  return result
}

function generateTokenFile() {
  console.log("🚀 Generating token...")
  const input = "./build/tokens.json"
  const tokens = JSON.parse(fs.readFileSync(input, "utf-8"))
  const tailwindConfig = mapProperties(tokens)
  const dir = `${outDist}/tailwind`
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
  }
  // SPacing
  // Colors

  fs.writeFileSync(
    `${dir}/apollo-tokens.tailwind.ts`,
    `// NOTE: This file is auto-generated. Do not modify it.\r\nexport const apolloTailwindConfig = ${JSON.stringify(tailwindConfig)};\n`,
    "utf-8"
  )
  fs.writeFileSync(
    `${dir}/typographyVariant.ts`,
    `// NOTE: This file is auto-generated. Do not modify it.\r\nexport const typographyVariant = ${JSON.stringify(convertToCSSClasses(tailwindConfig.typography))};\n`,
    "utf-8"
  )
  console.log("✅ apollo-tokens.tailwind.ts has been generated successfully.")
  console.log("✅ typographyVariant.ts has been generated successfully.")
}

function generateThemeFile() {
  console.log("🚀 Generating CSS variable and theme files...")

  const cssFilePath = "./build/tokens.css"
  const cssContent = fs.readFileSync(cssFilePath, "utf8")

  const cssVariables = cssContent.match(/--[^:]+:\s*[^;]+/g)

  const theme = {
    typography: {},
    colors: {},
    spacing: {},
    font: {},
  }

  cssVariables.forEach((variable) => {
    const [key, value] = variable.split(":").map((item) => item.trim())

    const formattedKey = key.slice(2)
    let formattedValue = value

    if (formattedValue.startsWith("'") && formattedValue.endsWith("'")) {
      formattedValue = formattedValue.slice(1, -1)
    } else if (formattedValue.startsWith('"') && formattedValue.endsWith('"')) {
      formattedValue = formattedValue.slice(1, -1)
    }

    const colorsPrefixes = [
      `${tokenPrefix}-global-colors`,
      `${tokenPrefix}-colors`,
    ]
    const spacingPrefixes = [
      `${tokenPrefix}-global-spacing`,
      `${tokenPrefix}-space`,
    ]
    const fontPrefixes = [`${tokenPrefix}-global-font`, `${tokenPrefix}-font`]

    if (fontPrefixes.some((key) => formattedKey.startsWith(key))) {
      theme.font[formattedKey] = formattedValue
    } else if (spacingPrefixes.some((key) => formattedKey.startsWith(key))) {
      theme.spacing[formattedKey] = formattedValue
    } else if (colorsPrefixes.some((key) => formattedKey.startsWith(key))) {
      theme.colors[formattedKey] = formattedValue
    } else if (formattedKey.startsWith(`${tokenPrefix}-typography`)) {
      theme.typography[formattedKey] = formattedValue
    }
  })

  const filePath = `${outDist}/apolloTheme.ts`
  const content = `// NOTE: This file is auto-generated. Do not modify it.\r\nexport const apolloTheme = {tokens: ${JSON.stringify({ ...theme.typography, ...theme.spacing, ...theme.font })},colors: ${JSON.stringify(theme.colors)},lightColors: ${JSON.stringify(theme.colors)},darkColors: ${JSON.stringify(theme.colors)}};export type ApolloTheme = typeof apolloTheme;`
  if (!fs.existsSync(outDist)) {
    fs.mkdirSync(outDist, { recursive: true })
  }
  fs.writeFileSync(filePath, content)

  console.log("✅ apolloTheme.ts has been generated successfully.")
}

async function runTask() {
  console.log("🚀 Run pre-built script...")
  generateTokenFile()
  generateThemeFile()
  console.log("✅ All pre-built script has been run successfully.")
}

runTask()
