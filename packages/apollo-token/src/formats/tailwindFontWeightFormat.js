module.exports = function ({ dictionary, options }) {
  let fontWeightArray = dictionary.allTokens.filter((token) => {
    return token.type === "fontWeights";
  });

  const uniqueTypes = fontWeightArray
    .map((item) => item.attributes.type)
    .filter((value, index, self) => self.indexOf(value) === index);

  let fw = "module.exports = {\n";

  uniqueTypes.map((uniqueType) => {
    fw += `  ${JSON.stringify(uniqueType)}: {`;
    let hasItems = false;

    fontWeightArray.map((token) => {
      let value = "--" + token.name;
      let type = token.attributes.type;
      let item = token.attributes.item;

      if (uniqueType === type) {
        if (item !== undefined) {
          hasItems = true;
          fw += `
    ${[item]}: 'var(${value})',`;
        } else if (item === undefined) {
          hasItems = false;
          fw = fw.slice(0, -3);
          fw += `: 'var(${value})',`;
        }
      }
    });
    hasItems ? (fw += "\n  },\n") : (fw += "\n");
  });
  fw += "}\n";

  return fw;
};
