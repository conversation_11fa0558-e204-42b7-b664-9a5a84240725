module.exports = function ({ dictionary, options }) {
  let colorsArray = dictionary.allTokens.filter((token) => {
    return token.attributes.category === "core";
  });
  
  // const aliasUniqueTypesItem = colorsAliasArray.map((item) => item.attributes.item)
  // const aliasUniqueTypes =  aliasUniqueTypesItem.filter((value, index, self) => self.indexOf(value) === index);
  let colorsAliasArray = dictionary.allTokens.map((token) => {
    let value = JSON.stringify(token.value);
    if (options.outputReferences) {
      // the `dictionary` object now has `usesReference()` and
      // `getReferences()` methods. `usesReference()` will return true if
      // the value has a reference in it. `getReferences()` will return
      // an array of references to the whole tokens so that you can access
      // their names or any other attributes.
      if (dictionary.usesReference(token.original.value)) {
        const refs = dictionary.getReferences(token.original.value);
        refs.forEach(ref => {
          value = value.replace(ref.value, function() {
            return `${ref.name}`;
          });
        });
      }
    }
    return `export const ${token.name} = ${value};`
    // return token.attributes.category === "palette";
  }).join(`\n`);

  const uniqueTypes = colorsArray
    .map((item) => item.attributes.item)
    .filter((value, index, self) => self.indexOf(value) === index);

  let colors = "module.exports = {\n";

  uniqueTypes.map((uniqueType) => {
    colors += `  ${JSON.stringify(uniqueType)}: {`;
    let hasItems = false;

    colorsArray.map((token) => {
      // let value = "--" + token.name;
      // let tokenValue = `'var(${value})'`
      // let type = token.attributes.type;
      let value = token.value;
      let tokenValue = `"${value}"`
      let item = token.attributes.item;

      if (uniqueType === item) {
        if (item !== undefined) {
          hasItems = true;
          colors += `
    ${[token.attributes.subitem]}: ${tokenValue},`;
        } else if (item === undefined) {
          hasItems = false;
          colors = colors.slice(0, -3);
          colors += `: ${tokenValue},`;
        }
      }
    });
    hasItems ? (colors += "\n  },\n") : (colors += "\n");
  });
  colors += "}\n";

  return colors;
};
