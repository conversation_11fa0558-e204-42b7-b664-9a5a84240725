{"global": {"typography": {"display1": {"fontFamily": {"value": "var(--apl-font-font-family-display1)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-display1)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-display1)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-display1)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-display1)", "type": "textDecoration"}}, "display2": {"fontFamily": {"value": "var(--apl-font-font-family-display2)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-display2)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-display2)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-display2)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-display2)", "type": "textDecoration"}, "alias": "headline2"}, "H1": {"fontFamily": {"value": "var(--apl-font-font-family-head-line1)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-head-line1)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-head-line1)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-head-line1)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-head-line1)", "type": "textDecoration"}}, "H2": {"fontFamily": {"value": "var(--apl-font-font-family-head-line2)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-head-line2)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-head-line2)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-head-line2)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-head-line2)", "type": "textDecoration"}}, "H3": {"fontFamily": {"value": "var(--apl-font-font-family-head-line3)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-head-line3)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-head-line3)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-head-line3)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-head-line3)", "type": "textDecoration"}}, "H4": {"fontFamily": {"value": "var(--apl-font-font-family-head-line4)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-head-line4)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-head-line4)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-head-line4)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-head-line4)", "type": "textDecoration"}}, "H5": {"fontFamily": {"value": "var(--apl-font-font-family-head-line5)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-head-line5)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-head-line5)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-head-line5)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-head-line5)", "type": "textDecoration"}}, "Body1": {"fontFamily": {"value": "var(--apl-font-font-family-body1)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-body1)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-body1)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-body1)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-body1)", "type": "textDecoration"}}, "Body2": {"fontFamily": {"value": "var(--apl-font-font-family-body2)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-body2)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-body2)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-body2)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-body2)", "type": "textDecoration"}}, "Caption": {"fontFamily": {"value": "var(--apl-font-font-family-caption)", "type": "fontFamilies"}, "fontSize": {"value": "var(--apl-font-font-size-caption)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-caption)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-caption)", "type": "textDecoration"}}, "TextLink": {"fontFamily": {"value": "var(--apl-font-font-family-text-link)", "type": "fontFamilies"}, "fontWeight": {"value": "var(--apl-font-font-weight-text-link)", "type": "fontWeights"}, "fontSize": {"value": "var(--apl-font-font-size-text-link)", "type": "fontSizes"}, "letterSpacing": {"value": "var(--apl-font-letter-spacing-text-link)", "type": "letterSpacing"}, "textDecoration": {"value": "var(--apl-font-text-decoration-text-link)", "type": "textDecoration"}}}, "global": {"colors": {"brand": {"10": {"value": "#F5FFF7", "type": "color"}, "20": {"value": "#D5F0E0", "type": "color"}, "30": {"value": "#8EC5A5", "type": "color"}, "40": {"value": "#5FA77D", "type": "color"}, "50": {"value": "#409261", "type": "color"}, "60": {"value": "#006D2E", "type": "color"}, "70": {"value": "#004A1F", "type": "color"}, "80": {"value": "#003C19", "type": "color"}, "90": {"value": "#002C12", "type": "color"}, "95": {"value": "#00210E", "type": "color"}}, "gray": {"0": {"value": "#FFFFFF", "type": "color"}, "5": {"value": "#FAFAFA", "type": "color"}, "10": {"value": "#F6F7FB", "type": "color"}, "20": {"value": "#E9EBF0", "type": "color"}, "30": {"value": "#D3D7E1", "type": "color"}, "40": {"value": "#BEC4D1", "type": "color"}, "50": {"value": "#A8B0C2", "type": "color"}, "60": {"value": "#778093", "type": "color"}, "70": {"value": "#5C6372", "type": "color"}, "80": {"value": "#424752", "type": "color"}, "90": {"value": "#272A31", "type": "color"}, "100": {"value": "#0C0E11", "type": "color"}}, "danger": {"10": {"value": "#FFF2F2", "type": "color"}, "20": {"value": "#F9D1D1", "type": "color"}, "30": {"value": "#F6BABA", "type": "color"}, "40": {"value": "#EA8484", "type": "color"}, "50": {"value": "#E74747", "type": "color"}, "60": {"value": "#E11919", "type": "color"}, "70": {"value": "#B41414", "type": "color"}, "80": {"value": "#870F0F", "type": "color"}, "90": {"value": "#5A0A0A", "type": "color"}, "95": {"value": "#440808", "type": "color"}}, "warning": {"10": {"value": "#FFF8DD", "type": "color"}, "20": {"value": "#FFEEB1", "type": "color"}, "30": {"value": "#FFDD86", "type": "color"}, "40": {"value": "#FFCB59", "type": "color"}, "50": {"value": "#FFB828", "type": "color"}, "60": {"value": "#F9A60C", "type": "color"}, "70": {"value": "#EC9709", "type": "color"}, "80": {"value": "#DF8806", "type": "color"}, "90": {"value": "#D37A03", "type": "color"}, "95": {"value": "#B26000", "type": "color"}}, "success": {"10": {"value": "#ECF6ED", "type": "color"}, "20": {"value": "#C8E6C9", "type": "color"}, "30": {"value": "#B3DBB5", "type": "color"}, "40": {"value": "#8DC891", "type": "color"}, "50": {"value": "#67B66C", "type": "color"}, "60": {"value": "#41A447", "type": "color"}, "70": {"value": "#348339", "type": "color"}, "80": {"value": "#27622B", "type": "color"}, "90": {"value": "#1A421C", "type": "color"}, "95": {"value": "#143115", "type": "color"}}, "process": {"10": {"value": "#EDF0FE", "type": "color"}, "20": {"value": "#DBE1FF", "type": "color"}, "30": {"value": "#B5C1FA", "type": "color"}, "40": {"value": "#91A3F7", "type": "color"}, "50": {"value": "#6C84F5", "type": "color"}, "60": {"value": "#4765F2", "type": "color"}, "70": {"value": "#3951C2", "type": "color"}, "80": {"value": "#2B3D91", "type": "color"}, "90": {"value": "#1C2861", "type": "color"}, "95": {"value": "#151E49", "type": "color"}}}, "font": {"families": {"display": {"value": "var(--font-ibm-plex-sans-thai)", "type": "fontFamilies"}, "Content": {"value": "var(--font-ibm-plex-sans-thai)", "type": "fontFamilies"}}, "weight": {"bold": {"value": "Bold", "type": "fontWeights"}, "semiBold": {"value": "SemiBold", "type": "fontWeights"}, "regular": {"value": "Regular", "type": "fontWeights"}}, "lineHeight": {"3xs": {"value": "20px", "type": "lineHeights"}, "2xs": {"value": "24px", "type": "lineHeights"}, "xs": {"value": "26px", "type": "lineHeights"}, "sm": {"value": "30px", "type": "lineHeights"}, "md": {"value": "40px", "type": "lineHeights"}, "lg": {"value": "46px", "type": "lineHeights"}, "xl": {"value": "52px", "type": "lineHeights"}, "2xl": {"value": "66px", "type": "lineHeights"}, "3xl": {"value": "132px", "type": "lineHeights"}}, "fontSize": {"3xs": {"value": "12px", "type": "fontSizes"}, "2xs": {"value": "14px", "type": "fontSizes"}, "xs": {"value": "16px", "type": "fontSizes"}, "sm": {"value": "20px", "type": "fontSizes"}, "md": {"value": "24px", "type": "fontSizes"}, "lg": {"value": "28px", "type": "fontSizes"}, "xl": {"value": "32px", "type": "fontSizes"}, "2xl": {"value": "40px", "type": "fontSizes"}, "3xl": {"value": "80px", "type": "fontSizes"}}, "textCase": {"none": {"value": "none", "type": "textCase"}, "upperCase": {"value": "uppercase", "type": "textCase"}}, "letterSpacing": {"md": {"value": "0", "type": "letterSpacing"}}, "paragraphSpacing": {"md": {"value": "0", "type": "paragraphSpacing"}}, "decoration": {"none": {"value": "none", "type": "textDecoration"}, "underLine": {"value": "underline", "type": "textDecoration"}, "lineThrough": {"value": "line-through", "type": "textDecoration"}}}, "spacing": {"2xs": {"value": "4px", "type": "spacing", "description": "0.25 rem"}, "xs": {"value": "8px", "type": "spacing", "description": "0.50 rem"}, "sm": {"value": "12px", "type": "spacing", "description": "0.75 rem"}, "md": {"value": "16px", "type": "spacing", "description": "1 rem"}, "lg": {"value": "20px", "type": "spacing", "description": "1.25 rem"}, "xl": {"value": "24px", "type": "spacing", "description": "1.50"}, "2xl": {"value": "28px", "type": "spacing", "description": "1.75"}, "3xl": {"value": "32px", "type": "spacing", "description": "2 rem"}, "4xl": {"value": "36px", "type": "spacing", "description": "2.25 rem"}}}}, "Alias": {"colors": {"surface": {"static": {"ui": {"default": {"value": "{global.colors.gray.0}", "type": "color"}, "hover": {"value": "{global.colors.gray.10}", "type": "color"}, "active": {"value": "{global.colors.gray.20}", "type": "color"}, "disabled": {"value": "{global.colors.gray.10}", "type": "color"}, "primary": {"value": "{global.colors.brand.10}", "type": "color"}, "delete": {"value": "{global.colors.danger.10}", "type": "color"}}, "default1": {"value": "{global.colors.gray.0}", "type": "color"}, "default2": {"value": "{global.colors.gray.10}", "type": "color"}, "default3": {"value": "{global.colors.gray.20}", "type": "color"}, "process": {"default": {"value": "{global.colors.process.10}", "type": "color"}, "hover": {"value": "{global.colors.process.20}", "type": "color"}, "active": {"value": "{global.colors.process.30}", "type": "color"}, "disabled": {"value": "{global.colors.process.10}", "type": "color"}}, "success": {"default": {"value": "{global.colors.brand.20}", "type": "color"}, "hover": {"value": "{global.colors.brand.30}", "type": "color"}, "active": {"value": "{global.colors.brand.40}", "type": "color"}, "disabled": {"value": "{global.colors.brand.10}", "type": "color"}}, "warning": {"default": {"value": "{global.colors.warning.10}", "type": "color"}, "hover": {"value": "{global.colors.warning.20}", "type": "color"}, "active": {"value": "{global.colors.warning.30}", "type": "color"}, "disabled": {"value": "{global.colors.warning.10}", "type": "color"}}, "danger": {"default": {"value": "{global.colors.danger.10}", "type": "color"}, "hover": {"value": "{global.colors.danger.20}", "type": "color"}, "active": {"value": "{global.colors.danger.30}", "type": "color"}, "disabled": {"value": "{global.colors.danger.10}", "type": "color"}}, "default4": {"value": "{global.colors.process.95}", "type": "color"}, "default5": {"value": "{global.colors.gray.5}", "type": "color"}}, "action": {"primary": {"default": {"value": "{global.colors.brand.60}", "type": "color"}, "hover": {"value": "{global.colors.brand.50}", "type": "color"}, "active": {"value": "{global.colors.brand.70}", "type": "color"}, "disabled": {"value": "{global.colors.gray.10}", "type": "color"}}, "delete": {"default": {"value": "{global.colors.danger.50}", "type": "color"}, "hover": {"value": "{global.colors.danger.40}", "type": "color"}, "active": {"value": "{global.colors.danger.70}", "type": "color"}, "disabled": {"value": "{global.colors.gray.10}", "type": "color"}}, "secondary": {"value": "{global.colors.gray.0}", "type": "color"}}}, "content": {"default": {"value": "{global.colors.gray.100}", "type": "color"}, "inversed": {"value": "{global.colors.gray.0}", "type": "color"}, "disabled": {"value": "{global.colors.gray.40}", "type": "color"}, "description": {"value": "{global.colors.gray.70}", "type": "color"}, "placeholder": {"value": "{global.colors.gray.40}", "type": "color"}, "onaction": {"value": "{global.colors.gray.90}", "type": "color"}, "onactioninversed": {"value": "{global.colors.gray.0}", "type": "color"}, "primary": {"default": {"value": "{global.colors.brand.60}", "type": "color"}, "subdued": {"value": "{global.colors.brand.40}", "type": "color"}}, "process": {"default": {"value": "{global.colors.process.60}", "type": "color"}, "subdued": {"value": "{global.colors.process.40}", "type": "color"}}, "success": {"default": {"value": "{global.colors.success.60}", "type": "color"}, "subdued": {"value": "{global.colors.success.40}", "type": "color"}}, "warning": {"default": {"value": "{global.colors.warning.60}", "type": "color"}, "subdued": {"value": "{global.colors.warning.40}", "type": "color"}}, "danger": {"default": {"value": "{global.colors.danger.60}", "type": "color"}, "subdued": {"value": "{global.colors.danger.40}", "type": "color"}}, "subdued": {"value": "{global.colors.gray.50}", "type": "color"}}, "border": {"default": {"value": "{global.colors.gray.30}", "type": "color"}, "disabled": {"value": "{global.colors.gray.40}", "type": "color"}, "subdued": {"value": "{global.colors.gray.5}", "type": "color"}, "accented": {"value": "{global.colors.gray.100}", "type": "color"}, "inversed": {"value": "{global.colors.gray.0}", "type": "color"}, "primary": {"default": {"value": "{global.colors.brand.60}", "type": "color"}, "subdued": {"value": "{global.colors.brand.50}", "type": "color"}, "accented": {"value": "{global.colors.brand.70}", "type": "color"}}, "process": {"default": {"value": "{global.colors.process.50}", "type": "color"}, "subdued": {"value": "{global.colors.process.30}", "type": "color"}, "accented": {"value": "{global.colors.process.70}", "type": "color"}}, "success": {"default": {"value": "{global.colors.success.50}", "type": "color"}, "subdued": {"value": "{global.colors.success.40}", "type": "color"}, "accented": {"value": "{global.colors.success.70}", "type": "color"}}, "warning": {"default": {"value": "{global.colors.warning.50}", "type": "color"}, "subdued": {"value": "{global.colors.warning.40}", "type": "color"}, "accented": {"value": "{global.colors.warning.70}", "type": "color"}}, "danger": {"default": {"value": "{global.colors.danger.50}", "type": "color"}, "subdued": {"value": "{global.colors.danger.40}", "type": "color"}, "accented": {"value": "{global.colors.danger.70}", "type": "color"}}, "focus": {"value": "{global.colors.brand.20}", "type": "color"}}}, "font": {"fontFamily": {"display1": {"value": "{global.font.families.display}", "type": "fontFamilies"}, "display2": {"value": "{global.font.families.display}", "type": "fontFamilies"}, "headLine1": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "headLine2": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "headLine3": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "headLine4": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "headLine5": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "body1": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "body2": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "caption": {"value": "{global.font.families.Content}", "type": "fontFamilies"}, "textLink": {"value": "{global.font.families.Content}", "type": "fontFamilies"}}, "letterSpacing": {"display1": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "display2": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "headLine1": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "headLine2": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "headLine3": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "headLine4": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "headLine5": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "body1": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "body2": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "caption": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}, "textLink": {"value": "{global.font.letterSpacing.md}", "type": "letterSpacing"}}, "lineHeight": {"display1": {"value": "{global.font.lineHeight.3xl}", "type": "lineHeights"}, "display2": {"value": "{global.font.lineHeight.2xl}", "type": "lineHeights"}, "headLine1": {"value": "{global.font.lineHeight.lg}", "type": "lineHeights"}, "headLine2": {"value": "{global.font.lineHeight.md}", "type": "lineHeights"}, "headLine3": {"value": "{global.font.lineHeight.sm}", "type": "lineHeights"}, "headLine4": {"value": "{global.font.lineHeight.xs}", "type": "lineHeights"}, "headLine5": {"value": "{global.font.lineHeight.2xs}", "type": "lineHeights"}, "body1": {"value": "{global.font.lineHeight.xs}", "type": "lineHeights"}, "body2": {"value": "{global.font.lineHeight.2xs}", "type": "lineHeights"}, "caption": {"value": "{global.font.lineHeight.3xs}", "type": "lineHeights"}, "textLink": {"value": "{global.font.lineHeight.xs}", "type": "lineHeights"}}, "fontWeight": {"display1": {"value": "{global.font.weight.bold}", "type": "fontWeights"}, "display2": {"value": "{global.font.weight.bold}", "type": "fontWeights"}, "headLine1": {"value": "{global.font.weight.semiBold}", "type": "fontWeights"}, "headLine2": {"value": "{global.font.weight.semiBold}", "type": "fontWeights"}, "headLine3": {"value": "{global.font.weight.semiBold}", "type": "fontWeights"}, "headLine4": {"value": "{global.font.weight.semiBold}", "type": "fontWeights"}, "headLine5": {"value": "{global.font.weight.semiBold}", "type": "fontWeights"}, "body1": {"value": "{global.font.weight.regular}", "type": "fontWeights"}, "body2": {"value": "{global.font.weight.regular}", "type": "fontWeights"}, "caption": {"value": "{global.font.weight.regular}", "type": "fontWeights"}, "textLink": {"value": "{global.font.weight.regular}", "type": "fontWeights"}}, "fontSize": {"display1": {"value": "{global.font.fontSize.3xl}", "type": "fontSizes"}, "display2": {"value": "{global.font.fontSize.2xl}", "type": "fontSizes"}, "headLine1": {"value": "{global.font.fontSize.lg}", "type": "fontSizes"}, "headLine2": {"value": "{global.font.fontSize.md}", "type": "fontSizes"}, "headLine3": {"value": "{global.font.fontSize.sm}", "type": "fontSizes"}, "headLine4": {"value": "{global.font.fontSize.xs}", "type": "fontSizes"}, "headLine5": {"value": "{global.font.fontSize.2xs}", "type": "fontSizes"}, "body1": {"value": "{global.font.fontSize.xs}", "type": "fontSizes"}, "body2": {"value": "{global.font.fontSize.2xs}", "type": "fontSizes"}, "caption": {"value": "{global.font.fontSize.3xs}", "type": "fontSizes"}, "textLink": {"value": "{global.font.fontSize.xs}", "type": "fontSizes"}}, "textCase": {"display1": {"value": "{global.font.textCase.none}", "type": "textCase"}, "display2": {"value": "{global.font.textCase.none}", "type": "textCase"}, "headLine1": {"value": "{global.font.textCase.none}", "type": "textCase"}, "headLine2": {"value": "{global.font.textCase.none}", "type": "textCase"}, "headLine3": {"value": "{global.font.textCase.none}", "type": "textCase"}, "headLine4": {"value": "{global.font.textCase.none}", "type": "textCase"}, "headLine5": {"value": "{global.font.textCase.none}", "type": "textCase"}, "body1": {"value": "{global.font.textCase.none}", "type": "textCase"}, "body2": {"value": "{global.font.textCase.none}", "type": "textCase"}, "caption": {"value": "{global.font.textCase.none}", "type": "textCase"}, "textLink": {"value": "{global.font.textCase.none}", "type": "textCase"}}, "textDecoration": {"display1": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "display2": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "headLine1": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "headLine2": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "headLine3": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "headLine4": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "headLine5": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "body1": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "body2": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "caption": {"value": "{global.font.decoration.none}", "type": "textDecoration"}, "textLink": {"value": "{global.font.decoration.underLine}", "type": "textDecoration"}}, "paragraphSpacing": {"display1": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "display2": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "headLine1": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "headLine2": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "headLine3": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "headLine4": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "headLine5": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "body1": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "body2": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "caption": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}, "textLink": {"value": "{global.font.paragraphSpacing.md}", "type": "paragraphSpacing"}}}, "space": {"padding": {"2xs": {"value": "{global.spacing.2xs}", "type": "spacing"}, "xs": {"value": "{global.spacing.xs}", "type": "spacing"}, "sm": {"value": "{global.spacing.sm}", "type": "spacing"}, "md": {"value": "{global.spacing.md}", "type": "spacing"}, "lg": {"value": "{global.spacing.lg}", "type": "spacing"}, "xl": {"value": "{global.spacing.xl}", "type": "spacing"}, "2xl": {"value": "{global.spacing.2xl}", "type": "spacing"}, "3xl": {"value": "{global.spacing.3xl}", "type": "spacing"}}, "gap": {"2xs": {"value": "{global.spacing.2xs}", "type": "spacing"}, "xs": {"value": "{global.spacing.xs}", "type": "spacing"}, "sm": {"value": "{global.spacing.sm}", "type": "spacing"}, "md": {"value": "{global.spacing.md}", "type": "spacing"}, "lg": {"value": "{global.spacing.lg}", "type": "spacing"}, "xl": {"value": "{global.spacing.xl}", "type": "spacing"}, "2xl": {"value": "{global.spacing.2xl}", "type": "spacing"}, "3xl": {"value": "{global.spacing.3xl}", "type": "spacing"}}}}, "Component": {"display": {"1": {"value": {"fontFamily": "{font.fontFamily.display1}", "fontWeight": "{font.fontWeight.display1}", "fontSize": "{font.fontSize.display1}", "letterSpacing": "{font.letterSpacing.display1}", "paragraphSpacing": "{font.paragraphSpacing.display1}", "textDecoration": "{font.textDecoration.display1}", "textCase": "{font.textCase.display1}"}, "type": "typography"}, "2": {"value": {"fontFamily": "{font.fontFamily.display2}", "fontWeight": "{font.fontWeight.display2}", "fontSize": "{font.fontSize.display2}", "letterSpacing": "{font.letterSpacing.display2}", "paragraphSpacing": "{font.paragraphSpacing.display2}", "textDecoration": "{font.textDecoration.display2}", "textCase": "{font.textCase.display2}"}, "type": "typography"}}, "headLine": {"1": {"value": {"fontFamily": "{font.fontFamily.headLine1}", "fontWeight": "{font.fontWeight.headLine1}", "fontSize": "{font.fontSize.headLine1}", "letterSpacing": "{font.letterSpacing.headLine1}", "paragraphSpacing": "{font.paragraphSpacing.headLine1}", "textDecoration": "{font.textDecoration.headLine1}", "textCase": "{font.textCase.headLine1}"}, "type": "typography"}, "2": {"value": {"fontFamily": "{font.fontFamily.headLine2}", "fontWeight": "{font.fontWeight.headLine2}", "fontSize": "{font.fontSize.headLine2}", "letterSpacing": "{font.letterSpacing.headLine2}", "paragraphSpacing": "{font.paragraphSpacing.headLine2}", "textDecoration": "{font.textDecoration.headLine2}", "textCase": "{font.textCase.headLine2}"}, "type": "typography"}, "3": {"value": {"fontFamily": "{font.fontFamily.headLine3}", "fontWeight": "{font.fontWeight.headLine3}", "fontSize": "{font.fontSize.headLine3}", "letterSpacing": "{font.letterSpacing.headLine3}", "paragraphSpacing": "{font.paragraphSpacing.headLine3}", "textDecoration": "{font.textDecoration.headLine3}", "textCase": "{font.textCase.headLine3}"}, "type": "typography"}, "4": {"value": {"fontFamily": "{font.fontFamily.headLine4}", "fontWeight": "{font.fontWeight.headLine4}", "fontSize": "{font.fontSize.headLine4}", "letterSpacing": "{font.letterSpacing.headLine4}", "paragraphSpacing": "{font.paragraphSpacing.headLine4}", "textDecoration": "{font.textDecoration.headLine4}", "textCase": "{font.textCase.headLine4}"}, "type": "typography"}, "5": {"value": {"fontFamily": "{font.fontFamily.headLine5}", "fontWeight": "{font.fontWeight.headLine5}", "fontSize": "{font.fontSize.headLine5}", "letterSpacing": "{font.letterSpacing.headLine5}", "paragraphSpacing": "{font.paragraphSpacing.headLine5}", "textDecoration": "{font.textDecoration.headLine5}", "textCase": "{font.textCase.headLine5}"}, "type": "typography"}}, "body": {"1": {"value": {"fontFamily": "{font.fontFamily.body1}", "fontWeight": "{font.fontWeight.body1}", "fontSize": "{font.fontSize.body1}", "letterSpacing": "{font.letterSpacing.body1}", "paragraphSpacing": "{font.paragraphSpacing.body1}", "textDecoration": "{font.textDecoration.body1}", "textCase": "{font.textCase.body1}"}, "type": "typography"}, "2": {"value": {"fontFamily": "{font.fontFamily.body2}", "fontWeight": "{font.fontWeight.body2}", "fontSize": "{font.fontSize.body2}", "letterSpacing": "{font.letterSpacing.body2}", "paragraphSpacing": "{font.paragraphSpacing.body2}", "textDecoration": "{font.textDecoration.body2}", "textCase": "{font.textCase.body2}"}, "type": "typography"}}, "caption": {"1": {"value": {"fontFamily": "{font.fontFamily.caption}", "fontWeight": "{font.fontWeight.caption}", "fontSize": "{font.fontSize.caption}", "letterSpacing": "{font.letterSpacing.caption}", "paragraphSpacing": "{font.paragraphSpacing.caption}", "textDecoration": "{font.textDecoration.caption}", "textCase": "{font.textCase.caption}"}, "type": "typography"}}, "textLink": {"default": {"value": {"fontFamily": "{font.fontFamily.textLink}", "fontWeight": "{font.fontWeight.textLink}", "fontSize": "{font.fontSize.textLink}", "letterSpacing": "{font.letterSpacing.textLink}", "paragraphSpacing": "{font.paragraphSpacing.textLink}", "textDecoration": "{font.textDecoration.textLink}", "textCase": "{font.textCase.textLink}"}, "type": "typography"}}}, "$themes": [], "$metadata": {"tokenSetOrder": ["global", "<PERSON><PERSON>", "Component"]}}