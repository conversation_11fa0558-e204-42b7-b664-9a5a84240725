// from tailwind/createPlugin.js
export function createPlugin(plugin: any, config?: any) {
  return {
    handler: plugin,
    config,
  }
}

createPlugin.withOptions = function (
  pluginFunction: (opt: any) => {},
  configFunction = (_: any) => ({})
) {
  const optionsFunction = function (options: any) {
    return {
      __options: options,
      handler: pluginFunction(options),
      config: configFunction(options),
    }
  }

  optionsFunction.__isOptionsFunction = true

  // Expose plugin dependencies so that `object-hash` returns a different
  // value if anything here changes, to ensure a rebuild is triggered.
  optionsFunction.__pluginFunction = pluginFunction
  optionsFunction.__configFunction = configFunction

  return optionsFunction
}
