import merge from "deepmerge"
import type { Config } from "tailwindcss"

import { apolloTailwindConfig } from "../generated-files/tailwind-preset/apollo/tokens.tailwind"
import { apolloTypographyVariant } from "../generated-files/tailwind-preset/apollo/typography-variant"
import { createPlugin } from "./utils"

const {
  global: { colors },
  alias: { colors: aliasColors },
} = apolloTailwindConfig

const materialTailwindConfig: Config = {
  darkMode: "class",
  content: [],
  theme: {
    extend: {
      colors: {
        ...colors,
        ...aliasColors,
      },
    },
  },
  plugins: [
    createPlugin(({ addComponents }: any) => {
      addComponents([apolloTypographyVariant])
    }),
  ],
}

/**
 * @param {object} tailwindConfig - Tailwind config object
 * @return {object} new config object
 */
export function injectApollo(tailwindConfig: Config): Config {
  const config = merge(materialTailwindConfig, { ...tailwindConfig })
  return config
}
