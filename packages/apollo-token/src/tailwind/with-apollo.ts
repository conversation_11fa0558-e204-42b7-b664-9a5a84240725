import merge from "deepmerge"
import type { Config } from "tailwindcss"

import { apolloTailwindConfig } from "../generated-files/tailwind/apollo-tokens.tailwind"
import { typographyVariant } from "../generated-files/tailwind/typographyVariant"
import { createPlugin } from "./utils"

const { colors, typography } = apolloTailwindConfig

const materialTailwindConfig: Config = {
  darkMode: "class",
  content: [],
  theme: {
    extend: {
      colors,
      fontSize: {
        display1: typography["display1"].fontSize,
        display2: typography["display2"].fontSize,
        h1: typography["h1"].fontSize,
        h2: typography["h2"].fontSize,
        h3: typography["h3"].fontSize,
        h4: typography["h4"].fontSize,
        h5: typography["h5"].fontSize,
        body1: typography["body1"].fontSize,
        body2: typography["body2"].fontSize,
        caption: typography["caption"].fontSize,
        textlink: typography["textlink"].fontSize,
      },
      fontWeight: {
        display1: typography["display1"].fontWeight,
        display2: typography["display2"].fontWeight,
        h1: typography["h1"].fontWeight,
        h2: typography["h2"].fontWeight,
        h3: typography["h3"].fontWeight,
        h4: typography["h4"].fontWeight,
        h5: typography["h5"].fontWeight,
        body1: typography["body1"].fontWeight,
        body2: typography["body2"].fontWeight,
        caption: "400",
        textlink: typography["textlink"].fontWeight,
      },
    },
  },
  plugins: [
    createPlugin(({ addComponents }: any) => {
      addComponents([typographyVariant])
    }),
  ],
}

/**
 * @param {object} tailwindConfig - Tailwind config object
 * @return {object} new config object
 */
export function withApollo(tailwindConfig: Config): Config {
  const config = merge(materialTailwindConfig, { ...tailwindConfig })
  return config
}
