const StyleDictionary = require("style-dictionary")
const { makeSdTailwindConfig } = require("sd-tailwindcss-transformer")
const tokenCore = require("./tokens/core.json")
const {
  registerTransforms,
  transforms,
} = require("@tokens-studio/sd-transforms")

registerTransforms(StyleDictionary)

// assume currently we use `Body1` as a base font-size.
function getBasePxFontSize() {
  const baseSize = tokenCore.typography.Body1.fontSize.value
  return baseSize || 16
}

function throwSizeError(name, value, unitType) {
  throw `Invalid Number: '${name}: ${value}' is not a valid number, cannot transform to '${unitType}' \n`
}

function fontSize(token, options) {
  const baseFont = getBasePxFontSize()
  const floatVal = parseFloat(token.value)
  if (isNaN(floatVal)) {
    throwSizeError(token.name, token.value, "rem")
  }

  if (floatVal === 0) {
    return "0"
  }

  return `${floatVal / baseFont}rem`
}

function fontWeight(token) {
  const weightMapper = {
    Thin: 100,
    ExtraLight: 200,
    Light: 300,
    Regular: 400,
    Medium: 500,
    SemiBold: 600,
    Bold: 700,
    ExtraBold: 800,
    Black: 900,
  }

  return weightMapper[token.value] || weightMapper["Regular"]
}

function fontFamily(token) {
  return token.value
}

StyleDictionary.registerTransform({
  transitive: true,
  name: "tailwind/typography",
  type: "value",
  matcher: function (token) {
    return token.attributes.category === "typography"
  },
  transformer: function (token, options) {
    if (token.attributes.item === "fontSize") return fontSize(token, options)
    if (token.attributes.item === "fontWeight") return fontWeight(token)
    if (token.attributes.item === "fontFamily") return fontFamily(token)
  },
})

const types = ["typography", "colors"]

types.map((type) => {
  const _StyleDictionary = StyleDictionary.extend(
    makeSdTailwindConfig({
      type,
      buildPath: "build/tailwind/",
      source: ["tokens/core.json"],
      transforms: ["attribute/cti", "name/cti/kebab", "tailwind/typography"],
    })
  )

  _StyleDictionary.buildAllPlatforms()
})
