import { Tokens } from "../tokens/ts/apollo"

export type TokenValue = string | number
export type TokenObject = {
  [key: string]: TokenValue | TokenObject
}

export type DefineTokens = Tokens | TokenObject

/**
 * @description converts the nested theme object to an array of style property string
 *
 * @param config
 * @param isVariable whether to add `--` prefix or not. Default `false`
 *
 * @returns `'property-name:value;'[]` or `'--css-variable-name:value;'[]`
 */
export function createStyleProperties<T extends object>(
  config: T,
  isVariable = false
): string[] {
  /**
   * This function shouldn't alter the any of the config's value (except for adding prefix),
   * because it assumes that value is a valid CSS property value
   */
  if (!config) {
    return []
  }

  let rootStyle: string[] = []

  const configEntries = Object.entries(config)

  configEntries.forEach(([key, value]) => {
    if (typeof value === "string") {
      const variableKey = isVariable ? `--${key}` : key
      rootStyle.push(`${variableKey}:${value};`)
    } else {
      rootStyle = [...rootStyle, ...createStyleProperties(value, isVariable)]
    }
  })

  return rootStyle
}

export function createTypographyAliasClasses(configs: Record<string, string>) {
  return createStyleProperties(configs)
}

/**
 * Converts a camelCase string to a kebab-case CSS variable name.
 * @param key The input string, potentially camelCase or dot-separated.
 * @param prefix An optional prefix for the CSS variable.
 * @returns The CSS variable name in kebab-case, prefixed.
 */
function toCSSVariableName(key: string, prefix: string = 'apl'): string {
  // Replace dots with hyphens for token paths (e.g., "color.green-pine.40" -> "color-green-pine-40")
  const dotToKebab = key.replace(/\./g, '-');
  // Convert camelCase to kebab-case
  const kebabCase = dotToKebab.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
  return `--${prefix}-${kebabCase}`;
}

/**
 * Converts a numeric value to CSS-compatible format with appropriate units.
 */
function formatNumericValue(value: number, path: string[]): string {
  // For properties containing 'weight' in their path, keep as numbers (e.g., font-weight)
  if (path.some(part => part.includes('weight'))) {
    return String(value);
  }

  const stringValue = String(value);
  // For other numbers, assume they need 'px' units if they don't already have units
  const hasUnits = ['px', '%', 'rem', 'em'].some(unit => stringValue.includes(unit));
  return hasUnits ? stringValue : `${value}px`;
}

/**
 * Converts token references to CSS variable references.
 */
function formatTokenReference(cssValue: string, prefix: string): string {
  if (cssValue.startsWith('{') && cssValue.endsWith('}')) {
    const tokenPath = cssValue.slice(1, -1);
    const referencedVariableName = toCSSVariableName(tokenPath, prefix);
    return `var(${referencedVariableName})`;
  }
  return cssValue;
}

/**
 * Processes a single token value and converts it to CSS format.
 */
function processTokenValue(value: TokenValue, path: string[], prefix: string): string {
  let cssValue = String(value);

  if (typeof value === 'number') {
    cssValue = formatNumericValue(value, path);
  }

  cssValue = formatTokenReference(cssValue, prefix);

  const variableName = toCSSVariableName(path.join('-'), prefix);
  return `  ${variableName}: ${cssValue};`;
}

/**
 * Recursively converts a token object to an array of CSS custom property strings.
 * @param obj The token object to convert.
 * @param prefix The prefix for CSS variable names.
 * @param currentPath An array representing the current path in the token object for nested properties.
 * @returns An array of CSS custom property strings.
 */
function tokensToCSSProperties(
  obj: DefineTokens,
  prefix: string = 'apl',
  currentPath: string[] = []
): string[] {
  const cssProperties: string[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const newPath = [...currentPath, key];

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      // Recursively process nested objects
      cssProperties.push(...tokensToCSSProperties(value, prefix, newPath));
    } else {
      // Process leaf values
      cssProperties.push(processTokenValue(value as TokenValue, newPath, prefix));
    }
  }

  return cssProperties;
}

/**
 * Converts a design tokens object into a sorted list of CSS custom property strings,
 * intelligently handling light-dark mode aliases.
 * @param tokens The design tokens object.
 * @param prefix The prefix for CSS variable names (default: 'apl').
 * @returns A sorted array of CSS custom property strings.
 */
export function generateStyleFromTokens(tokens: DefineTokens, prefix: string = 'apl'): string[] {
  const cssProperties = tokensToCSSProperties(tokens, prefix);

  // Group alias color properties by their base name to create light-dark() variants
  const lightDarkProperties: string[] = [];
  const regularProperties: string[] = [];
  // Map to store light/dark variants for each base color, e.g., { "--apl-alias-color-primary": { light: "...", dark: "..." } }
  const aliasColorMap = new Map<string, { light?: string; dark?: string }>();

  const aliasColorRegex = /^ {2}--apl-alias-color-(light|dark)-(.+?):\s*(.+);$/;

  cssProperties.forEach(prop => {
    // Use RegExp.exec() instead of String.match()
    const match = aliasColorRegex.exec(prop);
    if (match) {
      const [, mode, colorName, value] = match; // mode = 'light' or 'dark', colorName = 'primary', value = '#ffffff'
      const baseKey = `--${prefix}-alias-color-${colorName}`; // e.g., --apl-alias-color-primary

      if (!aliasColorMap.has(baseKey)) {
        aliasColorMap.set(baseKey, {});
      }
      aliasColorMap.get(baseKey)![mode as 'light' | 'dark'] = value;
    } else {
      regularProperties.push(prop);
    }
  });

  // Create light-dark() properties for colors that have both light and dark variants
  aliasColorMap.forEach((modes, baseKey) => {
    if (modes.light && modes.dark) {
      // If both light and dark variants exist, create a light-dark() CSS function
      lightDarkProperties.push(`  ${baseKey}: light-dark(${modes.light}, ${modes.dark});`);
    }
  });

  const allProperties = [...regularProperties, ...lightDarkProperties].sort((a, b) =>
    a.localeCompare(b)
  );

  return allProperties;
}
