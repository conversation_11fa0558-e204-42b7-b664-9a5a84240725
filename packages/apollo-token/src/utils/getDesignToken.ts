/**
 * This function extracts tokens object value, and transform the
 * value into a valid CSS property's value if necessary
 *
 * `Theme` is an object of design tokens with the value of valid CSS property's value
 */
export function transformTokensToTheme<T>(value: object) {
  return Object.entries(value).reduce<T>((obj: T, [key, data]) => {
    const themeValue = detectValue(data)

    return {
      ...obj,
      [key]: themeValue,
    }
  }, {} as T)
}

function detectValue(token: any) {
  const tokenValue = token?.value ?? token
  const tokenType = token?.type

  if (isCssVariable(tokenValue)) {
    // Regular expression to match content within curly braces {}
    const referencedTokenRegex = new RegExp("{(.*)}", "g")

    // It's safe to assert the type to string, because it has checked on `isCssVariable`
    const value = (tokenValue as string).replace(
      referencedTokenRegex,
      "var($1)"
    ) // Replace with var(--referenced-token-name)

    return value
  }

  if (isShadowToken(tokenType)) {
    const { x, y, blur, spread, color } = tokenValue ?? {}

    return parseShadowToken({ x, y, blur, spread, color })
  }

  return tokenValue
}

function isCssVariable(tokenValue: any) {
  // Regular expression to match any content within curly braces {}
  const curlyBracesRegex = new RegExp("{.*}", "g")

  return typeof tokenValue === "string" && curlyBracesRegex.test(tokenValue)
}

function isShadowToken(tokenType: any) {
  return (
    typeof tokenType === "string" && tokenType.toLowerCase().includes("shadow")
  )
}

type ShadowTokenValue = {
  x?: string
  y?: string
  blur?: string
  spread?: string
  color?: string
  type?: string
}

export function parseShadowToken({
  x = "",
  y = "",
  blur = "",
  spread = "",
  color = "",
}: ShadowTokenValue) {
  const consecutiveSpacesRegex = new RegExp(" +", "g")

  return `${x} ${y} ${blur} ${spread} ${color}`
    .replace(consecutiveSpacesRegex, " ")
    .trim()
}
