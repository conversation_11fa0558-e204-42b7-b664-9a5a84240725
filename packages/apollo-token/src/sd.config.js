const StyleDictionary = require("style-dictionary")
const {
  registerTransforms,
  transforms,
} = require("@tokens-studio/sd-transforms")
registerTransforms(StyleDictionary)

function hexToRgb(hex) {
  var shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i
  hex = hex.replace(shorthandRegex, function (m, r, g, b) {
    return r + r + g + g + b + b
  })

  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? parseInt(result[1], 16) +
        " " +
        parseInt(result[2], 16) +
        " " +
        parseInt(result[3], 16)
    : null
}

StyleDictionary.registerTransform({
  name: "color/css/hexrgba",
  type: "value",
  transitive: true,
  matcher: (token) => typeof token.value === "string" && token.type === "color",
  transformer: (token) => hexToRgb(token.value),
})

StyleDictionary.registerTransform({
  type: `value`,
  transitive: true,
  name: `tailwind/font/px`,
  matcher: function (token) {
    return token.attributes.category === "fontSize"
  },
  transformer: function ({ value }) {
    if (value.indexOf("px") !== -1) {
      return value
    }
    return `${value}px`
  },
})

module.exports = {
  source: ["src/tokens/core.json"],
  platforms: {
    json: {
      buildPath: "build/",
      transforms: ["attribute/cti", "name/cti/kebab", "size/rem"],
      prefix: "apl",
      files: [
        {
          destination: "tokens.json",
          format: "json",
        },
      ],
    },
    // UNUSED FOR NOW
    // js: {
    //   buildPath: "build/",
    //   transforms: ["attribute/cti", "name/cti/kebab", "size/rem"],
    //   transformGroup: "js",
    //   prefix: "cjx",
    //   files: [
    //     {
    //       destination: "tokensModule.js",
    //       format: "javascript/module",
    //       options: {
    //         outputReferences: true,
    //       },
    //     },
    //   ],
    // },
    // jsVariables: {
    //   buildPath: "build/",
    //   transforms: ["attribute/cti", "name/cti/constant", "size/rem"],
    //   transformGroup: "js",
    //   files: [
    //     {
    //       destination: "tokens.js",
    //       format: "javascript/es6",
    //       options: {
    //         outputReferences: true,
    //       },
    //     },
    //   ],
    // },
    css: {
      transformGroup: "tokens-studio",
      transforms: [
        "attribute/cti",
        "name/cti/kebab",
        "size/rem",
        ...transforms,
      ],
      buildPath: "build/",
      prefix: "apl",
      files: [
        {
          destination: "tokens.css",
          format: "css/variables",
          options: {
            outputReferences: true,
          },
        },
      ],
    },
  },
}
