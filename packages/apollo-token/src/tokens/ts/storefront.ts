/**
 * Apollo Design Tokens
 * Generated on: 2025-09-23T07:05:27.763Z
 *
 * This file contains all design tokens merged from:
 * - global.json
 * - system.json
 */

// All design tokens merged into single object with direct values
export const tokens = {
  "color": {
    "green-pine": {
      "0": "#000000",
      "10": "#002109",
      "20": "#003915",
      "30": "#005321",
      "40": "#016E2E",
      "50": "#2C8745",
      "60": "#49A25C",
      "70": "#64BE74",
      "80": "#7FDA8E",
      "90": "#9BF7A7",
      "95": "#C5FFC8",
      "99": "#F6FFF2",
      "100": "#FFFFFF"
    },
    "gray-smoke": {
      "0": "#000000",
      "10": "#1C1B1C",
      "20": "#313031",
      "30": "#474647",
      "40": "#5F5E5F",
      "50": "#787777",
      "60": "#929091",
      "70": "#ADABAB",
      "80": "#C8C6C6",
      "90": "#E5E2E2",
      "95": "#F3F0F0",
      "99": "#F8F7F7",
      "100": "#FFFFFF"
    },
    "blue-ocean": {
      "0": "#000000",
      "10": "#001159",
      "20": "#00218C",
      "30": "#0032C4",
      "40": "#2E4EDC",
      "50": "#4C6AF6",
      "60": "#7087FF",
      "70": "#95A6FF",
      "80": "#B9C3FF",
      "90": "#DEE1FF",
      "95": "#F0EFFF",
      "99": "#FEFBFF",
      "100": "#FFFFFF"
    },
    "gray-bluish": {
      "0": "#000000",
      "10": "#131C2B",
      "20": "#283141",
      "30": "#3E4758",
      "40": "#565F71",
      "50": "#6E778A",
      "60": "#8891A4",
      "70": "#A2ABC0",
      "80": "#BEC7DB",
      "90": "#DAE3F8",
      "95": "#ECF0FF",
      "99": "#FDFBFF",
      "100": "#FFFFFF"
    },
    "red-cherry": {
      "0": "#000000",
      "10": "#410001",
      "20": "#690003",
      "30": "#930006",
      "40": "#C0000B",
      "50": "#E9211E",
      "60": "#FF5546",
      "70": "#FF8A7B",
      "80": "#FFB4AA",
      "90": "#FFDAD5",
      "95": "#FFEDEA",
      "99": "#FFF4F3",
      "100": "#FFFFFF"
    },
    "yellow-peanut": {
      "0": "#000000",
      "10": "#403300",
      "20": "#665200",
      "30": "#8C7000",
      "40": "#B38F00",
      "50": "#D9AF09",
      "60": "#FFD118",
      "70": "#FFD940",
      "80": "#FFE169",
      "90": "#FFE991",
      "95": "#FFF1BA",
      "99": "#FFFAE6",
      "100": "#FFFFFF"
    },
    "green-matcha": {
      "0": "#000000",
      "10": "#233D18",
      "20": "#2F5220",
      "30": "#477A2F",
      "40": "#5EA33F",
      "50": "#76CC4F",
      "60": "#91D672",
      "70": "#ADE095",
      "80": "#C8EBB9",
      "90": "#D6F0CA",
      "95": "#E4F5DC",
      "99": "#F1FAED",
      "100": "#FFFFFF"
    },
    "lilac-soft": {
      "0": "#000000",
      "10": "#3F2745",
      "20": "#5E4264",
      "30": "#8C6C94",
      "40": "#AB87B3",
      "50": "#CAA3D3",
      "60": "#D5B5DC",
      "70": "#DFC8E5",
      "80": "#EADAED",
      "90": "#EFE3F2",
      "95": "#F4EDF6",
      "99": "#FAF6FB",
      "100": "#FFFFFF"
    },
    "black-soft": {
      "0": "#0000000D",
      "10": "#0000001A",
      "20": "#00000033",
      "30": "#0000004D",
      "40": "#00000066",
      "50": "#00000080",
      "60": "#00000099",
      "70": "#000000B3",
      "80": "#000000CC",
      "90": "#000000E6",
      "100": "#000000"
    },
    "white-soft": {
      "0": "#FFFFFF0D",
      "10": "#FFFFFF1A",
      "20": "#FFFFFF33",
      "30": "#FFFFFF4D",
      "40": "#FFFFFF66",
      "50": "#FFFFFF80",
      "60": "#FFFFFF99",
      "70": "#FFFFFFB3",
      "80": "#FFFFFFCC",
      "90": "#FFFFFFE6",
      "100": "#FFFFFF"
    }
  },
  "spacing": {
    "2": "2px",
    "4": "4px",
    "6": "6px",
    "8": "8px",
    "10": "10px",
    "12": "12px",
    "14": "14px",
    "16": "16px",
    "18": "18px",
    "20": "20px",
    "21": "21px",
    "22": "22px",
    "24": "24px",
    "26": "26px",
    "28": "28px",
    "30": "30px",
    "32": "32px",
    "34": "34px",
    "36": "36px",
    "38": "38px",
    "40": "40px",
    "42": "42px",
    "44": "44px",
    "46": "46px",
    "48": "48px",
    "50": "50px",
    "52": "52px",
    "54": "54px",
    "56": "56px",
    "58": "58px",
    "60": "60px",
    "62": "62px",
    "64": "64px",
    "66": "66px",
    "68": "68px",
    "70": "70px",
    "72": "72px",
    "74": "74px",
    "76": "76px",
    "78": "78px",
    "80": "80px",
    "82": "82px",
    "84": "84px",
    "86": "86px",
    "88": "88px",
    "90": "90px",
    "92": "92px",
    "94": "94px",
    "96": "96px",
    "98": "98px",
    "100": "100px",
    "102": "102px",
    "104": "104px",
    "106": "106px",
    "108": "108px",
    "110": "110px",
    "112": "112px",
    "114": "114px",
    "116": "116px",
    "118": "118px",
    "120": "120px",
    "122": "122px",
    "124": "124px",
    "126": "126px",
    "128": "128px",
    "none": "0px",
    "reverse-2": "-2px",
    "reverse-4": "-4px",
    "reverse-6": "-6px",
    "reverse-8": "-8px"
  },
  "typography": {
    "font-family": {
      "ibm-plex-sans-thai": "IBM Plex Sans Thai"
    },
    "font-size": {
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "15": "15px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "21": "21px",
      "22": "22px",
      "24": "24px",
      "26": "26px",
      "27": "27px",
      "28": "28px",
      "30": "30px",
      "32": "32px",
      "33": "33px",
      "34": "34px",
      "36": "36px",
      "38": "38px",
      "40": "40px",
      "42": "42px",
      "44": "44px",
      "45": "45px",
      "46": "46px",
      "47": "47px",
      "48": "48px",
      "50": "50px",
      "52": "52px",
      "54": "54px",
      "56": "56px",
      "57": "57px",
      "58": "58px",
      "60": "60px",
      "62": "62px",
      "64": "64px",
      "66": "66px",
      "68": "68px",
      "70": "70px",
      "72": "72px",
      "74": "74px",
      "76": "76px",
      "78": "78px",
      "80": "80px",
      "82": "82px",
      "84": "84px",
      "85": "85.5px",
      "86": "86px",
      "88": "88px",
      "67-5": "67.5px"
    },
    "font-weight": {
      "thin-it": 100,
      "thin": 100,
      "extra-light": 200,
      "light": 300,
      "regular": 400,
      "medium-it": 500,
      "medium": 500,
      "semi-bold": 600,
      "bold-it": 700,
      "bold": 700,
      "black-it": 900,
      "black": 900
    },
    "font-spacing": {
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "22": "22px",
      "24": "24px",
      "26": "26px",
      "28": "28px",
      "30": "30px",
      "32": "32px",
      "34": "34px",
      "36": "36px",
      "38": "38px",
      "40": "40px",
      "42": "42px",
      "44": "44px",
      "46": "46px",
      "48": "48px",
      "50": "50px",
      "52": "52px",
      "54": "54px",
      "56": "56px",
      "58": "58px",
      "60": "60px",
      "62": "62px",
      "64": "64px",
      "66": "66px",
      "68": "68px",
      "70": "70px",
      "72": "72px",
      "74": "74px",
      "76": "76px",
      "78": "78px",
      "80": "80px",
      "82": "82px",
      "84": "84px",
      "86": "86px",
      "88": "88px"
    },
    "line-height": {
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "15": "15px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "21": "21px",
      "22": "22px",
      "24": "24px",
      "26": "26px",
      "27": "27px",
      "28": "28px",
      "30": "30px",
      "32": "32px",
      "33": "33px",
      "34": "34px",
      "36": "36px",
      "38": "38px",
      "40": "40px",
      "42": "42px",
      "44": "44px",
      "46": "46px",
      "48": "48px",
      "50": "50px",
      "52": "52px",
      "54": "54px",
      "56": "56px",
      "58": "58px",
      "60": "60px",
      "62": "62px",
      "64": "64px",
      "66": "66px",
      "67": "67.5px",
      "68": "68px",
      "70": "70px",
      "72": "72px",
      "74": "74px",
      "76": "76px",
      "78": "78px",
      "80": "80px",
      "82": "82px",
      "84": "84px",
      "85": "85.5px",
      "86": "86px",
      "88": "88px",
      "96": "96px",
      "132": "132px"
    }
  },
  "radius": {
    "2": "2px",
    "4": "4px",
    "6": "6px",
    "8": "8px",
    "10": "10px",
    "12": "12px",
    "14": "14px",
    "16": "16px",
    "18": "18px",
    "20": "20px",
    "22": "22px",
    "24": "24px",
    "26": "26px",
    "28": "28px",
    "30": "30px",
    "32": "32px",
    "34": "34px",
    "36": "36px",
    "38": "38px",
    "40": "40px",
    "42": "42px",
    "44": "44px",
    "46": "46px",
    "48": "48px",
    "50": "50px",
    "52": "52px",
    "54": "54px",
    "56": "56px",
    "58": "58px",
    "60": "60px",
    "62": "62px",
    "64": "64px",
    "66": "66px",
    "68": "68px",
    "70": "70px",
    "72": "72px",
    "74": "74px",
    "76": "76px",
    "78": "78px",
    "80": "80px",
    "none": "0px",
    "full": "9999px"
  },
  "elevation": {
    "x-axis": {
      "1": "1px",
      "2": "2px",
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "22": "22px",
      "24": "24px",
      "none": "0px"
    },
    "y-axis": {
      "1": "1px",
      "2": "2px",
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "22": "22px",
      "24": "24px",
      "none": "0px"
    },
    "spread": {
      "1": "1px",
      "2": "2px",
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "22": "22px",
      "24": "24px",
      "none": "0px"
    },
    "blur": {
      "2": "2px",
      "4": "4px",
      "6": "6px",
      "8": "8px",
      "10": "10px",
      "12": "12px",
      "14": "14px",
      "16": "16px",
      "18": "18px",
      "20": "20px",
      "22": "22px",
      "24": "24px",
      "26": "26px",
      "28": "28px",
      "30": "30px",
      "32": "32px",
      "none": "0px"
    }
  },
  "alias": {
    "color": {
      "light": {
        "primary": {
          "primary": "{base.color.primary.40}",
          "surface-tint": "{base.color.primary.40}",
          "on-primary": "{base.color.primary.100}",
          "primary-container": "{base.color.primary.95}",
          "hovered": "{base.color.primary.50}",
          "focused": "{base.color.primary.60}",
          "pressed": "{base.color.primary.30}",
          "text-only-background-hovered": "{base.color.neutral.90}"
        },
        "error": {
          "error": "{base.color.danger.40}",
          "on-error": "{base.color.danger.100}",
          "error-container": "{base.color.danger.90}",
          "on-error-container": "{base.color.danger.30}",
          "text-icon-disabled": "{base.color.danger.40}",
          "container-disabled": "{base.color.danger.99}",
          "hovered": "{base.color.danger.50}",
          "focused": "{base.color.danger.60}",
          "pressed": "{base.color.danger.30}",
          "disable": "{base.color.danger.90}",
          "text-only-background-hovered": "{base.color.neutral.90}"
        },
        "static": {
          "text-icon": {
            "text-icon-light": "{base.color.neutral.100}",
            "text-icon-dark": "{base.color.neutral.10}"
          }
        },
        "warning": {
          "warning": "{base.color.warning.50}",
          "on-warning": "{base.color.warning.100}",
          "warning-container": "{base.color.warning.99}",
          "on-warning-container": "{base.color.warning.30}"
        },
        "secondary": {
          "seconday": "{base.color.secondary.40}",
          "on-secondary": "{base.color.secondary.100}",
          "secondary-container": "{base.color.secondary.95}",
          "on-secondary-container": "{base.color.secondary.30}"
        },
        "tertiary": {
          "tertiary": "{base.color.tertiary.40}",
          "on-tertiary": "{base.color.tertiary.100}",
          "tertiary-container": "{base.color.tertiary.95}",
          "on-tertiary-container": "{base.color.tertiary.30}"
        },
        "background-and-surface": {
          "background": "{base.color.neutral.100}",
          "on-background": "{base.color.neutral.99}",
          "surface": "{base.color.neutral.99}",
          "on-surface": "{base.color.neutral.30}",
          "surface-variant": "{base.color.neutral.100}",
          "on-surface-variant": "{base.color.neutral.80}",
          "text-icon-disabled": "{base.color.neutral.70}",
          "container-disabled": "{base.color.neutral.95}"
        },
        "outline-and-border": {
          "outline": "{base.color.neutral.70}",
          "outline-variant": "{base.color.neutral.80}",
          "border": "{base.color.neutral.30}",
          "border-disabled": "{base.color.neutral.70}"
        },
        "effects": {
          "shadow": "{base.color.overlay-black.40}",
          "scrim": "{base.color.overlay-black.40}",
          "overlay-surface-white": "{base.color.overlay-white.20}",
          "overlay-surface-black": "{base.color.overlay-black.20}",
          "overlay-surface-disabled": "{base.color.overlay-white.70}"
        },
        "success": {
          "success": "{base.color.success.50}",
          "on-success": "{base.color.success.100}",
          "success-container": "{base.color.success.99}",
          "on-success-container": "{base.color.success.30}"
        },
        "other": {
          "badge": {
            "low-stock": "{base.color.danger.90}",
            "more-item": "{base.color.danger.60}",
            "discount": "{base.color.danger.50}",
            "monthly": "{base.color.warning.60}"
          }
        }
      },
      "dark": {
        "primary": {
          "primary": "{base.color.primary.50}",
          "surface-tint": "{base.color.primary.70}",
          "on-primary": "{base.color.primary.20}",
          "primary-container": "{base.color.primary.30}",
          "hovered": "{base.color.primary.70}",
          "focused": "{base.color.primary.90}",
          "pressed": "{base.color.primary.30}",
          "text-only-background-hovered": "{base.color.overlay-black.20}"
        },
        "error": {
          "error": "{base.color.danger.90}",
          "on-error": "{base.color.danger.20}",
          "error-container": "{base.color.danger.70}",
          "on-error-container": "{base.color.danger.95}",
          "text-icon-disabled": "{base.color.danger.30}",
          "container-disabled": "{base.color.danger.10}",
          "hovered": "{base.color.danger.70}",
          "focused": "{base.color.danger.90}",
          "pressed": "{base.color.danger.30}",
          "disable": "{base.color.danger.10}",
          "text-only-background-hovered": "{base.color.overlay-black.20}"
        },
        "static": {
          "text-icon": {
            "text-icon-light": "{base.color.neutral.100}",
            "text-icon-dark": "{base.color.neutral.10}"
          }
        },
        "warning": {
          "warning": "{base.color.warning.90}",
          "on-warning": "{base.color.warning.20}",
          "warning-container": "{base.color.warning.30}",
          "on-warning-container": "{base.color.warning.99}"
        },
        "secondary": {
          "seconday": "{base.color.secondary.70}",
          "on-secondary": "{base.color.secondary.20}",
          "secondary-container": "{base.color.secondary.30}",
          "on-secondary-container": "{base.color.secondary.95}"
        },
        "tertiary": {
          "tertiary": "{base.color.tertiary.70}",
          "on-tertiary": "{base.color.tertiary.20}",
          "tertiary-container": "{base.color.tertiary.30}",
          "on-tertiary-container": "{base.color.tertiary.95}"
        },
        "background-and-surface": {
          "background": "{base.color.neutral.0}",
          "on-background": "{base.color.neutral.20}",
          "surface": "{base.color.neutral.10}",
          "on-surface": "{base.color.neutral.99}",
          "surface-variant": "{base.color.neutral.70}",
          "on-surface-variant": "{base.color.neutral.20}",
          "text-icon-disabled": "{base.color.neutral.20}",
          "container-disabled": "{base.color.neutral.10}"
        },
        "outline-and-border": {
          "outline": "{base.color.neutral.30}",
          "outline-variant": "{base.color.neutral.80}",
          "border": "{base.color.neutral.95}",
          "border-disabled": "{base.color.neutral.30}"
        },
        "effects": {
          "shadow": "{base.color.overlay-black.60}",
          "scrim": "{base.color.overlay-black.60}",
          "overlay-surface-white": "{base.color.overlay-white.20}",
          "overlay-surface-black": "{base.color.overlay-black.20}",
          "overlay-surface-disabled": "{base.color.overlay-black.70}"
        },
        "success": {
          "success": "{base.color.primary.90}",
          "on-success": "{base.color.success.20}",
          "success-container": "{base.color.success.30}",
          "on-success-container": "{base.color.success.95}"
        },
        "other": {
          "badge": {
            "low-stock": "{base.color.danger.90}",
            "more-item": "{base.color.danger.60}",
            "discount": "{base.color.danger.50}",
            "monthly": "{base.color.warning.60}"
          }
        }
      }
    },
    "typography": {
      "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
      "display": {
        "large": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.3xl}",
          "line-height": "{base.typography.line-height.line-height49}"
        },
        "medium": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.2xl}",
          "line-height": "{base.typography.line-height.line-height49}"
        },
        "small": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.xl}",
          "line-height": "{base.typography.line-height.line-height31}"
        }
      },
      "headline": {
        "large": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.lg}",
          "line-height": "{base.typography.line-height.line-height29}"
        },
        "medium": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.md}",
          "line-height": "{base.typography.line-height.line-height44}"
        },
        "small": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.sm}",
          "line-height": "{base.typography.line-height.line-height21}"
        }
      },
      "title": {
        "large": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.xs-plus}",
          "line-height": "{base.typography.line-height.line-height21}"
        },
        "medium": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.xs-md}",
          "line-height": "{base.typography.line-height.line-height21}"
        },
        "small": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight3}",
          "font-size": "{base.typography.font-size.xs-sm}",
          "line-height": "{base.typography.line-height.line-height17}"
        }
      },
      "body": {
        "large": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight1}",
          "weight-emphasized": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.xs}",
          "line-height": "{base.typography.line-height.line-height13}"
        },
        "medium": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight1}",
          "weight-emphasized": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.2xs}",
          "line-height": "{base.typography.line-height.line-height13}"
        },
        "small": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight1}",
          "weight-emphasized": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.3xs}",
          "line-height": "{base.typography.line-height.line-height10}"
        }
      },
      "label": {
        "large": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight1}",
          "weight-emphasized": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.2xs}",
          "line-height": "{base.typography.line-height.line-height10}"
        },
        "medium": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight1}",
          "weight-emphasized": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.3xs}",
          "line-height": "{base.typography.line-height.line-height10}"
        },
        "small": {
          "font-family": "{base.typography.font-family.ibm-plex-sans-thai}",
          "font-weight": "{base.typography.font-weight.weight1}",
          "weight-emphasized": "{base.typography.font-weight.weight2}",
          "font-size": "{base.typography.font-size.4xs}",
          "line-height": "{base.typography.line-height.line-height13}"
        }
      }
    },
    "spacing": {
      "margin": {
        "vertical": {
          "vertical": "{base.spacing.space15}"
        },
        "horizontal": {
          "horizontal": "{base.spacing.space15}"
        }
      },
      "padding": {
        "padding1": "{base.spacing.space1}",
        "padding2": "{base.spacing.space2}",
        "padding3": "{base.spacing.space3}",
        "padding4": "{base.spacing.space4}",
        "padding5": "{base.spacing.space5}",
        "padding6": "{base.spacing.space6}",
        "padding7": "{base.spacing.space7}",
        "padding8": "{base.spacing.space8}",
        "padding9": "{base.spacing.space9}",
        "padding10": "{base.spacing.space10}",
        "padding11": "{base.spacing.space11}",
        "padding12": "{base.spacing.space12}"
      },
      "gap": {
        "gap1": "{base.spacing.space1}",
        "gap2": "{base.spacing.space2}",
        "gap3": "{base.spacing.space3}",
        "gap4": "{base.spacing.space4}",
        "gap5": "{base.spacing.space5}",
        "gap6": "{base.spacing.space6}",
        "gap7": "{base.spacing.space7}",
        "gap8": "{base.spacing.space8}",
        "gap9": "{base.spacing.space9}",
        "gap10": "{base.spacing.space10}",
        "gap11": "{base.spacing.space11}",
        "gap12": "{base.spacing.space12}"
      }
    },
    "radius": {
      "radius1": "{base.radius.none}",
      "radius2": "{base.radius.4}",
      "radius3": "{base.radius.6}",
      "radius4": "{base.radius.8}",
      "radius5": "{base.radius.10}",
      "radius6": "{base.radius.12}",
      "radius7": "{base.radius.14}",
      "radius8": "{base.radius.16}",
      "radius9": "{base.radius.20}",
      "radius10": "{base.radius.24}",
      "radius11": "{base.radius.full}"
    },
    "elevation": {
      "elevations1": {
        "x-axis": "{base.elevation.x-axis.none}",
        "y-axis": "{base.elevation.y-axis.2}",
        "blur": "{base.elevation.blur.4}",
        "spread": "{base.spacing.space1}",
        "color": "{base.color.overlay-black.10}"
      },
      "elevations2": {
        "x-axis": "0px",
        "y-axis": "0px",
        "blur": "0px",
        "spread": "0px",
        "color": "0px"
      },
      "elevations3": {
        "x-axis": "0px",
        "y-axis": "0px",
        "blur": "0px",
        "spread": "0px",
        "color": "0px"
      }
    }
  },
  "base": {
    "color": {
      "primary": {
        "0": "{color.green-pine.0}",
        "10": "{color.green-pine.10}",
        "20": "{color.green-pine.20}",
        "30": "{color.green-pine.30}",
        "40": "{color.green-pine.40}",
        "50": "{color.green-pine.50}",
        "60": "{color.green-pine.60}",
        "70": "{color.green-pine.70}",
        "80": "{color.green-pine.80}",
        "90": "{color.green-pine.90}",
        "95": "{color.green-pine.95}",
        "99": "{color.green-pine.99}",
        "100": "{color.green-pine.100}"
      },
      "secondary": {
        "0": "{color.gray-bluish.0}",
        "10": "{color.gray-bluish.10}",
        "20": "{color.gray-bluish.20}",
        "30": "{color.gray-bluish.30}",
        "40": "{color.gray-bluish.40}",
        "50": "{color.gray-bluish.50}",
        "60": "{color.gray-bluish.60}",
        "70": "{color.gray-bluish.70}",
        "80": "{color.gray-bluish.80}",
        "90": "{color.gray-bluish.90}",
        "95": "{color.gray-bluish.95}",
        "99": "{color.gray-bluish.99}",
        "100": "{color.gray-bluish.100}"
      },
      "tertiary": {
        "0": "{color.blue-ocean.0}",
        "10": "{color.blue-ocean.10}",
        "20": "{color.blue-ocean.20}",
        "30": "{color.blue-ocean.30}",
        "40": "{color.blue-ocean.40}",
        "50": "{color.blue-ocean.50}",
        "60": "{color.blue-ocean.60}",
        "70": "{color.blue-ocean.70}",
        "80": "{color.blue-ocean.80}",
        "90": "{color.blue-ocean.90}",
        "95": "{color.blue-ocean.95}",
        "99": "{color.blue-ocean.99}",
        "100": "{color.blue-ocean.100}"
      },
      "neutral": {
        "0": "{color.gray-smoke.0}",
        "10": "{color.gray-smoke.10}",
        "20": "{color.gray-smoke.20}",
        "30": "{color.gray-smoke.30}",
        "40": "{color.gray-smoke.40}",
        "50": "{color.gray-smoke.50}",
        "60": "{color.gray-smoke.60}",
        "70": "{color.gray-smoke.70}",
        "80": "{color.gray-smoke.80}",
        "90": "{color.gray-smoke.90}",
        "95": "{color.gray-smoke.95}",
        "99": "{color.gray-smoke.99}",
        "100": "{color.gray-smoke.100}"
      },
      "danger": {
        "0": "{color.red-cherry.0}",
        "10": "{color.red-cherry.10}",
        "20": "{color.red-cherry.20}",
        "30": "{color.red-cherry.30}",
        "40": "{color.red-cherry.40}",
        "50": "{color.red-cherry.50}",
        "60": "{color.red-cherry.60}",
        "70": "{color.red-cherry.70}",
        "80": "{color.red-cherry.80}",
        "90": "{color.red-cherry.90}",
        "95": "{color.red-cherry.95}",
        "99": "{color.red-cherry.99}",
        "100": "{color.red-cherry.100}"
      },
      "warning": {
        "0": "{color.yellow-peanut.0}",
        "10": "{color.yellow-peanut.10}",
        "20": "{color.yellow-peanut.20}",
        "30": "{color.yellow-peanut.30}",
        "40": "{color.yellow-peanut.40}",
        "50": "{color.yellow-peanut.50}",
        "60": "{color.yellow-peanut.60}",
        "70": "{color.yellow-peanut.70}",
        "80": "{color.yellow-peanut.80}",
        "90": "{color.yellow-peanut.90}",
        "95": "{color.yellow-peanut.95}",
        "99": "{color.yellow-peanut.99}",
        "100": "{color.yellow-peanut.100}"
      },
      "success": {
        "0": "{color.green-matcha.0}",
        "10": "{color.green-matcha.10}",
        "20": "{color.green-matcha.20}",
        "30": "{color.green-matcha.30}",
        "40": "{color.green-matcha.40}",
        "50": "{color.green-matcha.50}",
        "60": "{color.green-matcha.60}",
        "70": "{color.green-matcha.70}",
        "80": "{color.green-matcha.80}",
        "90": "{color.green-matcha.90}",
        "95": "{color.green-matcha.95}",
        "99": "{color.green-matcha.99}",
        "100": "{color.green-matcha.100}"
      },
      "overlay-black": {
        "0": "{color.black-soft.0}",
        "10": "{color.black-soft.10}",
        "20": "{color.black-soft.20}",
        "30": "{color.black-soft.30}",
        "40": "{color.black-soft.40}",
        "50": "{color.black-soft.50}",
        "60": "{color.black-soft.60}",
        "70": "{color.black-soft.70}",
        "80": "{color.black-soft.80}",
        "90": "{color.black-soft.90}",
        "100": "{color.black-soft.100}"
      },
      "overlay-white": {
        "0": "{color.white-soft.0}",
        "10": "{color.white-soft.10}",
        "20": "{color.white-soft.20}",
        "30": "{color.white-soft.30}",
        "40": "{color.white-soft.40}",
        "50": "{color.white-soft.50}",
        "60": "{color.white-soft.60}",
        "70": "{color.white-soft.70}",
        "80": "{color.white-soft.80}",
        "90": "{color.white-soft.90}",
        "100": "{color.white-soft.100}"
      },
      "kid-club": {
        "0": "{color.lilac-soft.0}",
        "color": "#FFFFFF",
        "color-2": "#FFFFFF",
        "color-3": "#FFFFFF",
        "color-4": "#FFFFFF",
        "color-5": "#FFFFFF",
        "color-6": "#FFFFFF",
        "color-7": "#FFFFFF",
        "color-8": "#FFFFFF",
        "color-9": "#FFFFFF",
        "color-10": "#FFFFFF",
        "color-11": "#FFFFFF",
        "color-12": "#FFFFFF"
      }
    },
    "spacing": {
      "space1": "{spacing.none}",
      "space2": "{spacing.2}",
      "space3": "{spacing.4}",
      "space4": "{spacing.6}",
      "space5": "{spacing.8}",
      "space6": "{spacing.10}",
      "space7": "{spacing.12}",
      "space8": "{spacing.16}",
      "space9": "{spacing.20}",
      "space10": "{spacing.24}",
      "space11": "{spacing.28}",
      "space12": "{spacing.32}",
      "space13": "{spacing.36}",
      "space14": "{spacing.40}",
      "space15": "{spacing.48}",
      "neg-space1": "{spacing.reverse-2}",
      "neg-space2": "{spacing.reverse-4}",
      "neg-space3": "{spacing.reverse-6}",
      "neg-space4": "{spacing.reverse-8}",
      "tertiary": {
        "tertiary": "{base.color.tertiary.40}"
      },
      "title": {
        "large": {
          "font-size": "{spacing.font-scale.scale8}",
          "line-height": "{spacing.line-height.height8}",
          "letter-spacing": "{spacing.letter-spacing.letter3}"
        }
      }
    },
    "typography": {
      "font-family": {
        "ibm-plex-sans-thai": "{typography.font-family.ibm-plex-sans-thai}"
      },
      "font-size": {
        "3xl": "{typography.font-size.57}",
        "2xl": "{typography.font-size.45}",
        "xl": "{typography.font-size.36}",
        "lg": "{typography.font-size.32}",
        "md": "{typography.font-size.28}",
        "sm": "{typography.font-size.24}",
        "xs-plus": "{typography.font-size.22}",
        "xs-md": "{typography.font-size.20}",
        "xs-sm": "{typography.font-size.18}",
        "xs": "{typography.font-size.16}",
        "2xs": "{typography.font-size.14}",
        "3xs": "{typography.font-size.12}",
        "4xs": "{typography.font-size.10}"
      },
      "font-weight": {
        "weight1": "{typography.font-weight.regular}",
        "weight2": "{typography.font-weight.medium}",
        "weight3": "{typography.font-weight.bold}"
      },
      "line-height": {
        "line-height1": "{typography.line-height.4}",
        "line-height2": "{typography.line-height.6}",
        "line-height3": "{typography.line-height.8}",
        "line-height4": "{typography.line-height.10}",
        "line-height5": "{typography.line-height.12}",
        "line-height6": "{typography.line-height.14}",
        "line-height7": "{typography.line-height.15}",
        "line-height8": "{typography.line-height.16}",
        "line-height9": "{typography.line-height.18}",
        "line-height10": "{typography.line-height.20}",
        "line-height11": "{typography.line-height.21}",
        "line-height12": "{typography.line-height.22}",
        "line-height13": "{typography.line-height.24}",
        "line-height14": "{typography.line-height.26}",
        "line-height15": "{typography.line-height.27}",
        "line-height16": "{typography.line-height.28}",
        "line-height17": "{typography.line-height.30}",
        "line-height18": "{typography.line-height.32}",
        "line-height19": "{typography.line-height.33}",
        "line-height20": "{typography.line-height.34}",
        "line-height21": "{typography.line-height.36}",
        "line-height22": "{typography.line-height.38}",
        "line-height23": "{typography.line-height.40}",
        "line-height24": "{typography.line-height.42}",
        "line-height25": "{typography.line-height.44}",
        "line-height26": "{typography.line-height.46}",
        "line-height27": "{typography.line-height.48}",
        "line-height28": "{typography.line-height.50}",
        "line-height29": "{typography.line-height.52}",
        "line-height30": "{typography.line-height.54}",
        "line-height31": "{typography.line-height.56}",
        "line-height32": "{typography.line-height.58}",
        "line-height33": "{typography.line-height.60}",
        "line-height34": "{typography.line-height.62}",
        "line-height35": "{typography.line-height.64}",
        "line-height36": "{typography.line-height.66}",
        "line-height37": "{typography.line-height.67}",
        "line-height38": "{typography.line-height.68}",
        "line-height39": "{typography.line-height.70}",
        "line-height40": "{typography.line-height.72}",
        "line-height41": "{typography.line-height.74}",
        "line-height42": "{typography.line-height.76}",
        "line-height43": "{typography.line-height.78}",
        "line-height44": "{typography.line-height.80}",
        "line-height45": "{typography.line-height.82}",
        "line-height46": "{typography.line-height.84}",
        "line-height47": "{typography.line-height.85}",
        "line-height48": "{typography.line-height.86}",
        "line-height49": "{typography.line-height.88}",
        "line-height50": "{typography.line-height.96}",
        "line-height51": "{typography.line-height.132}"
      }
    },
    "radius": {
      "2": "{radius.2}",
      "4": "{radius.4}",
      "6": "{radius.6}",
      "8": "{radius.8}",
      "10": "{radius.10}",
      "12": "{radius.12}",
      "14": "{radius.14}",
      "16": "{radius.16}",
      "18": "{radius.18}",
      "20": "{radius.20}",
      "22": "{radius.22}",
      "24": "{radius.24}",
      "26": "{radius.26}",
      "28": "{radius.28}",
      "30": "{radius.30}",
      "32": "{radius.32}",
      "34": "{radius.34}",
      "36": "{radius.36}",
      "38": "{radius.38}",
      "40": "{radius.40}",
      "42": "{radius.42}",
      "44": "{radius.44}",
      "46": "{radius.46}",
      "48": "{radius.48}",
      "50": "{radius.50}",
      "52": "{radius.52}",
      "54": "{radius.54}",
      "56": "{radius.56}",
      "58": "{radius.58}",
      "60": "{radius.60}",
      "62": "{radius.62}",
      "64": "{radius.64}",
      "66": "{radius.66}",
      "68": "{radius.68}",
      "70": "{radius.70}",
      "72": "{radius.72}",
      "74": "{radius.74}",
      "76": "{radius.76}",
      "78": "{radius.78}",
      "80": "{radius.80}",
      "none": "{radius.none}",
      "full": "{radius.full}"
    },
    "elevation": {
      "x-axis": {
        "1": "{elevation.x-axis.1}",
        "2": "{elevation.x-axis.2}",
        "4": "{elevation.x-axis.4}",
        "6": "{elevation.x-axis.6}",
        "8": "{elevation.x-axis.8}",
        "10": "{elevation.x-axis.10}",
        "12": "{elevation.x-axis.12}",
        "14": "{elevation.x-axis.14}",
        "16": "{elevation.x-axis.16}",
        "18": "{elevation.x-axis.18}",
        "20": "{elevation.x-axis.20}",
        "22": "{elevation.y-axis.22}",
        "24": "{elevation.x-axis.24}",
        "none": "{elevation.x-axis.none}"
      },
      "y-axis": {
        "1": "{elevation.y-axis.1}",
        "2": "{elevation.y-axis.2}",
        "4": "{elevation.y-axis.4}",
        "6": "{elevation.y-axis.6}",
        "8": "{elevation.y-axis.8}",
        "10": "{elevation.y-axis.10}",
        "12": "{elevation.y-axis.12}",
        "14": "{elevation.y-axis.14}",
        "16": "{elevation.y-axis.16}",
        "18": "{elevation.y-axis.18}",
        "20": "{elevation.y-axis.20}",
        "22": "{elevation.y-axis.22}",
        "24": "{elevation.y-axis.24}",
        "none": "{elevation.y-axis.none}"
      },
      "spread": {
        "1": "{elevation.spread.1}",
        "2": "{elevation.spread.2}",
        "4": "{elevation.spread.4}",
        "6": "{elevation.spread.8}",
        "8": "{elevation.spread.8}",
        "10": "{elevation.spread.10}",
        "12": "{elevation.spread.12}",
        "14": "{elevation.spread.14}",
        "16": "{elevation.spread.16}",
        "18": "{elevation.spread.18}",
        "20": "{elevation.spread.20}",
        "22": "{elevation.spread.22}",
        "24": "{elevation.spread.24}",
        "none": "{elevation.spread.none}"
      },
      "blur": {
        "2": "{elevation.blur.2}",
        "4": "{elevation.blur.4}",
        "6": "{elevation.blur.6}",
        "8": "{elevation.blur.8}",
        "10": "{elevation.blur.10}",
        "12": "{elevation.blur.12}",
        "14": "{elevation.blur.14}",
        "16": "{elevation.blur.16}",
        "18": "{elevation.blur.18}",
        "20": "{elevation.blur.20}",
        "22": "{elevation.blur.22}",
        "24": "{elevation.blur.24}",
        "26": "{elevation.blur.26}",
        "28": "{elevation.blur.28}",
        "30": "{elevation.blur.30}",
        "32": "{elevation.blur.32}",
        "none": "{elevation.blur.none}"
      }
    },
    "effects": {}
  }
} as const;

// Type definitions
export type Tokens = typeof tokens;

// Default export
export default tokens;
