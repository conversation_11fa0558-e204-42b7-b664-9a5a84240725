// NOTE: This file is auto-generated. Do not modify it.
export const posandroidTypographyVariant = {
  ".typography-font-family": "var(--apl-alias-typography-font-family)",
  ".typography-font-size": {
    "4": "var(--apl-base-typography-font-size-4)",
    "6": "var(--apl-base-typography-font-size-6)",
    "8": "var(--apl-base-typography-font-size-8)",
    "10": "var(--apl-base-typography-font-size-10)",
    "12": "var(--apl-base-typography-font-size-12)",
    "14": "var(--apl-base-typography-font-size-14)",
    "15": "var(--apl-base-typography-font-size-15)",
    "16": "var(--apl-base-typography-font-size-16)",
    "18": "var(--apl-base-typography-font-size-18)",
    "20": "var(--apl-base-typography-font-size-20)",
    "21": "var(--apl-base-typography-font-size-21)",
    "22": "var(--apl-base-typography-font-size-22)",
    "24": "var(--apl-base-typography-font-size-24)",
    "26": "var(--apl-base-typography-font-size-26)",
    "27": "var(--apl-base-typography-font-size-27)",
    "28": "var(--apl-base-typography-font-size-28)",
    "30": "var(--apl-base-typography-font-size-30)",
    "32": "var(--apl-base-typography-font-size-32)",
    "33": "var(--apl-base-typography-font-size-33)",
    "34": "var(--apl-base-typography-font-size-34)",
    "36": "var(--apl-base-typography-font-size-36)",
    "38": "var(--apl-base-typography-font-size-38)",
    "40": "var(--apl-base-typography-font-size-40)",
    "42": "var(--apl-base-typography-font-size-42)",
    "44": "var(--apl-base-typography-font-size-44)",
    "45": "var(--apl-base-typography-font-size-45)",
    "46": "var(--apl-base-typography-font-size-46)",
    "47": "var(--apl-base-typography-font-size-47)",
    "48": "var(--apl-base-typography-font-size-48)",
    "50": "var(--apl-base-typography-font-size-50)",
    "52": "var(--apl-base-typography-font-size-52)",
    "54": "var(--apl-base-typography-font-size-54)",
    "56": "var(--apl-base-typography-font-size-56)",
    "57": "var(--apl-base-typography-font-size-57)",
    "58": "var(--apl-base-typography-font-size-58)",
    "60": "var(--apl-base-typography-font-size-60)",
    "62": "var(--apl-base-typography-font-size-62)",
    "64": "var(--apl-base-typography-font-size-64)",
    "66": "var(--apl-base-typography-font-size-66)",
    "68": "var(--apl-base-typography-font-size-68)",
    "70": "var(--apl-base-typography-font-size-70)",
    "72": "var(--apl-base-typography-font-size-72)",
    "74": "var(--apl-base-typography-font-size-74)",
    "76": "var(--apl-base-typography-font-size-76)",
    "78": "var(--apl-base-typography-font-size-78)",
    "80": "var(--apl-base-typography-font-size-80)",
    "82": "var(--apl-base-typography-font-size-82)",
    "84": "var(--apl-base-typography-font-size-84)",
    "85": "var(--apl-base-typography-font-size-85)",
    "86": "var(--apl-base-typography-font-size-86)",
    "88": "var(--apl-base-typography-font-size-88)",
    "67-5": "var(--apl-base-typography-font-size-67-5)",
    "3xl": "var(--apl-base-typography-font-size-3xl)",
    "2xl": "var(--apl-base-typography-font-size-2xl)",
    "xl": "var(--apl-base-typography-font-size-xl)",
    "lg": "var(--apl-base-typography-font-size-lg)",
    "md": "var(--apl-base-typography-font-size-md)",
    "sm": "var(--apl-base-typography-font-size-sm)",
    "xs-plus": "var(--apl-base-typography-font-size-xs-plus)",
    "xs-md": "var(--apl-base-typography-font-size-xs-md)",
    "xs-sm": "var(--apl-base-typography-font-size-xs-sm)",
    "xs": "var(--apl-base-typography-font-size-xs)",
    "2xs": "var(--apl-base-typography-font-size-2xs)",
    "3xs": "var(--apl-base-typography-font-size-3xs)",
    "4xs": "var(--apl-base-typography-font-size-4xs)"
  },
  ".typography-font-weight": {
    "thin-it": "var(--apl-base-typography-font-weight-thin-it)",
    "thin": "var(--apl-base-typography-font-weight-thin)",
    "extra-light": "var(--apl-base-typography-font-weight-extra-light)",
    "light": "var(--apl-base-typography-font-weight-light)",
    "regular": "var(--apl-base-typography-font-weight-regular)",
    "medium-it": "var(--apl-base-typography-font-weight-medium-it)",
    "medium": "var(--apl-base-typography-font-weight-medium)",
    "semi-bold": "var(--apl-base-typography-font-weight-semi-bold)",
    "bold-it": "var(--apl-base-typography-font-weight-bold-it)",
    "bold": "var(--apl-base-typography-font-weight-bold)",
    "black-it": "var(--apl-base-typography-font-weight-black-it)",
    "black": "var(--apl-base-typography-font-weight-black)",
    "weight1": "var(--apl-base-typography-font-weight-weight1)",
    "weight2": "var(--apl-base-typography-font-weight-weight2)",
    "weight3": "var(--apl-base-typography-font-weight-weight3)"
  },
  ".typography-font-spacing": {
    "4": "var(--apl-base-typography-font-spacing-4)",
    "6": "var(--apl-base-typography-font-spacing-6)",
    "8": "var(--apl-base-typography-font-spacing-8)",
    "10": "var(--apl-base-typography-font-spacing-10)",
    "12": "var(--apl-base-typography-font-spacing-12)",
    "14": "var(--apl-base-typography-font-spacing-14)",
    "16": "var(--apl-base-typography-font-spacing-16)",
    "18": "var(--apl-base-typography-font-spacing-18)",
    "20": "var(--apl-base-typography-font-spacing-20)",
    "22": "var(--apl-base-typography-font-spacing-22)",
    "24": "var(--apl-base-typography-font-spacing-24)",
    "26": "var(--apl-base-typography-font-spacing-26)",
    "28": "var(--apl-base-typography-font-spacing-28)",
    "30": "var(--apl-base-typography-font-spacing-30)",
    "32": "var(--apl-base-typography-font-spacing-32)",
    "34": "var(--apl-base-typography-font-spacing-34)",
    "36": "var(--apl-base-typography-font-spacing-36)",
    "38": "var(--apl-base-typography-font-spacing-38)",
    "40": "var(--apl-base-typography-font-spacing-40)",
    "42": "var(--apl-base-typography-font-spacing-42)",
    "44": "var(--apl-base-typography-font-spacing-44)",
    "46": "var(--apl-base-typography-font-spacing-46)",
    "48": "var(--apl-base-typography-font-spacing-48)",
    "50": "var(--apl-base-typography-font-spacing-50)",
    "52": "var(--apl-base-typography-font-spacing-52)",
    "54": "var(--apl-base-typography-font-spacing-54)",
    "56": "var(--apl-base-typography-font-spacing-56)",
    "58": "var(--apl-base-typography-font-spacing-58)",
    "60": "var(--apl-base-typography-font-spacing-60)",
    "62": "var(--apl-base-typography-font-spacing-62)",
    "64": "var(--apl-base-typography-font-spacing-64)",
    "66": "var(--apl-base-typography-font-spacing-66)",
    "68": "var(--apl-base-typography-font-spacing-68)",
    "70": "var(--apl-base-typography-font-spacing-70)",
    "72": "var(--apl-base-typography-font-spacing-72)",
    "74": "var(--apl-base-typography-font-spacing-74)",
    "76": "var(--apl-base-typography-font-spacing-76)",
    "78": "var(--apl-base-typography-font-spacing-78)",
    "80": "var(--apl-base-typography-font-spacing-80)",
    "82": "var(--apl-base-typography-font-spacing-82)",
    "84": "var(--apl-base-typography-font-spacing-84)",
    "86": "var(--apl-base-typography-font-spacing-86)",
    "88": "var(--apl-base-typography-font-spacing-88)"
  },
  ".typography-line-height": {
    "4": "var(--apl-base-typography-line-height-4)",
    "6": "var(--apl-base-typography-line-height-6)",
    "8": "var(--apl-base-typography-line-height-8)",
    "10": "var(--apl-base-typography-line-height-10)",
    "12": "var(--apl-base-typography-line-height-12)",
    "14": "var(--apl-base-typography-line-height-14)",
    "15": "var(--apl-base-typography-line-height-15)",
    "16": "var(--apl-base-typography-line-height-16)",
    "18": "var(--apl-base-typography-line-height-18)",
    "20": "var(--apl-base-typography-line-height-20)",
    "21": "var(--apl-base-typography-line-height-21)",
    "22": "var(--apl-base-typography-line-height-22)",
    "24": "var(--apl-base-typography-line-height-24)",
    "26": "var(--apl-base-typography-line-height-26)",
    "27": "var(--apl-base-typography-line-height-27)",
    "28": "var(--apl-base-typography-line-height-28)",
    "30": "var(--apl-base-typography-line-height-30)",
    "32": "var(--apl-base-typography-line-height-32)",
    "33": "var(--apl-base-typography-line-height-33)",
    "34": "var(--apl-base-typography-line-height-34)",
    "36": "var(--apl-base-typography-line-height-36)",
    "38": "var(--apl-base-typography-line-height-38)",
    "40": "var(--apl-base-typography-line-height-40)",
    "42": "var(--apl-base-typography-line-height-42)",
    "44": "var(--apl-base-typography-line-height-44)",
    "46": "var(--apl-base-typography-line-height-46)",
    "48": "var(--apl-base-typography-line-height-48)",
    "50": "var(--apl-base-typography-line-height-50)",
    "52": "var(--apl-base-typography-line-height-52)",
    "54": "var(--apl-base-typography-line-height-54)",
    "56": "var(--apl-base-typography-line-height-56)",
    "58": "var(--apl-base-typography-line-height-58)",
    "60": "var(--apl-base-typography-line-height-60)",
    "62": "var(--apl-base-typography-line-height-62)",
    "64": "var(--apl-base-typography-line-height-64)",
    "66": "var(--apl-base-typography-line-height-66)",
    "67": "var(--apl-base-typography-line-height-67)",
    "68": "var(--apl-base-typography-line-height-68)",
    "70": "var(--apl-base-typography-line-height-70)",
    "72": "var(--apl-base-typography-line-height-72)",
    "74": "var(--apl-base-typography-line-height-74)",
    "76": "var(--apl-base-typography-line-height-76)",
    "78": "var(--apl-base-typography-line-height-78)",
    "80": "var(--apl-base-typography-line-height-80)",
    "82": "var(--apl-base-typography-line-height-82)",
    "84": "var(--apl-base-typography-line-height-84)",
    "85": "var(--apl-base-typography-line-height-85)",
    "86": "var(--apl-base-typography-line-height-86)",
    "88": "var(--apl-base-typography-line-height-88)",
    "96": "var(--apl-base-typography-line-height-96)",
    "100": "var(--apl-base-typography-line-height-100)",
    "132": "var(--apl-base-typography-line-height-132)",
    "line-height1": "var(--apl-base-typography-line-height-line-height1)",
    "line-height2": "var(--apl-base-typography-line-height-line-height2)",
    "line-height3": "var(--apl-base-typography-line-height-line-height3)",
    "line-height4": "var(--apl-base-typography-line-height-line-height4)",
    "line-height5": "var(--apl-base-typography-line-height-line-height5)",
    "line-height6": "var(--apl-base-typography-line-height-line-height6)",
    "line-height7": "var(--apl-base-typography-line-height-line-height7)",
    "line-height8": "var(--apl-base-typography-line-height-line-height8)",
    "line-height9": "var(--apl-base-typography-line-height-line-height9)",
    "line-height10": "var(--apl-base-typography-line-height-line-height10)",
    "line-height11": "var(--apl-base-typography-line-height-line-height11)",
    "line-height12": "var(--apl-base-typography-line-height-line-height12)",
    "line-height13": "var(--apl-base-typography-line-height-line-height13)",
    "line-height14": "var(--apl-base-typography-line-height-line-height14)",
    "line-height15": "var(--apl-base-typography-line-height-line-height15)",
    "line-height16": "var(--apl-base-typography-line-height-line-height16)",
    "line-height17": "var(--apl-base-typography-line-height-line-height17)",
    "line-height18": "var(--apl-base-typography-line-height-line-height18)",
    "line-height19": "var(--apl-base-typography-line-height-line-height19)",
    "line-height20": "var(--apl-base-typography-line-height-line-height20)",
    "line-height21": "var(--apl-base-typography-line-height-line-height21)",
    "line-height22": "var(--apl-base-typography-line-height-line-height22)",
    "line-height23": "var(--apl-base-typography-line-height-line-height23)",
    "line-height24": "var(--apl-base-typography-line-height-line-height24)",
    "line-height25": "var(--apl-base-typography-line-height-line-height25)",
    "line-height26": "var(--apl-base-typography-line-height-line-height26)",
    "line-height27": "var(--apl-base-typography-line-height-line-height27)",
    "line-height28": "var(--apl-base-typography-line-height-line-height28)",
    "line-height29": "var(--apl-base-typography-line-height-line-height29)",
    "line-height30": "var(--apl-base-typography-line-height-line-height30)",
    "line-height31": "var(--apl-base-typography-line-height-line-height31)",
    "line-height32": "var(--apl-base-typography-line-height-line-height32)",
    "line-height33": "var(--apl-base-typography-line-height-line-height33)",
    "line-height34": "var(--apl-base-typography-line-height-line-height34)",
    "line-height35": "var(--apl-base-typography-line-height-line-height35)",
    "line-height36": "var(--apl-base-typography-line-height-line-height36)",
    "line-height37": "var(--apl-base-typography-line-height-line-height37)",
    "line-height38": "var(--apl-base-typography-line-height-line-height38)",
    "line-height39": "var(--apl-base-typography-line-height-line-height39)",
    "line-height40": "var(--apl-base-typography-line-height-line-height40)",
    "line-height41": "var(--apl-base-typography-line-height-line-height41)",
    "line-height42": "var(--apl-base-typography-line-height-line-height42)",
    "line-height43": "var(--apl-base-typography-line-height-line-height43)",
    "line-height44": "var(--apl-base-typography-line-height-line-height44)",
    "line-height45": "var(--apl-base-typography-line-height-line-height45)",
    "line-height46": "var(--apl-base-typography-line-height-line-height46)",
    "line-height47": "var(--apl-base-typography-line-height-line-height47)",
    "line-height48": "var(--apl-base-typography-line-height-line-height48)",
    "line-height49": "var(--apl-base-typography-line-height-line-height49)",
    "line-height50": "var(--apl-base-typography-line-height-line-height50)",
    "line-height51": "var(--apl-base-typography-line-height-line-height51)",
    "line-height52": "var(--apl-base-typography-line-height-line-height52)"
  },
  ".typography-surface": {
    "static": {
      "default2": "var(--apl-base-typography-surface-static-default2)",
      "default2-2": "var(--apl-base-typography-surface-static-default2-2)"
    }
  },
  ".typography-m3": {
    "display": {
      "large": "var(--apl-base-typography-m3-display-large)",
      "medium": "var(--apl-base-typography-m3-display-medium)",
      "small": "var(--apl-base-typography-m3-display-small)"
    },
    "headerline": {
      "large": "var(--apl-base-typography-m3-headerline-large)",
      "medium": "var(--apl-base-typography-m3-headerline-medium)",
      "small": "var(--apl-base-typography-m3-headerline-small)"
    },
    "title": {
      "large": "var(--apl-base-typography-m3-title-large)",
      "medium": "var(--apl-base-typography-m3-title-medium)",
      "small": "var(--apl-base-typography-m3-title-small)"
    },
    "body": {
      "large": "var(--apl-base-typography-m3-body-large)",
      "large-emphasized": "var(--apl-base-typography-m3-body-large-emphasized)",
      "medium": "var(--apl-base-typography-m3-body-medium)",
      "medium-emphasized": "var(--apl-base-typography-m3-body-medium-emphasized)",
      "small": "var(--apl-base-typography-m3-body-small)",
      "small-emphasized": "var(--apl-base-typography-m3-body-small-emphasized)"
    },
    "label": {
      "large-emphasized": "var(--apl-base-typography-m3-label-large-emphasized)",
      "large": "var(--apl-base-typography-m3-label-large)",
      "medium": "var(--apl-base-typography-m3-label-medium)",
      "medium-emphasized": "var(--apl-base-typography-m3-label-medium-emphasized)",
      "small": "var(--apl-base-typography-m3-label-small)",
      "small-emphasized": "var(--apl-base-typography-m3-label-small-emphasized)"
    }
  },
  ".typography-display": {
    "large": {
      "font-family": "var(--apl-alias-typography-display-large-font-family)",
      "font-weight": "var(--apl-alias-typography-display-large-font-weight)",
      "font-size": "var(--apl-alias-typography-display-large-font-size)",
      "line-height": "var(--apl-alias-typography-display-large-line-height)"
    },
    "medium": {
      "font-family": "var(--apl-alias-typography-display-medium-font-family)",
      "font-weight": "var(--apl-alias-typography-display-medium-font-weight)",
      "font-size": "var(--apl-alias-typography-display-medium-font-size)",
      "line-height": "var(--apl-alias-typography-display-medium-line-height)"
    },
    "small": {
      "font-family": "var(--apl-alias-typography-display-small-font-family)",
      "font-weight": "var(--apl-alias-typography-display-small-font-weight)",
      "font-size": "var(--apl-alias-typography-display-small-font-size)",
      "line-height": "var(--apl-alias-typography-display-small-line-height)"
    }
  },
  ".typography-headline": {
    "large": {
      "font-family": "var(--apl-alias-typography-headline-large-font-family)",
      "font-weight": "var(--apl-alias-typography-headline-large-font-weight)",
      "font-size": "var(--apl-alias-typography-headline-large-font-size)",
      "line-height": "var(--apl-alias-typography-headline-large-line-height)"
    },
    "medium": {
      "font-family": "var(--apl-alias-typography-headline-medium-font-family)",
      "font-weight": "var(--apl-alias-typography-headline-medium-font-weight)",
      "font-size": "var(--apl-alias-typography-headline-medium-font-size)",
      "line-height": "var(--apl-alias-typography-headline-medium-line-height)"
    },
    "small": {
      "font-family": "var(--apl-alias-typography-headline-small-font-family)",
      "font-weight": "var(--apl-alias-typography-headline-small-font-weight)",
      "font-size": "var(--apl-alias-typography-headline-small-font-size)",
      "line-height": "var(--apl-alias-typography-headline-small-line-height)"
    }
  },
  ".typography-title": {
    "large": {
      "font-family": "var(--apl-alias-typography-title-large-font-family)",
      "font-weight": "var(--apl-alias-typography-title-large-font-weight)",
      "font-size": "var(--apl-alias-typography-title-large-font-size)",
      "line-height": "var(--apl-alias-typography-title-large-line-height)"
    },
    "medium": {
      "font-family": "var(--apl-alias-typography-title-medium-font-family)",
      "font-weight": "var(--apl-alias-typography-title-medium-font-weight)",
      "font-size": "var(--apl-alias-typography-title-medium-font-size)",
      "line-height": "var(--apl-alias-typography-title-medium-line-height)"
    },
    "small": {
      "font-family": "var(--apl-alias-typography-title-small-font-family)",
      "font-weight": "var(--apl-alias-typography-title-small-font-weight)",
      "font-size": "var(--apl-alias-typography-title-small-font-size)",
      "line-height": "var(--apl-alias-typography-title-small-line-height)"
    }
  },
  ".typography-body": {
    "large": {
      "font-family": "var(--apl-alias-typography-body-large-font-family)",
      "font-weight": "var(--apl-alias-typography-body-large-font-weight)",
      "weight-emphasized": "var(--apl-alias-typography-body-large-weight-emphasized)",
      "font-size": "var(--apl-alias-typography-body-large-font-size)",
      "line-height": "var(--apl-alias-typography-body-large-line-height)"
    },
    "medium": {
      "font-family": "var(--apl-alias-typography-body-medium-font-family)",
      "font-weight": "var(--apl-alias-typography-body-medium-font-weight)",
      "weight-emphasized": "var(--apl-alias-typography-body-medium-weight-emphasized)",
      "font-size": "var(--apl-alias-typography-body-medium-font-size)",
      "line-height": "var(--apl-alias-typography-body-medium-line-height)"
    },
    "small": {
      "font-family": "var(--apl-alias-typography-body-small-font-family)",
      "font-weight": "var(--apl-alias-typography-body-small-font-weight)",
      "weight-emphasized": "var(--apl-alias-typography-body-small-weight-emphasized)",
      "font-size": "var(--apl-alias-typography-body-small-font-size)",
      "line-height": "var(--apl-alias-typography-body-small-line-height)"
    }
  },
  ".typography-label": {
    "large": {
      "font-family": "var(--apl-alias-typography-label-large-font-family)",
      "font-weight": "var(--apl-alias-typography-label-large-font-weight)",
      "weight-emphasized": "var(--apl-alias-typography-label-large-weight-emphasized)",
      "font-size": "var(--apl-alias-typography-label-large-font-size)",
      "line-height": "var(--apl-alias-typography-label-large-line-height)"
    },
    "medium": {
      "font-family": "var(--apl-alias-typography-label-medium-font-family)",
      "font-weight": "var(--apl-alias-typography-label-medium-font-weight)",
      "weight-emphasized": "var(--apl-alias-typography-label-medium-weight-emphasized)",
      "font-size": "var(--apl-alias-typography-label-medium-font-size)",
      "line-height": "var(--apl-alias-typography-label-medium-line-height)"
    },
    "small": {
      "font-family": "var(--apl-alias-typography-label-small-font-family)",
      "font-weight": "var(--apl-alias-typography-label-small-font-weight)",
      "weight-emphasized": "var(--apl-alias-typography-label-small-weight-emphasized)",
      "font-size": "var(--apl-alias-typography-label-small-font-size)",
      "line-height": "var(--apl-alias-typography-label-small-line-height)"
    }
  }
};
