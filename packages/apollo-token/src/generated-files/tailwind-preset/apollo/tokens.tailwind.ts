// NOTE: This file is auto-generated. Do not modify it.
export const apolloTailwindConfig = {
  "global": {
    "colors": {
      "green-pine": {
        "0": "var(--apl-color-green-pine-0)",
        "10": "var(--apl-color-green-pine-10)",
        "20": "var(--apl-color-green-pine-20)",
        "30": "var(--apl-color-green-pine-30)",
        "40": "var(--apl-color-green-pine-40)",
        "50": "var(--apl-color-green-pine-50)",
        "60": "var(--apl-color-green-pine-60)",
        "70": "var(--apl-color-green-pine-70)",
        "80": "var(--apl-color-green-pine-80)",
        "90": "var(--apl-color-green-pine-90)",
        "95": "var(--apl-color-green-pine-95)",
        "99": "var(--apl-color-green-pine-99)",
        "100": "var(--apl-color-green-pine-100)"
      },
      "gray-smoke": {
        "0": "var(--apl-color-gray-smoke-0)",
        "10": "var(--apl-color-gray-smoke-10)",
        "20": "var(--apl-color-gray-smoke-20)",
        "30": "var(--apl-color-gray-smoke-30)",
        "40": "var(--apl-color-gray-smoke-40)",
        "50": "var(--apl-color-gray-smoke-50)",
        "60": "var(--apl-color-gray-smoke-60)",
        "70": "var(--apl-color-gray-smoke-70)",
        "80": "var(--apl-color-gray-smoke-80)",
        "90": "var(--apl-color-gray-smoke-90)",
        "95": "var(--apl-color-gray-smoke-95)",
        "99": "var(--apl-color-gray-smoke-99)",
        "100": "var(--apl-color-gray-smoke-100)"
      },
      "blue-ocean": {
        "0": "var(--apl-color-blue-ocean-0)",
        "10": "var(--apl-color-blue-ocean-10)",
        "20": "var(--apl-color-blue-ocean-20)",
        "30": "var(--apl-color-blue-ocean-30)",
        "40": "var(--apl-color-blue-ocean-40)",
        "50": "var(--apl-color-blue-ocean-50)",
        "60": "var(--apl-color-blue-ocean-60)",
        "70": "var(--apl-color-blue-ocean-70)",
        "80": "var(--apl-color-blue-ocean-80)",
        "90": "var(--apl-color-blue-ocean-90)",
        "95": "var(--apl-color-blue-ocean-95)",
        "99": "var(--apl-color-blue-ocean-99)",
        "100": "var(--apl-color-blue-ocean-100)"
      },
      "gray-bluish": {
        "0": "var(--apl-color-gray-bluish-0)",
        "10": "var(--apl-color-gray-bluish-10)",
        "20": "var(--apl-color-gray-bluish-20)",
        "30": "var(--apl-color-gray-bluish-30)",
        "40": "var(--apl-color-gray-bluish-40)",
        "50": "var(--apl-color-gray-bluish-50)",
        "60": "var(--apl-color-gray-bluish-60)",
        "70": "var(--apl-color-gray-bluish-70)",
        "80": "var(--apl-color-gray-bluish-80)",
        "90": "var(--apl-color-gray-bluish-90)",
        "95": "var(--apl-color-gray-bluish-95)",
        "99": "var(--apl-color-gray-bluish-99)",
        "100": "var(--apl-color-gray-bluish-100)"
      },
      "red-cherry": {
        "0": "var(--apl-color-red-cherry-0)",
        "10": "var(--apl-color-red-cherry-10)",
        "20": "var(--apl-color-red-cherry-20)",
        "30": "var(--apl-color-red-cherry-30)",
        "40": "var(--apl-color-red-cherry-40)",
        "50": "var(--apl-color-red-cherry-50)",
        "60": "var(--apl-color-red-cherry-60)",
        "70": "var(--apl-color-red-cherry-70)",
        "80": "var(--apl-color-red-cherry-80)",
        "90": "var(--apl-color-red-cherry-90)",
        "95": "var(--apl-color-red-cherry-95)",
        "99": "var(--apl-color-red-cherry-99)",
        "100": "var(--apl-color-red-cherry-100)"
      },
      "yellow-peanut": {
        "0": "var(--apl-color-yellow-peanut-0)",
        "10": "var(--apl-color-yellow-peanut-10)",
        "20": "var(--apl-color-yellow-peanut-20)",
        "30": "var(--apl-color-yellow-peanut-30)",
        "40": "var(--apl-color-yellow-peanut-40)",
        "50": "var(--apl-color-yellow-peanut-50)",
        "60": "var(--apl-color-yellow-peanut-60)",
        "70": "var(--apl-color-yellow-peanut-70)",
        "80": "var(--apl-color-yellow-peanut-80)",
        "90": "var(--apl-color-yellow-peanut-90)",
        "95": "var(--apl-color-yellow-peanut-95)",
        "99": "var(--apl-color-yellow-peanut-99)",
        "100": "var(--apl-color-yellow-peanut-100)"
      },
      "green-matcha": {
        "0": "var(--apl-color-green-matcha-0)",
        "10": "var(--apl-color-green-matcha-10)",
        "20": "var(--apl-color-green-matcha-20)",
        "30": "var(--apl-color-green-matcha-30)",
        "40": "var(--apl-color-green-matcha-40)",
        "50": "var(--apl-color-green-matcha-50)",
        "60": "var(--apl-color-green-matcha-60)",
        "70": "var(--apl-color-green-matcha-70)",
        "80": "var(--apl-color-green-matcha-80)",
        "90": "var(--apl-color-green-matcha-90)",
        "95": "var(--apl-color-green-matcha-95)",
        "99": "var(--apl-color-green-matcha-99)",
        "100": "var(--apl-color-green-matcha-100)"
      },
      "lilac-soft": {
        "0": "var(--apl-color-lilac-soft-0)",
        "10": "var(--apl-color-lilac-soft-10)",
        "20": "var(--apl-color-lilac-soft-20)",
        "30": "var(--apl-color-lilac-soft-30)",
        "40": "var(--apl-color-lilac-soft-40)",
        "50": "var(--apl-color-lilac-soft-50)",
        "60": "var(--apl-color-lilac-soft-60)",
        "70": "var(--apl-color-lilac-soft-70)",
        "80": "var(--apl-color-lilac-soft-80)",
        "90": "var(--apl-color-lilac-soft-90)",
        "95": "var(--apl-color-lilac-soft-95)",
        "99": "var(--apl-color-lilac-soft-99)",
        "100": "var(--apl-color-lilac-soft-100)"
      },
      "black-soft": {
        "0": "var(--apl-color-black-soft-0)",
        "10": "var(--apl-color-black-soft-10)",
        "20": "var(--apl-color-black-soft-20)",
        "30": "var(--apl-color-black-soft-30)",
        "40": "var(--apl-color-black-soft-40)",
        "50": "var(--apl-color-black-soft-50)",
        "60": "var(--apl-color-black-soft-60)",
        "70": "var(--apl-color-black-soft-70)",
        "80": "var(--apl-color-black-soft-80)",
        "90": "var(--apl-color-black-soft-90)",
        "100": "var(--apl-color-black-soft-100)"
      },
      "white-soft": {
        "0": "var(--apl-color-white-soft-0)",
        "10": "var(--apl-color-white-soft-10)",
        "20": "var(--apl-color-white-soft-20)",
        "30": "var(--apl-color-white-soft-30)",
        "40": "var(--apl-color-white-soft-40)",
        "50": "var(--apl-color-white-soft-50)",
        "60": "var(--apl-color-white-soft-60)",
        "70": "var(--apl-color-white-soft-70)",
        "80": "var(--apl-color-white-soft-80)",
        "90": "var(--apl-color-white-soft-90)",
        "100": "var(--apl-color-white-soft-100)"
      },
      "primary": {
        "0": "var(--apl-color-primary-0)",
        "10": "var(--apl-color-primary-10)",
        "20": "var(--apl-color-primary-20)",
        "30": "var(--apl-color-primary-30)",
        "40": "var(--apl-color-primary-40)",
        "50": "var(--apl-color-primary-50)",
        "60": "var(--apl-color-primary-60)",
        "70": "var(--apl-color-primary-70)",
        "80": "var(--apl-color-primary-80)",
        "90": "var(--apl-color-primary-90)",
        "95": "var(--apl-color-primary-95)",
        "99": "var(--apl-color-primary-99)",
        "100": "var(--apl-color-primary-100)"
      },
      "secondary": {
        "0": "var(--apl-color-secondary-0)",
        "10": "var(--apl-color-secondary-10)",
        "20": "var(--apl-color-secondary-20)",
        "30": "var(--apl-color-secondary-30)",
        "40": "var(--apl-color-secondary-40)",
        "50": "var(--apl-color-secondary-50)",
        "60": "var(--apl-color-secondary-60)",
        "70": "var(--apl-color-secondary-70)",
        "80": "var(--apl-color-secondary-80)",
        "90": "var(--apl-color-secondary-90)",
        "95": "var(--apl-color-secondary-95)",
        "99": "var(--apl-color-secondary-99)",
        "100": "var(--apl-color-secondary-100)"
      },
      "tertiary": {
        "0": "var(--apl-color-tertiary-0)",
        "10": "var(--apl-color-tertiary-10)",
        "20": "var(--apl-color-tertiary-20)",
        "30": "var(--apl-color-tertiary-30)",
        "40": "var(--apl-color-tertiary-40)",
        "50": "var(--apl-color-tertiary-50)",
        "60": "var(--apl-color-tertiary-60)",
        "70": "var(--apl-color-tertiary-70)",
        "80": "var(--apl-color-tertiary-80)",
        "90": "var(--apl-color-tertiary-90)",
        "95": "var(--apl-color-tertiary-95)",
        "99": "var(--apl-color-tertiary-99)",
        "100": "var(--apl-color-tertiary-100)"
      },
      "neutral": {
        "0": "var(--apl-color-neutral-0)",
        "10": "var(--apl-color-neutral-10)",
        "20": "var(--apl-color-neutral-20)",
        "30": "var(--apl-color-neutral-30)",
        "40": "var(--apl-color-neutral-40)",
        "50": "var(--apl-color-neutral-50)",
        "60": "var(--apl-color-neutral-60)",
        "70": "var(--apl-color-neutral-70)",
        "80": "var(--apl-color-neutral-80)",
        "90": "var(--apl-color-neutral-90)",
        "95": "var(--apl-color-neutral-95)",
        "99": "var(--apl-color-neutral-99)",
        "100": "var(--apl-color-neutral-100)"
      },
      "danger": {
        "0": "var(--apl-color-danger-0)",
        "10": "var(--apl-color-danger-10)",
        "20": "var(--apl-color-danger-20)",
        "30": "var(--apl-color-danger-30)",
        "40": "var(--apl-color-danger-40)",
        "50": "var(--apl-color-danger-50)",
        "60": "var(--apl-color-danger-60)",
        "70": "var(--apl-color-danger-70)",
        "80": "var(--apl-color-danger-80)",
        "90": "var(--apl-color-danger-90)",
        "95": "var(--apl-color-danger-95)",
        "99": "var(--apl-color-danger-99)",
        "100": "var(--apl-color-danger-100)"
      },
      "warning": {
        "0": "var(--apl-color-warning-0)",
        "10": "var(--apl-color-warning-10)",
        "20": "var(--apl-color-warning-20)",
        "30": "var(--apl-color-warning-30)",
        "40": "var(--apl-color-warning-40)",
        "50": "var(--apl-color-warning-50)",
        "60": "var(--apl-color-warning-60)",
        "70": "var(--apl-color-warning-70)",
        "80": "var(--apl-color-warning-80)",
        "90": "var(--apl-color-warning-90)",
        "95": "var(--apl-color-warning-95)",
        "99": "var(--apl-color-warning-99)",
        "100": "var(--apl-color-warning-100)"
      },
      "success": {
        "0": "var(--apl-color-success-0)",
        "10": "var(--apl-color-success-10)",
        "20": "var(--apl-color-success-20)",
        "30": "var(--apl-color-success-30)",
        "40": "var(--apl-color-success-40)",
        "50": "var(--apl-color-success-50)",
        "60": "var(--apl-color-success-60)",
        "70": "var(--apl-color-success-70)",
        "80": "var(--apl-color-success-80)",
        "90": "var(--apl-color-success-90)",
        "95": "var(--apl-color-success-95)",
        "99": "var(--apl-color-success-99)",
        "100": "var(--apl-color-success-100)"
      },
      "overlay-black": {
        "0": "var(--apl-color-overlay-black-0)",
        "10": "var(--apl-color-overlay-black-10)",
        "20": "var(--apl-color-overlay-black-20)",
        "30": "var(--apl-color-overlay-black-30)",
        "40": "var(--apl-color-overlay-black-40)",
        "50": "var(--apl-color-overlay-black-50)",
        "60": "var(--apl-color-overlay-black-60)",
        "70": "var(--apl-color-overlay-black-70)",
        "80": "var(--apl-color-overlay-black-80)",
        "90": "var(--apl-color-overlay-black-90)",
        "100": "var(--apl-color-overlay-black-100)"
      },
      "overlay-white": {
        "0": "var(--apl-color-overlay-white-0)",
        "10": "var(--apl-color-overlay-white-10)",
        "20": "var(--apl-color-overlay-white-20)",
        "30": "var(--apl-color-overlay-white-30)",
        "40": "var(--apl-color-overlay-white-40)",
        "50": "var(--apl-color-overlay-white-50)",
        "60": "var(--apl-color-overlay-white-60)",
        "70": "var(--apl-color-overlay-white-70)",
        "80": "var(--apl-color-overlay-white-80)",
        "90": "var(--apl-color-overlay-white-90)",
        "100": "var(--apl-color-overlay-white-100)"
      },
      "kid-club": {
        "0": "var(--apl-color-kid-club-0)",
        "color": "var(--apl-color-kid-club-color)",
        "color-2": "var(--apl-color-kid-club-color-2)",
        "color-3": "var(--apl-color-kid-club-color-3)",
        "color-4": "var(--apl-color-kid-club-color-4)",
        "color-5": "var(--apl-color-kid-club-color-5)",
        "color-6": "var(--apl-color-kid-club-color-6)",
        "color-7": "var(--apl-color-kid-club-color-7)",
        "color-8": "var(--apl-color-kid-club-color-8)",
        "color-9": "var(--apl-color-kid-club-color-9)",
        "color-10": "var(--apl-color-kid-club-color-10)",
        "color-11": "var(--apl-color-kid-club-color-11)",
        "color-12": "var(--apl-color-kid-club-color-12)"
      },
      "m3-elevation": {
        "elevation-1": "var(--apl-base-color-m3-elevation-elevation-1)"
      }
    },
    "spacing": {
      "2": "var(--apl-base-spacing-2)",
      "4": "var(--apl-base-spacing-4)",
      "6": "var(--apl-base-spacing-6)",
      "8": "var(--apl-base-spacing-8)",
      "10": "var(--apl-base-spacing-10)",
      "12": "var(--apl-base-spacing-12)",
      "14": "var(--apl-base-spacing-14)",
      "16": "var(--apl-base-spacing-16)",
      "18": "var(--apl-base-spacing-18)",
      "20": "var(--apl-base-spacing-20)",
      "21": "var(--apl-base-spacing-21)",
      "22": "var(--apl-base-spacing-22)",
      "24": "var(--apl-base-spacing-24)",
      "26": "var(--apl-base-spacing-26)",
      "28": "var(--apl-base-spacing-28)",
      "30": "var(--apl-base-spacing-30)",
      "32": "var(--apl-base-spacing-32)",
      "34": "var(--apl-base-spacing-34)",
      "36": "var(--apl-base-spacing-36)",
      "38": "var(--apl-base-spacing-38)",
      "40": "var(--apl-base-spacing-40)",
      "42": "var(--apl-base-spacing-42)",
      "44": "var(--apl-base-spacing-44)",
      "46": "var(--apl-base-spacing-46)",
      "48": "var(--apl-base-spacing-48)",
      "50": "var(--apl-base-spacing-50)",
      "52": "var(--apl-base-spacing-52)",
      "54": "var(--apl-base-spacing-54)",
      "56": "var(--apl-base-spacing-56)",
      "58": "var(--apl-base-spacing-58)",
      "60": "var(--apl-base-spacing-60)",
      "62": "var(--apl-base-spacing-62)",
      "64": "var(--apl-base-spacing-64)",
      "66": "var(--apl-base-spacing-66)",
      "68": "var(--apl-base-spacing-68)",
      "70": "var(--apl-base-spacing-70)",
      "72": "var(--apl-base-spacing-72)",
      "74": "var(--apl-base-spacing-74)",
      "76": "var(--apl-base-spacing-76)",
      "78": "var(--apl-base-spacing-78)",
      "80": "var(--apl-base-spacing-80)",
      "82": "var(--apl-base-spacing-82)",
      "84": "var(--apl-base-spacing-84)",
      "86": "var(--apl-base-spacing-86)",
      "88": "var(--apl-base-spacing-88)",
      "90": "var(--apl-base-spacing-90)",
      "92": "var(--apl-base-spacing-92)",
      "94": "var(--apl-base-spacing-94)",
      "96": "var(--apl-base-spacing-96)",
      "98": "var(--apl-base-spacing-98)",
      "100": "var(--apl-base-spacing-100)",
      "102": "var(--apl-base-spacing-102)",
      "104": "var(--apl-base-spacing-104)",
      "106": "var(--apl-base-spacing-106)",
      "108": "var(--apl-base-spacing-108)",
      "110": "var(--apl-base-spacing-110)",
      "112": "var(--apl-base-spacing-112)",
      "114": "var(--apl-base-spacing-114)",
      "116": "var(--apl-base-spacing-116)",
      "118": "var(--apl-base-spacing-118)",
      "120": "var(--apl-base-spacing-120)",
      "122": "var(--apl-base-spacing-122)",
      "124": "var(--apl-base-spacing-124)",
      "126": "var(--apl-base-spacing-126)",
      "128": "var(--apl-base-spacing-128)",
      "none": "var(--apl-base-spacing-none)",
      "space1": "var(--apl-base-spacing-space1)",
      "space2": "var(--apl-base-spacing-space2)",
      "space3": "var(--apl-base-spacing-space3)",
      "space4": "var(--apl-base-spacing-space4)",
      "space5": "var(--apl-base-spacing-space5)",
      "space6": "var(--apl-base-spacing-space6)",
      "space7": "var(--apl-base-spacing-space7)",
      "space8": "var(--apl-base-spacing-space8)",
      "space9": "var(--apl-base-spacing-space9)",
      "space10": "var(--apl-base-spacing-space10)",
      "space11": "var(--apl-base-spacing-space11)",
      "space12": "var(--apl-base-spacing-space12)",
      "space13": "var(--apl-base-spacing-space13)",
      "space14": "var(--apl-base-spacing-space14)",
      "space15": "var(--apl-base-spacing-space15)",
      "neg-space1": "var(--apl-base-spacing-neg-space1)",
      "neg-space2": "var(--apl-base-spacing-neg-space2)",
      "neg-space3": "var(--apl-base-spacing-neg-space3)",
      "neg-space4": "var(--apl-base-spacing-neg-space4)",
      "alias-color": {
        "tertiary": {
          "tertiary": "var(--apl-base-spacing-alias-color-tertiary-tertiary)"
        }
      },
      "title": {
        "large": {
          "font-size": "var(--apl-base-spacing-title-large-font-size)",
          "line-height": "var(--apl-base-spacing-title-large-line-height)",
          "letter-spacing": "var(--apl-base-spacing-title-large-letter-spacing)"
        }
      }
    },
    "typography": {
      "font-family": {
        "ibm-plex-sans-thai": "var(--apl-base-typography-font-family-ibm-plex-sans-thai)"
      },
      "font-size": {
        "4": "var(--apl-base-typography-font-size-4)",
        "6": "var(--apl-base-typography-font-size-6)",
        "8": "var(--apl-base-typography-font-size-8)",
        "10": "var(--apl-base-typography-font-size-10)",
        "12": "var(--apl-base-typography-font-size-12)",
        "14": "var(--apl-base-typography-font-size-14)",
        "15": "var(--apl-base-typography-font-size-15)",
        "16": "var(--apl-base-typography-font-size-16)",
        "18": "var(--apl-base-typography-font-size-18)",
        "20": "var(--apl-base-typography-font-size-20)",
        "21": "var(--apl-base-typography-font-size-21)",
        "22": "var(--apl-base-typography-font-size-22)",
        "24": "var(--apl-base-typography-font-size-24)",
        "26": "var(--apl-base-typography-font-size-26)",
        "27": "var(--apl-base-typography-font-size-27)",
        "28": "var(--apl-base-typography-font-size-28)",
        "30": "var(--apl-base-typography-font-size-30)",
        "32": "var(--apl-base-typography-font-size-32)",
        "33": "var(--apl-base-typography-font-size-33)",
        "34": "var(--apl-base-typography-font-size-34)",
        "36": "var(--apl-base-typography-font-size-36)",
        "38": "var(--apl-base-typography-font-size-38)",
        "40": "var(--apl-base-typography-font-size-40)",
        "42": "var(--apl-base-typography-font-size-42)",
        "44": "var(--apl-base-typography-font-size-44)",
        "45": "var(--apl-base-typography-font-size-45)",
        "46": "var(--apl-base-typography-font-size-46)",
        "47": "var(--apl-base-typography-font-size-47)",
        "48": "var(--apl-base-typography-font-size-48)",
        "50": "var(--apl-base-typography-font-size-50)",
        "52": "var(--apl-base-typography-font-size-52)",
        "54": "var(--apl-base-typography-font-size-54)",
        "56": "var(--apl-base-typography-font-size-56)",
        "57": "var(--apl-base-typography-font-size-57)",
        "58": "var(--apl-base-typography-font-size-58)",
        "60": "var(--apl-base-typography-font-size-60)",
        "62": "var(--apl-base-typography-font-size-62)",
        "64": "var(--apl-base-typography-font-size-64)",
        "66": "var(--apl-base-typography-font-size-66)",
        "68": "var(--apl-base-typography-font-size-68)",
        "70": "var(--apl-base-typography-font-size-70)",
        "72": "var(--apl-base-typography-font-size-72)",
        "74": "var(--apl-base-typography-font-size-74)",
        "76": "var(--apl-base-typography-font-size-76)",
        "78": "var(--apl-base-typography-font-size-78)",
        "80": "var(--apl-base-typography-font-size-80)",
        "82": "var(--apl-base-typography-font-size-82)",
        "84": "var(--apl-base-typography-font-size-84)",
        "85": "var(--apl-base-typography-font-size-85)",
        "86": "var(--apl-base-typography-font-size-86)",
        "88": "var(--apl-base-typography-font-size-88)",
        "67-5": "var(--apl-base-typography-font-size-67-5)",
        "3xl": "var(--apl-base-typography-font-size-3xl)",
        "2xl": "var(--apl-base-typography-font-size-2xl)",
        "xl": "var(--apl-base-typography-font-size-xl)",
        "lg": "var(--apl-base-typography-font-size-lg)",
        "md": "var(--apl-base-typography-font-size-md)",
        "sm": "var(--apl-base-typography-font-size-sm)",
        "xs-plus": "var(--apl-base-typography-font-size-xs-plus)",
        "xs-md": "var(--apl-base-typography-font-size-xs-md)",
        "xs-sm": "var(--apl-base-typography-font-size-xs-sm)",
        "xs": "var(--apl-base-typography-font-size-xs)",
        "2xs": "var(--apl-base-typography-font-size-2xs)",
        "3xs": "var(--apl-base-typography-font-size-3xs)",
        "4xs": "var(--apl-base-typography-font-size-4xs)"
      },
      "font-weight": {
        "thin-it": "var(--apl-base-typography-font-weight-thin-it)",
        "thin": "var(--apl-base-typography-font-weight-thin)",
        "extra-light": "var(--apl-base-typography-font-weight-extra-light)",
        "light": "var(--apl-base-typography-font-weight-light)",
        "regular": "var(--apl-base-typography-font-weight-regular)",
        "medium-it": "var(--apl-base-typography-font-weight-medium-it)",
        "medium": "var(--apl-base-typography-font-weight-medium)",
        "semi-bold": "var(--apl-base-typography-font-weight-semi-bold)",
        "bold-it": "var(--apl-base-typography-font-weight-bold-it)",
        "bold": "var(--apl-base-typography-font-weight-bold)",
        "black-it": "var(--apl-base-typography-font-weight-black-it)",
        "black": "var(--apl-base-typography-font-weight-black)",
        "weight1": "var(--apl-base-typography-font-weight-weight1)",
        "weight2": "var(--apl-base-typography-font-weight-weight2)",
        "weight3": "var(--apl-base-typography-font-weight-weight3)"
      },
      "font-spacing": {
        "4": "var(--apl-base-typography-font-spacing-4)",
        "6": "var(--apl-base-typography-font-spacing-6)",
        "8": "var(--apl-base-typography-font-spacing-8)",
        "10": "var(--apl-base-typography-font-spacing-10)",
        "12": "var(--apl-base-typography-font-spacing-12)",
        "14": "var(--apl-base-typography-font-spacing-14)",
        "16": "var(--apl-base-typography-font-spacing-16)",
        "18": "var(--apl-base-typography-font-spacing-18)",
        "20": "var(--apl-base-typography-font-spacing-20)",
        "22": "var(--apl-base-typography-font-spacing-22)",
        "24": "var(--apl-base-typography-font-spacing-24)",
        "26": "var(--apl-base-typography-font-spacing-26)",
        "28": "var(--apl-base-typography-font-spacing-28)",
        "30": "var(--apl-base-typography-font-spacing-30)",
        "32": "var(--apl-base-typography-font-spacing-32)",
        "34": "var(--apl-base-typography-font-spacing-34)",
        "36": "var(--apl-base-typography-font-spacing-36)",
        "38": "var(--apl-base-typography-font-spacing-38)",
        "40": "var(--apl-base-typography-font-spacing-40)",
        "42": "var(--apl-base-typography-font-spacing-42)",
        "44": "var(--apl-base-typography-font-spacing-44)",
        "46": "var(--apl-base-typography-font-spacing-46)",
        "48": "var(--apl-base-typography-font-spacing-48)",
        "50": "var(--apl-base-typography-font-spacing-50)",
        "52": "var(--apl-base-typography-font-spacing-52)",
        "54": "var(--apl-base-typography-font-spacing-54)",
        "56": "var(--apl-base-typography-font-spacing-56)",
        "58": "var(--apl-base-typography-font-spacing-58)",
        "60": "var(--apl-base-typography-font-spacing-60)",
        "62": "var(--apl-base-typography-font-spacing-62)",
        "64": "var(--apl-base-typography-font-spacing-64)",
        "66": "var(--apl-base-typography-font-spacing-66)",
        "68": "var(--apl-base-typography-font-spacing-68)",
        "70": "var(--apl-base-typography-font-spacing-70)",
        "72": "var(--apl-base-typography-font-spacing-72)",
        "74": "var(--apl-base-typography-font-spacing-74)",
        "76": "var(--apl-base-typography-font-spacing-76)",
        "78": "var(--apl-base-typography-font-spacing-78)",
        "80": "var(--apl-base-typography-font-spacing-80)",
        "82": "var(--apl-base-typography-font-spacing-82)",
        "84": "var(--apl-base-typography-font-spacing-84)",
        "86": "var(--apl-base-typography-font-spacing-86)",
        "88": "var(--apl-base-typography-font-spacing-88)"
      },
      "line-height": {
        "4": "var(--apl-base-typography-line-height-4)",
        "6": "var(--apl-base-typography-line-height-6)",
        "8": "var(--apl-base-typography-line-height-8)",
        "10": "var(--apl-base-typography-line-height-10)",
        "12": "var(--apl-base-typography-line-height-12)",
        "14": "var(--apl-base-typography-line-height-14)",
        "15": "var(--apl-base-typography-line-height-15)",
        "16": "var(--apl-base-typography-line-height-16)",
        "18": "var(--apl-base-typography-line-height-18)",
        "20": "var(--apl-base-typography-line-height-20)",
        "21": "var(--apl-base-typography-line-height-21)",
        "22": "var(--apl-base-typography-line-height-22)",
        "24": "var(--apl-base-typography-line-height-24)",
        "26": "var(--apl-base-typography-line-height-26)",
        "27": "var(--apl-base-typography-line-height-27)",
        "28": "var(--apl-base-typography-line-height-28)",
        "30": "var(--apl-base-typography-line-height-30)",
        "32": "var(--apl-base-typography-line-height-32)",
        "33": "var(--apl-base-typography-line-height-33)",
        "34": "var(--apl-base-typography-line-height-34)",
        "36": "var(--apl-base-typography-line-height-36)",
        "38": "var(--apl-base-typography-line-height-38)",
        "40": "var(--apl-base-typography-line-height-40)",
        "42": "var(--apl-base-typography-line-height-42)",
        "44": "var(--apl-base-typography-line-height-44)",
        "46": "var(--apl-base-typography-line-height-46)",
        "48": "var(--apl-base-typography-line-height-48)",
        "50": "var(--apl-base-typography-line-height-50)",
        "52": "var(--apl-base-typography-line-height-52)",
        "54": "var(--apl-base-typography-line-height-54)",
        "56": "var(--apl-base-typography-line-height-56)",
        "58": "var(--apl-base-typography-line-height-58)",
        "60": "var(--apl-base-typography-line-height-60)",
        "62": "var(--apl-base-typography-line-height-62)",
        "64": "var(--apl-base-typography-line-height-64)",
        "66": "var(--apl-base-typography-line-height-66)",
        "67": "var(--apl-base-typography-line-height-67)",
        "68": "var(--apl-base-typography-line-height-68)",
        "70": "var(--apl-base-typography-line-height-70)",
        "72": "var(--apl-base-typography-line-height-72)",
        "74": "var(--apl-base-typography-line-height-74)",
        "76": "var(--apl-base-typography-line-height-76)",
        "78": "var(--apl-base-typography-line-height-78)",
        "80": "var(--apl-base-typography-line-height-80)",
        "82": "var(--apl-base-typography-line-height-82)",
        "84": "var(--apl-base-typography-line-height-84)",
        "85": "var(--apl-base-typography-line-height-85)",
        "86": "var(--apl-base-typography-line-height-86)",
        "88": "var(--apl-base-typography-line-height-88)",
        "96": "var(--apl-base-typography-line-height-96)",
        "132": "var(--apl-base-typography-line-height-132)",
        "line-height1": "var(--apl-base-typography-line-height-line-height1)",
        "line-height2": "var(--apl-base-typography-line-height-line-height2)",
        "line-height3": "var(--apl-base-typography-line-height-line-height3)",
        "line-height4": "var(--apl-base-typography-line-height-line-height4)",
        "line-height5": "var(--apl-base-typography-line-height-line-height5)",
        "line-height6": "var(--apl-base-typography-line-height-line-height6)",
        "line-height7": "var(--apl-base-typography-line-height-line-height7)",
        "line-height8": "var(--apl-base-typography-line-height-line-height8)",
        "line-height9": "var(--apl-base-typography-line-height-line-height9)",
        "line-height10": "var(--apl-base-typography-line-height-line-height10)",
        "line-height11": "var(--apl-base-typography-line-height-line-height11)",
        "line-height12": "var(--apl-base-typography-line-height-line-height12)",
        "line-height13": "var(--apl-base-typography-line-height-line-height13)",
        "line-height14": "var(--apl-base-typography-line-height-line-height14)",
        "line-height15": "var(--apl-base-typography-line-height-line-height15)",
        "line-height16": "var(--apl-base-typography-line-height-line-height16)",
        "line-height17": "var(--apl-base-typography-line-height-line-height17)",
        "line-height18": "var(--apl-base-typography-line-height-line-height18)",
        "line-height19": "var(--apl-base-typography-line-height-line-height19)",
        "line-height20": "var(--apl-base-typography-line-height-line-height20)",
        "line-height21": "var(--apl-base-typography-line-height-line-height21)",
        "line-height22": "var(--apl-base-typography-line-height-line-height22)",
        "line-height23": "var(--apl-base-typography-line-height-line-height23)",
        "line-height24": "var(--apl-base-typography-line-height-line-height24)",
        "line-height25": "var(--apl-base-typography-line-height-line-height25)",
        "line-height26": "var(--apl-base-typography-line-height-line-height26)",
        "line-height27": "var(--apl-base-typography-line-height-line-height27)",
        "line-height28": "var(--apl-base-typography-line-height-line-height28)",
        "line-height29": "var(--apl-base-typography-line-height-line-height29)",
        "line-height30": "var(--apl-base-typography-line-height-line-height30)",
        "line-height31": "var(--apl-base-typography-line-height-line-height31)",
        "line-height32": "var(--apl-base-typography-line-height-line-height32)",
        "line-height33": "var(--apl-base-typography-line-height-line-height33)",
        "line-height34": "var(--apl-base-typography-line-height-line-height34)",
        "line-height35": "var(--apl-base-typography-line-height-line-height35)",
        "line-height36": "var(--apl-base-typography-line-height-line-height36)",
        "line-height37": "var(--apl-base-typography-line-height-line-height37)",
        "line-height38": "var(--apl-base-typography-line-height-line-height38)",
        "line-height39": "var(--apl-base-typography-line-height-line-height39)",
        "line-height40": "var(--apl-base-typography-line-height-line-height40)",
        "line-height41": "var(--apl-base-typography-line-height-line-height41)",
        "line-height42": "var(--apl-base-typography-line-height-line-height42)",
        "line-height43": "var(--apl-base-typography-line-height-line-height43)",
        "line-height44": "var(--apl-base-typography-line-height-line-height44)",
        "line-height45": "var(--apl-base-typography-line-height-line-height45)",
        "line-height46": "var(--apl-base-typography-line-height-line-height46)",
        "line-height47": "var(--apl-base-typography-line-height-line-height47)",
        "line-height48": "var(--apl-base-typography-line-height-line-height48)",
        "line-height49": "var(--apl-base-typography-line-height-line-height49)",
        "line-height50": "var(--apl-base-typography-line-height-line-height50)",
        "line-height51": "var(--apl-base-typography-line-height-line-height51)"
      },
      "m3": {
        "display": {
          "large": "var(--apl-base-typography-m3-display-large)",
          "medium": "var(--apl-base-typography-m3-display-medium)",
          "small": "var(--apl-base-typography-m3-display-small)"
        },
        "headerline": {
          "large": "var(--apl-base-typography-m3-headerline-large)",
          "medium": "var(--apl-base-typography-m3-headerline-medium)",
          "small": "var(--apl-base-typography-m3-headerline-small)"
        },
        "title": {
          "large": "var(--apl-base-typography-m3-title-large)",
          "medium": "var(--apl-base-typography-m3-title-medium)",
          "small": "var(--apl-base-typography-m3-title-small)"
        },
        "body": {
          "large": "var(--apl-base-typography-m3-body-large)",
          "large-emphasized": "var(--apl-base-typography-m3-body-large-emphasized)",
          "medium": "var(--apl-base-typography-m3-body-medium)",
          "medium-emphasized": "var(--apl-base-typography-m3-body-medium-emphasized)",
          "small": "var(--apl-base-typography-m3-body-small)",
          "small-emphasized": "var(--apl-base-typography-m3-body-small-emphasized)"
        },
        "label": {
          "large-emphasized": "var(--apl-base-typography-m3-label-large-emphasized)",
          "large": "var(--apl-base-typography-m3-label-large)",
          "medium": "var(--apl-base-typography-m3-label-medium)",
          "medium-emphasized": "var(--apl-base-typography-m3-label-medium-emphasized)",
          "small": "var(--apl-base-typography-m3-label-small)",
          "small-emphasized": "var(--apl-base-typography-m3-label-small-emphasized)"
        }
      }
    },
    "borderRadius": {
      "2": "var(--apl-base-borderRadiu-2)",
      "4": "var(--apl-base-borderRadiu-4)",
      "6": "var(--apl-base-borderRadiu-6)",
      "8": "var(--apl-base-borderRadiu-8)",
      "10": "var(--apl-base-borderRadiu-10)",
      "12": "var(--apl-base-borderRadiu-12)",
      "14": "var(--apl-base-borderRadiu-14)",
      "16": "var(--apl-base-borderRadiu-16)",
      "18": "var(--apl-base-borderRadiu-18)",
      "20": "var(--apl-base-borderRadiu-20)",
      "22": "var(--apl-base-borderRadiu-22)",
      "24": "var(--apl-base-borderRadiu-24)",
      "26": "var(--apl-base-borderRadiu-26)",
      "28": "var(--apl-base-borderRadiu-28)",
      "30": "var(--apl-base-borderRadiu-30)",
      "32": "var(--apl-base-borderRadiu-32)",
      "34": "var(--apl-base-borderRadiu-34)",
      "36": "var(--apl-base-borderRadiu-36)",
      "38": "var(--apl-base-borderRadiu-38)",
      "40": "var(--apl-base-borderRadiu-40)",
      "42": "var(--apl-base-borderRadiu-42)",
      "44": "var(--apl-base-borderRadiu-44)",
      "46": "var(--apl-base-borderRadiu-46)",
      "48": "var(--apl-base-borderRadiu-48)",
      "50": "var(--apl-base-borderRadiu-50)",
      "52": "var(--apl-base-borderRadiu-52)",
      "54": "var(--apl-base-borderRadiu-54)",
      "56": "var(--apl-base-borderRadiu-56)",
      "58": "var(--apl-base-borderRadiu-58)",
      "60": "var(--apl-base-borderRadiu-60)",
      "62": "var(--apl-base-borderRadiu-62)",
      "64": "var(--apl-base-borderRadiu-64)",
      "66": "var(--apl-base-borderRadiu-66)",
      "68": "var(--apl-base-borderRadiu-68)",
      "70": "var(--apl-base-borderRadiu-70)",
      "72": "var(--apl-base-borderRadiu-72)",
      "74": "var(--apl-base-borderRadiu-74)",
      "76": "var(--apl-base-borderRadiu-76)",
      "78": "var(--apl-base-borderRadiu-78)",
      "80": "var(--apl-base-borderRadiu-80)",
      "none": "var(--apl-base-borderRadiu-none)",
      "full": "var(--apl-base-borderRadiu-full)"
    },
    "boxShadow": {
      "x-axis": {
        "1": "var(--apl-base-boxShadow-x-axis-1)",
        "2": "var(--apl-base-boxShadow-x-axis-2)",
        "4": "var(--apl-base-boxShadow-x-axis-4)",
        "6": "var(--apl-base-boxShadow-x-axis-6)",
        "8": "var(--apl-base-boxShadow-x-axis-8)",
        "10": "var(--apl-base-boxShadow-x-axis-10)",
        "12": "var(--apl-base-boxShadow-x-axis-12)",
        "14": "var(--apl-base-boxShadow-x-axis-14)",
        "16": "var(--apl-base-boxShadow-x-axis-16)",
        "18": "var(--apl-base-boxShadow-x-axis-18)",
        "20": "var(--apl-base-boxShadow-x-axis-20)",
        "22": "var(--apl-base-boxShadow-x-axis-22)",
        "24": "var(--apl-base-boxShadow-x-axis-24)",
        "none": "var(--apl-base-boxShadow-x-axis-none)"
      },
      "y-axis": {
        "1": "var(--apl-base-boxShadow-y-axis-1)",
        "2": "var(--apl-base-boxShadow-y-axis-2)",
        "4": "var(--apl-base-boxShadow-y-axis-4)",
        "6": "var(--apl-base-boxShadow-y-axis-6)",
        "8": "var(--apl-base-boxShadow-y-axis-8)",
        "10": "var(--apl-base-boxShadow-y-axis-10)",
        "12": "var(--apl-base-boxShadow-y-axis-12)",
        "14": "var(--apl-base-boxShadow-y-axis-14)",
        "16": "var(--apl-base-boxShadow-y-axis-16)",
        "18": "var(--apl-base-boxShadow-y-axis-18)",
        "20": "var(--apl-base-boxShadow-y-axis-20)",
        "22": "var(--apl-base-boxShadow-y-axis-22)",
        "24": "var(--apl-base-boxShadow-y-axis-24)",
        "none": "var(--apl-base-boxShadow-y-axis-none)"
      },
      "spread": {
        "1": "var(--apl-base-boxShadow-spread-1)",
        "2": "var(--apl-base-boxShadow-spread-2)",
        "4": "var(--apl-base-boxShadow-spread-4)",
        "6": "var(--apl-base-boxShadow-spread-6)",
        "8": "var(--apl-base-boxShadow-spread-8)",
        "10": "var(--apl-base-boxShadow-spread-10)",
        "12": "var(--apl-base-boxShadow-spread-12)",
        "14": "var(--apl-base-boxShadow-spread-14)",
        "16": "var(--apl-base-boxShadow-spread-16)",
        "18": "var(--apl-base-boxShadow-spread-18)",
        "20": "var(--apl-base-boxShadow-spread-20)",
        "22": "var(--apl-base-boxShadow-spread-22)",
        "24": "var(--apl-base-boxShadow-spread-24)",
        "none": "var(--apl-base-boxShadow-spread-none)"
      },
      "blur": {
        "2": "var(--apl-base-boxShadow-blur-2)",
        "4": "var(--apl-base-boxShadow-blur-4)",
        "6": "var(--apl-base-boxShadow-blur-6)",
        "8": "var(--apl-base-boxShadow-blur-8)",
        "10": "var(--apl-base-boxShadow-blur-10)",
        "12": "var(--apl-base-boxShadow-blur-12)",
        "14": "var(--apl-base-boxShadow-blur-14)",
        "16": "var(--apl-base-boxShadow-blur-16)",
        "18": "var(--apl-base-boxShadow-blur-18)",
        "20": "var(--apl-base-boxShadow-blur-20)",
        "22": "var(--apl-base-boxShadow-blur-22)",
        "24": "var(--apl-base-boxShadow-blur-24)",
        "26": "var(--apl-base-boxShadow-blur-26)",
        "28": "var(--apl-base-boxShadow-blur-28)",
        "30": "var(--apl-base-boxShadow-blur-30)",
        "32": "var(--apl-base-boxShadow-blur-32)",
        "none": "var(--apl-base-boxShadow-blur-none)"
      }
    }
  },
  "alias": {
    "colors": {
      "primary": {
        "primary": "var(--apl-alias-color-primary-primary)",
        "surface-tint": "var(--apl-alias-color-primary-surface-tint)",
        "on-primary": "var(--apl-alias-color-primary-on-primary)",
        "primary-container": "var(--apl-alias-color-primary-primary-container)",
        "hovered": "var(--apl-alias-color-primary-hovered)",
        "focused": "var(--apl-alias-color-primary-focused)",
        "pressed": "var(--apl-alias-color-primary-pressed)",
        "text-only-background-hovered": "var(--apl-alias-color-primary-text-only-background-hovered)"
      },
      "extended": {
        "error": {
          "error": "var(--apl-alias-color-extended-error-error)",
          "on-error": "var(--apl-alias-color-extended-error-on-error)",
          "error-container": "var(--apl-alias-color-extended-error-error-container)",
          "on-error-container": "var(--apl-alias-color-extended-error-on-error-container)",
          "text-icon-disabled": "var(--apl-alias-color-extended-error-text-icon-disabled)",
          "container-disabled": "var(--apl-alias-color-extended-error-container-disabled)",
          "hovered": "var(--apl-alias-color-extended-error-hovered)",
          "focused": "var(--apl-alias-color-extended-error-focused)",
          "pressed": "var(--apl-alias-color-extended-error-pressed)",
          "disable": "var(--apl-alias-color-extended-error-disable)",
          "text-only-background-hovered": "var(--apl-alias-color-extended-error-text-only-background-hovered)"
        },
        "warning": {
          "warning": "var(--apl-alias-color-extended-warning-warning)",
          "on-warning": "var(--apl-alias-color-extended-warning-on-warning)",
          "warning-container": "var(--apl-alias-color-extended-warning-warning-container)",
          "on-warning-container": "var(--apl-alias-color-extended-warning-on-warning-container)"
        },
        "success": {
          "success": "var(--apl-alias-color-extended-success-success)",
          "on-success": "var(--apl-alias-color-extended-success-on-success)",
          "success-container": "var(--apl-alias-color-extended-success-success-container)",
          "on-success-container": "var(--apl-alias-color-extended-success-on-success-container)"
        }
      },
      "secondary": {
        "secondary": "var(--apl-alias-color-secondary-secondary)",
        "on-secondary": "var(--apl-alias-color-secondary-on-secondary)",
        "secondary-container": "var(--apl-alias-color-secondary-secondary-container)",
        "on-secondary-container": "var(--apl-alias-color-secondary-on-secondary-container)"
      },
      "tertiary": {
        "tertiary": "var(--apl-alias-color-tertiary-tertiary)",
        "on-tertiary": "var(--apl-alias-color-tertiary-on-tertiary)",
        "tertiary-container": "var(--apl-alias-color-tertiary-tertiary-container)",
        "on-tertiary-container": "var(--apl-alias-color-tertiary-on-tertiary-container)"
      },
      "background-and-surface": {
        "background": "var(--apl-alias-color-background-and-surface-background)",
        "on-background": "var(--apl-alias-color-background-and-surface-on-background)",
        "surface": "var(--apl-alias-color-background-and-surface-surface)",
        "on-surface": "var(--apl-alias-color-background-and-surface-on-surface)",
        "surface-variant": "var(--apl-alias-color-background-and-surface-surface-variant)",
        "on-surface-variant": "var(--apl-alias-color-background-and-surface-on-surface-variant)",
        "text-icon-disabled": "var(--apl-alias-color-background-and-surface-text-icon-disabled)",
        "container-disabled": "var(--apl-alias-color-background-and-surface-container-disabled)"
      },
      "outline-and-border": {
        "outline": "var(--apl-alias-color-outline-and-border-outline)",
        "outline-variant": "var(--apl-alias-color-outline-and-border-outline-variant)",
        "border": "var(--apl-alias-color-outline-and-border-border)",
        "border-disabled": "var(--apl-alias-color-outline-and-border-border-disabled)"
      },
      "effects": {
        "shadow": "var(--apl-alias-color-effects-shadow)",
        "scrim": "var(--apl-alias-color-effects-scrim)",
        "overlay-surface-white": "var(--apl-alias-color-effects-overlay-surface-white)",
        "overlay-surface-black": "var(--apl-alias-color-effects-overlay-surface-black)",
        "overlay-surface-disabled": "var(--apl-alias-color-effects-overlay-surface-disabled)"
      },
      "static": {
        "text-icon": {
          "text-icon-light": "var(--apl-alias-color-static-text-icon-text-icon-light)",
          "text-icon-dark": "var(--apl-alias-color-static-text-icon-text-icon-dark)"
        }
      }
    },
    "typography": {
      "font-family": "var(--apl-alias-typography-font-family)",
      "display": {
        "large": {
          "font-family": "var(--apl-alias-typography-display-large-font-family)",
          "font-weight": "var(--apl-alias-typography-display-large-font-weight)",
          "font-size": "var(--apl-alias-typography-display-large-font-size)",
          "line-height": "var(--apl-alias-typography-display-large-line-height)"
        },
        "medium": {
          "font-family": "var(--apl-alias-typography-display-medium-font-family)",
          "font-weight": "var(--apl-alias-typography-display-medium-font-weight)",
          "font-size": "var(--apl-alias-typography-display-medium-font-size)",
          "line-height": "var(--apl-alias-typography-display-medium-line-height)"
        },
        "small": {
          "font-family": "var(--apl-alias-typography-display-small-font-family)",
          "font-weight": "var(--apl-alias-typography-display-small-font-weight)",
          "font-size": "var(--apl-alias-typography-display-small-font-size)",
          "line-height": "var(--apl-alias-typography-display-small-line-height)"
        }
      },
      "headline": {
        "large": {
          "font-family": "var(--apl-alias-typography-headline-large-font-family)",
          "font-weight": "var(--apl-alias-typography-headline-large-font-weight)",
          "font-size": "var(--apl-alias-typography-headline-large-font-size)",
          "line-height": "var(--apl-alias-typography-headline-large-line-height)"
        },
        "medium": {
          "font-family": "var(--apl-alias-typography-headline-medium-font-family)",
          "font-weight": "var(--apl-alias-typography-headline-medium-font-weight)",
          "font-size": "var(--apl-alias-typography-headline-medium-font-size)",
          "line-height": "var(--apl-alias-typography-headline-medium-line-height)"
        },
        "small": {
          "font-family": "var(--apl-alias-typography-headline-small-font-family)",
          "font-weight": "var(--apl-alias-typography-headline-small-font-weight)",
          "font-size": "var(--apl-alias-typography-headline-small-font-size)",
          "line-height": "var(--apl-alias-typography-headline-small-line-height)"
        }
      },
      "title": {
        "large": {
          "font-family": "var(--apl-alias-typography-title-large-font-family)",
          "font-weight": "var(--apl-alias-typography-title-large-font-weight)",
          "font-size": "var(--apl-alias-typography-title-large-font-size)",
          "line-height": "var(--apl-alias-typography-title-large-line-height)"
        },
        "medium": {
          "font-family": "var(--apl-alias-typography-title-medium-font-family)",
          "font-weight": "var(--apl-alias-typography-title-medium-font-weight)",
          "font-size": "var(--apl-alias-typography-title-medium-font-size)",
          "line-height": "var(--apl-alias-typography-title-medium-line-height)"
        },
        "small": {
          "font-family": "var(--apl-alias-typography-title-small-font-family)",
          "font-weight": "var(--apl-alias-typography-title-small-font-weight)",
          "font-size": "var(--apl-alias-typography-title-small-font-size)",
          "line-height": "var(--apl-alias-typography-title-small-line-height)"
        }
      },
      "body": {
        "large": {
          "font-family": "var(--apl-alias-typography-body-large-font-family)",
          "font-weight": "var(--apl-alias-typography-body-large-font-weight)",
          "weight-emphasized": "var(--apl-alias-typography-body-large-weight-emphasized)",
          "font-size": "var(--apl-alias-typography-body-large-font-size)",
          "line-height": "var(--apl-alias-typography-body-large-line-height)"
        },
        "medium": {
          "font-family": "var(--apl-alias-typography-body-medium-font-family)",
          "font-weight": "var(--apl-alias-typography-body-medium-font-weight)",
          "weight-emphasized": "var(--apl-alias-typography-body-medium-weight-emphasized)",
          "font-size": "var(--apl-alias-typography-body-medium-font-size)",
          "line-height": "var(--apl-alias-typography-body-medium-line-height)"
        },
        "small": {
          "font-family": "var(--apl-alias-typography-body-small-font-family)",
          "font-weight": "var(--apl-alias-typography-body-small-font-weight)",
          "weight-emphasized": "var(--apl-alias-typography-body-small-weight-emphasized)",
          "font-size": "var(--apl-alias-typography-body-small-font-size)",
          "line-height": "var(--apl-alias-typography-body-small-line-height)"
        }
      },
      "label": {
        "large": {
          "font-family": "var(--apl-alias-typography-label-large-font-family)",
          "font-weight": "var(--apl-alias-typography-label-large-font-weight)",
          "weight-emphasized": "var(--apl-alias-typography-label-large-weight-emphasized)",
          "font-size": "var(--apl-alias-typography-label-large-font-size)",
          "line-height": "var(--apl-alias-typography-label-large-line-height)"
        },
        "medium": {
          "font-family": "var(--apl-alias-typography-label-medium-font-family)",
          "font-weight": "var(--apl-alias-typography-label-medium-font-weight)",
          "weight-emphasized": "var(--apl-alias-typography-label-medium-weight-emphasized)",
          "font-size": "var(--apl-alias-typography-label-medium-font-size)",
          "line-height": "var(--apl-alias-typography-label-medium-line-height)"
        },
        "small": {
          "font-family": "var(--apl-alias-typography-label-small-font-family)",
          "font-weight": "var(--apl-alias-typography-label-small-font-weight)",
          "weight-emphasized": "var(--apl-alias-typography-label-small-weight-emphasized)",
          "font-size": "var(--apl-alias-typography-label-small-font-size)",
          "line-height": "var(--apl-alias-typography-label-small-line-height)"
        }
      }
    },
    "spacing": {
      "margin": {
        "vertical": {
          "vertical": "var(--apl-alias-spacing-margin-vertical-vertical)"
        },
        "horizontal": {
          "horizontal": "var(--apl-alias-spacing-margin-horizontal-horizontal)"
        }
      },
      "padding": {
        "padding1": "var(--apl-alias-spacing-padding-padding1)",
        "padding2": "var(--apl-alias-spacing-padding-padding2)",
        "padding3": "var(--apl-alias-spacing-padding-padding3)",
        "padding4": "var(--apl-alias-spacing-padding-padding4)",
        "padding5": "var(--apl-alias-spacing-padding-padding5)",
        "padding6": "var(--apl-alias-spacing-padding-padding6)",
        "padding7": "var(--apl-alias-spacing-padding-padding7)",
        "padding8": "var(--apl-alias-spacing-padding-padding8)",
        "padding9": "var(--apl-alias-spacing-padding-padding9)",
        "padding10": "var(--apl-alias-spacing-padding-padding10)",
        "padding11": "var(--apl-alias-spacing-padding-padding11)",
        "padding12": "var(--apl-alias-spacing-padding-padding12)"
      },
      "gap": {
        "gap1": "var(--apl-alias-spacing-gap-gap1)",
        "gap2": "var(--apl-alias-spacing-gap-gap2)",
        "gap3": "var(--apl-alias-spacing-gap-gap3)",
        "gap4": "var(--apl-alias-spacing-gap-gap4)",
        "gap5": "var(--apl-alias-spacing-gap-gap5)",
        "gap6": "var(--apl-alias-spacing-gap-gap6)",
        "gap7": "var(--apl-alias-spacing-gap-gap7)",
        "gap8": "var(--apl-alias-spacing-gap-gap8)",
        "gap9": "var(--apl-alias-spacing-gap-gap9)",
        "gap10": "var(--apl-alias-spacing-gap-gap10)",
        "gap11": "var(--apl-alias-spacing-gap-gap11)",
        "gap12": "var(--apl-alias-spacing-gap-gap12)"
      }
    },
    "borderRadius": {
      "radius1": "var(--apl-alias-borderRadiu-radius1)",
      "radius2": "var(--apl-alias-borderRadiu-radius2)",
      "radius3": "var(--apl-alias-borderRadiu-radius3)",
      "radius4": "var(--apl-alias-borderRadiu-radius4)",
      "radius5": "var(--apl-alias-borderRadiu-radius5)",
      "radius6": "var(--apl-alias-borderRadiu-radius6)",
      "radius7": "var(--apl-alias-borderRadiu-radius7)",
      "radius8": "var(--apl-alias-borderRadiu-radius8)",
      "radius9": "var(--apl-alias-borderRadiu-radius9)",
      "radius10": "var(--apl-alias-borderRadiu-radius10)",
      "radius11": "var(--apl-alias-borderRadiu-radius11)"
    },
    "boxShadow": {
      "elevations1": {
        "x-axis": "var(--apl-alias-boxShadow-elevations1-x-axis)",
        "y-axis": "var(--apl-alias-boxShadow-elevations1-y-axis)",
        "blur": "var(--apl-alias-boxShadow-elevations1-blur)",
        "spread": "var(--apl-alias-boxShadow-elevations1-spread)",
        "color": "var(--apl-alias-boxShadow-elevations1-color)"
      },
      "elevations2": {
        "x-axis": "var(--apl-alias-boxShadow-elevations2-x-axis)",
        "y-axis": "var(--apl-alias-boxShadow-elevations2-y-axis)",
        "blur": "var(--apl-alias-boxShadow-elevations2-blur)",
        "spread": "var(--apl-alias-boxShadow-elevations2-spread)",
        "color": "var(--apl-alias-boxShadow-elevations2-color)"
      },
      "elevations3": {
        "x-axis": "var(--apl-alias-boxShadow-elevations3-x-axis)",
        "y-axis": "var(--apl-alias-boxShadow-elevations3-y-axis)",
        "blur": "var(--apl-alias-boxShadow-elevations3-blur)",
        "spread": "var(--apl-alias-boxShadow-elevations3-spread)",
        "color": "var(--apl-alias-boxShadow-elevations3-color)"
      }
    }
  }
};
