import React from "react"
import figma from "@figma/code-connect"

import { Radio, RadioGroup } from "../src"

figma.connect(
  Radio,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=520-7793&m=dev",
  {
    imports: ["import { Radio,RadioGroup } from '@apollo/ui'"],
    props: {
      disabled: figma.enum("state", {
        Enabled: undefined,
        Disable: true,
      }),
      label: figma.string("textRadio"),
    },
    example: (props) => (
      <RadioGroup disabled={props.disabled} name="fruits" defaultValue="1">
        <Radio value="1">{props.label}</Radio>
        <Radio value="2">Unselect Option</Radio>
      </RadioGroup>
    ),
  }
)
