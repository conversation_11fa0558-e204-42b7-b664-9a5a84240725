import React from "react"
import figma from "@figma/code-connect"

import { Autocomplete } from "../src/components/autocomplete/Autocomplete"

figma.connect(
  Autocomplete,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=8986-538&m=dev",
  {
    imports: ["import { Autocomplete } from '@apollo/ui'"],
    props: {
      multiple: figma.boolean("isMultipleValue"),
      label: figma.nestedProps("Label", {
        text: figma.string("Text"),
      }),
      helperText: figma.nestedProps("helper text", {
        text: figma.string("HelperText"),
      }),
      error: figma.enum("State", {
        Error: true,
      }),
      disabled: figma.enum("State", {
        Disabled: true,
      }),
      size: figma.enum("Size", {
        Small: "small",
        Medium: "medium",
      }),
    } as any,
    example: (props) => (
      <Autocomplete
        multiple={props.multiple}
        size={props.size}
        disabled={props.disabled}
        error={props.error}
        label={props?.label.text}
        helperText={props?.helperText.text}
      />
    ),
  }
)
