/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState } from "react"
import figma from "@figma/code-connect"

import { CapsuleTab } from "../src/components/capsule-tab"

figma.connect(
  CapsuleTab,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=8157-3613&m=dev",
  {
    imports: ["import { CapsuleTab } from '@apollo/ui'"],
    example: () => {
      const [selectedTab, setSelectedTab] = useState<number>(0)
      return (
        <CapsuleTab
          tabs={[
            { label: "Tab 1", id: "tab1" },
            { label: "Tab 2", id: "tab2" },
            { label: "Tab 3", id: "tab3" },
          ]}
          selectedIndex={selectedTab}
          onSelect={(index) => setSelectedTab(index)}
        />
      )
    },
  }
)
