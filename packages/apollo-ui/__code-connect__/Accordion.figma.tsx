/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState } from "react"
import figma from "@figma/code-connect"

import { Accordion } from "../src/components/accordion"

figma.connect(
  Accordion,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=11283-2726&m=dev",
  {
    imports: ["import { Accordion } from '@apollo/ui'"],
    example: () => {
      const [open, setOpen] = useState<boolean>(false)
      return (
        <Accordion
          open={open}
          fullWidth
          onOpenChange={(open) => setOpen(open)}
          label="My Accordion"
        >
          <p>Content Is Here!!!</p>
        </Accordion>
      )
    },
  }
)
