import React from "react"
import figma from "@figma/code-connect"

import { Typography } from "../src/components/typography/Typography"

figma.connect(
  Typography,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=11113-11712&m=dev",
  {
    imports: ["import { Typography } from '@apollo/ui'"],
    props: {
      level: figma.enum("Typo", {
        "Display 1": "display1",
        "Display 2": "display2",
        Headline1: "h1",
        Headline2: "h2",
        Headline3: "h3",
        Headline4: "h4",
        Headline5: "h5",
        Body1: "body1",
        Body2: "body2",
        Caption: "caption",
        HyperLink: "textlink", // Assuming HyperLink maps to body-1
      }),
      text: figma.string("Typo"),
    },
    example: (props) => (
      <Typography level={props.level}>{props.text}</Typography>
    ),
  }
)
