import React, { useState } from "react"
import figma from "@figma/code-connect"

import { Switch } from "../src"

/**
 * -- This file was auto-generated by Code Connect --
 * `props` includes a mapping from your code props to Figma properties.
 * You should check this is correct, and update the `example` function
 * to return the code example you'd like to see in Figma
 */

figma.connect(
  Switch,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=888-1843&m=dev",
  {
    imports: ["import { Switch } from '@apollo/ui'"],
    props: {
      disabled: figma.enum("stage", {
        Enabled: undefined,
        Disable: true,
      }),
      checked: figma.boolean("Trun-on"),
      label: figma.nestedProps("Label", {
        text: figma.string("Text"),
      }),
      actionText: figma.string("textSwitch"),
    },
    example: (props) => {
      const [value, setValue] = useState(props?.checked)

      const handleChange = (checked: boolean, event: Event) => {
        setValue(checked)
      }

      return (
        <Switch
          disabled={props.disabled}
          label={props.label.text}
          actionText={props.actionText}
          checked={value}
          onCheckedChange={handleChange}
        />
      )
    },
  }
)
