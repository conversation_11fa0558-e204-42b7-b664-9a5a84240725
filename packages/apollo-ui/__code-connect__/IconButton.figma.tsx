import React from "react"
import figma from "@figma/code-connect"

import { IconButton } from "../src"

figma.connect(
  IconButton,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=9538-17934&m=dev",
  {
    imports: ["import { IconButton } from '@apollo/ui'"],
    props: {
      disabled: figma.enum("State", {
        Disabled: true,
      }),
      children: figma.instance("icon"),
      size: figma.enum("Size", {
        Small: "small",
        Large: "large",
      }),
    },
    example: (props) => (
      <IconButton size={props.size} disabled={props.disabled}>
        {props.children}
      </IconButton>
    ),
  }
)
