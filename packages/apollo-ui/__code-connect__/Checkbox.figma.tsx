import React, { useState } from "react"
import figma from "@figma/code-connect"

import { Checkbox } from "../src"

/**
 * -- This file was auto-generated by Code Connect --
 * `props` includes a mapping from your code props to Figma properties.
 * You should check this is correct, and update the `example` function
 * to return the code example you'd like to see in Figma
 */

figma.connect(
  Checkbox,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=540-7746&m=dev",
  {
    imports: ["import { Checkbox } from '@apollo/ui'"],
    props: {
      disabled: figma.enum("state", {
        Enable: undefined,
        Disable: true,
      }),
      checked: figma.enum("type", {
        Unselect: false,
        Indeterminate: false,
        Select: true,
      }),
      indeterminate: figma.enum("type", {
        Unselect: undefined,
        Select: undefined,
        Indeterminate: true,
      }),
      label: figma.boolean("hasText", {
        true: figma.textContent("label"),
        false: undefined,
      }),
    },
    example: (props) => {
      const [value, setValue] = useState(props?.checked)

      const handleChange = (_: Event, checked: boolean) => {
        setValue(checked)
      }

      return (
        <Checkbox
          indeterminate={props.indeterminate}
          disabled={props.disabled}
          label={props.label}
          checked={value}
          onChange={handleChange}
        />
      )
    },
  }
)
