import React from "react"
import figma from "@figma/code-connect"

import { FloatButton } from "../src"

figma.connect(
  FloatButton,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=8633-5857&m=dev",
  {
    imports: ["import { FloatButton } from '@apollo/ui'"],
    props: {
      icon: figma.instance("IconButton"),
      label: figma.string("TextButton"),
      isExpanded: figma.boolean("Expand"),
      iconSide: figma.enum("Icon Position", {
        Left: "start",
        Right: "end",
      }),
      disabled: figma.enum("State", {
        Enabled: false,
        Disabled: true,
      }),
    },
    example: (props) => (
      <FloatButton
        disabled={props.disabled}
        icon={props.icon}
        iconSide={props.iconSide}
        label={props.label}
      />
    ),
  }
)
