import React, { useState, type ChangeEvent } from "react"
import figma from "@figma/code-connect"

import { Input } from "../src"

/**
 * -- This file was auto-generated by Code Connect --
 * `props` includes a mapping from your code props to Figma properties.
 * You should check this is correct, and update the `example` function
 * to return the code example you'd like to see in Figma
 */

figma.connect(
  Input,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=1344-3835&m=dev",
  {
    imports: ["import { Input } from '@design-systems/apollo-ui'"],
    props: {
      // Using State variant to handle error and disabled states
      state: figma.enum("State", {
        default: undefined,
        error: "error",
        disabled: "disabled",
      }),
      error: figma.enum("State", {
        default: undefined,
        error: true,
        disabled: undefined,
      }),
      disabled: figma.enum("State", {
        default: undefined,
        error: undefined,
        disabled: true,
      }),

      size: figma.enum("Size", {
        Medium: "medium",
        Small: "small",
      }),
      icon: figma.nestedProps(".icon_input_Master", {
        icon: figma.instance("Icon"),
      }),
      label: figma.nestedProps("Label", {
        text: figma.string("Text"),
        hasRequired: figma.boolean("hasRequired"),
      }),
      helperText: figma.nestedProps("helper text", {
        text: figma.string("HelperText"),
      }),
      text: figma.string("Text Field"),
    },
    example: (props) => {
      const [value, setValue] = useState(props.text)

      const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        setValue(event.target.value)
      }

      return (
        <Input
          size={props.size}
          error={props.error}
          disabled={props.disabled}
          label={props.label?.text}
          required={props?.label?.hasRequired}
          endDecorator={props?.icon?.icon}
          helperText={props.helperText?.text}
          value={value}
          onChange={handleChange}
        />
      )
    },
  }
)
