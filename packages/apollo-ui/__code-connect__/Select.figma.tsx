/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState } from "react"
import figma from "@figma/code-connect"

import { Select } from "../src/components/select"

figma.connect(
  Select,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=537-7773&m=dev",
  {
    imports: ["import { Select } from '@apollo/ui'"],
    example: () => {
      const [value, setValue] = useState("")
      return (
        <Select
          label="Select"
          helperText="This is a helper text"
          value={value}
          onChange={setValue}
        >
          <Select.Option label="Option 1" value="1" />
          <Select.Option label="Option 2" value="2" />
          <Select.Option label="Option 3" value="3" />
          <Select.Option label="Option 4" value="4" />
          <Select.Option label="Option 5" value="5" />
        </Select>
      )
    },
  }
)
