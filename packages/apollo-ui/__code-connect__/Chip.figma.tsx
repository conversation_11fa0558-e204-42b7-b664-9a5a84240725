import React, { useState } from "react"
import figma from "@figma/code-connect"

import { Chip } from "../src"

/**
 * -- This file was auto-generated by Code Connect --
 * `props` includes a mapping from your code props to Figma properties.
 * You should check this is correct, and update the `example` function
 * to return the code example you'd like to see in Figma
 */

figma.connect(
  Chip,
  "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=419-10875&m=dev",
  {
    imports: ["import { Chip } from '@apollo/ui'"],
    props: {
      disabled: figma.enum("State", {
        enable: undefined,
        Disabled: true,
      }),
      size: figma.enum("Size", {
        Medium: "medium",
        Small: "small",
      }),
      onClose: figma.boolean("hasCloseIcon", {
        true: () => {},
        false: undefined,
      }),
      label: figma.string("TextChip"),
    },
    example: (props) => {
      return (
        <Chip
          size={props.size}
          label={props.label}
          disabled={props.disabled}
          onClose={props.onClose}
        />
      )
    },
  }
)
