import { dirname, resolve } from "node:path"
import { fileURLToPath } from "node:url"
import react from "@vitejs/plugin-react"
import { defineConfig, LibraryOptions } from "vite"
import dts from "vite-plugin-dts"

const __dirname = dirname(fileURLToPath(import.meta.url))

const ReactCompilerConfig = {
  target: "17", // '17' | '18' | '19'
}

const libs = {
  main: {
    entry: resolve(__dirname, "src/index.ts"),
    name: "@apollo/ui",
    // the proper extensions will be added
    fileName: "index",
    formats: ["es", "umd"],
  },
  legacy: {
    entry: resolve(__dirname, "src/legacy/index.ts"),
    name: "@apollo/ui",
    // the proper extensions will be added
    fileName: "legacy",
    formats: ["es", "umd"],
  },
  cli: {
    entry: resolve(__dirname, "src/bin/index.ts"),
    name: "@apollo/ui",
    // the proper extensions will be added
    fileName: "cli",
    formats: ["cjs"],
  },
} as Record<string, LibraryOptions>

export default defineConfig({
  plugins: [
    react({
      babel: {
        plugins: [["babel-plugin-react-compiler", ReactCompilerConfig]],
      },
    }),
    dts({
      tsconfigPath: "./tsconfig.app.json",
    }),
  ],
  build: {
    emptyOutDir: false,
    lib: libs?.[process.env.TARGET_LIB as keyof typeof libs] ?? libs.main,
    rollupOptions:
      process.env.TARGET_LIB === "cli"
        ? {
            // For CLI, externalize Node.js built-ins but bundle dependencies
            external: [
              "fs",
              "path",
              "os",
              "module",
              "process",
              "events",
              "child_process",
              "node:fs",
              "node:path",
              "node:os",
              "node:module",
              "node:process",
              "node:events",
              "node:child_process",
              "node:util",
              "node:stream",
              "node:buffer",
              "node:crypto",
              "node:url",
              "node:querystring",
              "node:assert",
              "node:net",
              "node:tty",
              "node:readline",
              "util",
              "stream",
              "buffer",
              "crypto",
              "url",
              "querystring",
              "assert",
              "net",
              "tty",
              "readline",
            ],
            output: {
              format: "cjs",
            },
          }
        : {
            // For libraries, externalize deps that shouldn't be bundled
            external: [
              "react",
              "react-dom",
              "react/jsx-runtime",
              "@design-systems/apollo-ui",
              "@design-systems/apollo-icons",
            ],
            output: {
              // Provide global variables to use in the UMD build
              // for externalized deps
              globals: {
                react: "React",
                "react-dom": "ReactDOM",
                "react/jsx-runtime": "react/jsx-runtime",
              },
            },
          },
  },
})
