import chalk from "chalk"
import { Command } from "commander"
import ora from "ora"

import { CLIConfig, configManager } from "./config"

export function createSetupCommand(): Command {
  const setupCommand = new Command("setup")
    .description("Initialize and configure Apollo CLI")
    .option(
      "-t, --figma-token <token>",
      "Figma access token for design system integration"
    )
    .option(
      "-e, --doc-endpoint <endpoint>",
      "Documentation API endpoint URL",
      "https://api.figma.com/v1"
    )
    .option("-r, --reset", "Reset existing configuration")
    .option("--interactive", "Run interactive setup")
    .action(async (options) => {
      try {
        // Handle reset option
        if (options.reset) {
          const spinner = ora("Resetting configuration...").start()
          configManager.resetConfig()
          spinner.succeed("Configuration reset successfully")
          return
        }

        console.log(chalk.blue.bold("\n🚀 Apollo CLI Setup\n"))

        // Check if already configured
        if (configManager.isConfigured() && !options.reset) {
          console.log(chalk.yellow("⚠️  CLI is already configured"))
          console.log(
            chalk.gray(`Config file: ${configManager.getConfigPath()}`)
          )
          console.log(
            chalk.gray("Use --reset to reconfigure or --help for options")
          )
          return
        }

        const config: CLIConfig = {
          projectPath: process.cwd(),
          initialized: false,
        }

        // Get Figma access token
        if (options.figmaToken) {
          config.figmaAccessToken = options.figmaToken
        } else if (process.env.FIGMA_ACCESS_TOKEN) {
          config.figmaAccessToken = process.env.FIGMA_ACCESS_TOKEN
          console.log(
            chalk.green("✅ Using Figma access token from environment")
          )
        } else {
          console.log(chalk.red("❌ Figma access token is required"))
          console.log(chalk.gray("Provide token using one of these methods:"))
          console.log(
            chalk.gray("  1. Command option: --figma-token YOUR_TOKEN")
          )
          console.log(
            chalk.gray(
              "  2. Environment variable: FIGMA_ACCESS_TOKEN=YOUR_TOKEN"
            )
          )
          console.log(
            chalk.gray("  3. Set it in .env.cli file after running setup")
          )
          console.log(
            chalk.yellow("\n💡 Get your Figma access token from Figma settings")
          )

          // Create basic config without token for now
          config.figmaAccessToken = ""
        }

        // Set Documentation API endpoint
        config.documentationApiEndpoint = options.docEndpoint

        // Validate token if provided
        if (config.figmaAccessToken) {
          const spinner = ora("Validating Figma access token...").start()
          const isValid = await configManager.validateFigmaToken(
            config.figmaAccessToken
          )

          if (isValid) {
            spinner.succeed("Figma access token validated")
            config.initialized = true
          } else {
            spinner.fail("Figma access token validation failed")
            console.log(
              chalk.yellow(
                "⚠️  Setup will continue, but you may need to update the token"
              )
            )
          }
        }

        // Save configuration
        const saveSpinner = ora("Saving configuration...").start()
        configManager.saveConfig(config)
        saveSpinner.succeed("Configuration saved")

        // Create .env.cli file
        const envSpinner = ora("Creating .env.cli file...").start()
        configManager.createEnvFile(config)
        envSpinner.succeed(".env.cli file created")

        // Display setup summary
        console.log(chalk.green.bold("\n✅ Setup completed successfully!\n"))
        console.log(chalk.blue("📁 Configuration saved to:"))
        console.log(chalk.gray(`   ${configManager.getConfigPath()}`))
        console.log(chalk.blue("📄 Environment file created:"))
        console.log(chalk.gray(`   ${configManager.getEnvFilePath()}`))

        if (!config.figmaAccessToken) {
          console.log(chalk.yellow("\n⚠️  Next steps:"))
          console.log(
            chalk.gray("   1. Get your Figma access token from Figma settings:")
          )
          console.log(
            chalk.gray(
              "      https://www.figma.com/developers/api#access-tokens"
            )
          )
          console.log(
            chalk.gray(
              "   2. Add it to .env.cli file: FIGMA_ACCESS_TOKEN=your_token"
            )
          )
          console.log(
            chalk.gray("   3. Or run: cj-apollo setup --figma-token YOUR_TOKEN")
          )
        }

        console.log(chalk.blue("\n🎉 Apollo CLI is ready to use!"))
      } catch (error) {
        console.error(chalk.red("❌ Setup failed:"), error)
        process.exit(1)
      }
    })

  return setupCommand
}

export function createConfigCommand(): Command {
  const configCommand = new Command("config").description(
    "Manage CLI configuration"
  )

  configCommand
    .command("show")
    .description("Show current configuration")
    .action(() => {
      try {
        const config = configManager.loadConfig()

        console.log(chalk.blue.bold("\n📋 Current Configuration\n"))
        console.log(
          chalk.blue("Config file:"),
          chalk.gray(configManager.getConfigPath())
        )
        console.log(
          chalk.blue("Environment file:"),
          chalk.gray(configManager.getEnvFilePath())
        )
        console.log()

        const maskToken = (token?: string) => {
          if (!token) return chalk.red("Not set")
          return token.length > 8
            ? `${token.slice(0, 4)}...${token.slice(-4)}`
            : "****"
        }

        console.log(
          chalk.blue("Initialized:"),
          config.initialized ? chalk.green("Yes") : chalk.red("No")
        )
        console.log(
          chalk.blue("Figma Token:"),
          maskToken(config.figmaAccessToken)
        )
        console.log(
          chalk.blue("Documentation API:"),
          config.documentationApiEndpoint || chalk.gray("Default")
        )
        console.log(
          chalk.blue("Project Path:"),
          config.projectPath || chalk.gray("Current directory")
        )
        console.log()
      } catch (error) {
        console.error(chalk.red("❌ Failed to load configuration:"), error)
      }
    })

  configCommand
    .command("set <key> <value>")
    .description("Set a configuration value")
    .action((key: string, value: string) => {
      try {
        const validKeys = [
          "figmaAccessToken",
          "documentationApiEndpoint",
          "projectPath",
        ]

        if (!validKeys.includes(key)) {
          console.error(
            chalk.red(`❌ Invalid key. Valid keys: ${validKeys.join(", ")}`)
          )
          return
        }

        const updates: Partial<CLIConfig> = {}

        switch (key) {
          case "figmaAccessToken":
            updates.figmaAccessToken = value
            updates.initialized = true
            break
          case "documentationApiEndpoint":
            updates.documentationApiEndpoint = value
            break
          case "projectPath":
            updates.projectPath = value
            break
        }

        configManager.updateConfig(updates)

        // Update .env.cli file
        const config = configManager.loadConfig()
        configManager.createEnvFile(config)

        console.log(chalk.green(`✅ Updated ${key}`))
      } catch (error) {
        console.error(chalk.red("❌ Failed to update configuration:"), error)
      }
    })

  configCommand
    .command("reset")
    .description("Reset all configuration")
    .action(() => {
      try {
        configManager.resetConfig()
      } catch (error) {
        console.error(chalk.red("❌ Failed to reset configuration:"), error)
      }
    })

  return configCommand
}
