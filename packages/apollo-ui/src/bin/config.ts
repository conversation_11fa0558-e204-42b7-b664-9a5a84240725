import fs from "fs"
import os from "os"
import path from "path"
import chalk from "chalk"

export interface CLIConfig {
  figmaAccessToken?: string
  documentationApiEndpoint?: string
  projectPath?: string
  initialized?: boolean
}

export class ConfigManager {
  private configDir: string
  private configFile: string
  private envFile: string

  constructor() {
    // Store config in user's home directory
    this.configDir = path.join(os.homedir(), ".cj-apollo")
    this.configFile = path.join(this.configDir, "config.json")
    this.envFile = path.join(process.cwd(), ".env.cli")
  }

  /**
   * Initialize the CLI configuration directory
   */
  private ensureConfigDir(): void {
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true })
    }
  }

  /**
   * Load configuration from file
   */
  public loadConfig(): CLIConfig {
    try {
      if (fs.existsSync(this.configFile)) {
        const configData = fs.readFileSync(this.configFile, "utf8")
        return JSON.parse(configData)
      }
    } catch {
      console.warn(chalk.yellow("Warning: Failed to load config file"))
    }
    return {}
  }

  /**
   * Save configuration to file
   */
  public saveConfig(config: CLIConfig): void {
    this.ensureConfigDir()
    try {
      fs.writeFileSync(this.configFile, JSON.stringify(config, null, 2))
    } catch {
      console.error(chalk.red("Error: Failed to save config file"))
      throw new Error("Failed to save config file")
    }
  }

  /**
   * Update specific config values
   */
  public updateConfig(updates: Partial<CLIConfig>): void {
    const currentConfig = this.loadConfig()
    const newConfig = { ...currentConfig, ...updates }
    this.saveConfig(newConfig)
  }

  /**
   * Check if CLI is properly configured
   */
  public isConfigured(): boolean {
    const config = this.loadConfig()
    return !!(config.initialized && config.figmaAccessToken)
  }

  /**
   * Get the current working directory config
   */
  public getProjectConfig(): CLIConfig {
    const config = this.loadConfig()
    return {
      ...config,
      projectPath: process.cwd(),
    }
  }

  /**
   * Create .env.cli file in current directory
   */
  public createEnvFile(config: CLIConfig): void {
    try {
      const envContent = [
        "# Apollo CLI Environment Configuration",
        "# This file is automatically generated by cj-apollo CLI",
        "# Do not commit this file to version control",
        "",
        `FIGMA_ACCESS_TOKEN=${config.figmaAccessToken || ""}`,
        "",
        "# Add your custom environment variables below:",
        "",
      ].join("\n")

      fs.writeFileSync(this.envFile, envContent)
      console.log(chalk.green(`✅ Created .env.cli file at ${this.envFile}`))
    } catch {
      console.error(chalk.red("Error: Failed to create .env.cli file"))
      throw new Error("Failed to create .env.cli file")
    }
  }

  /**
   * Load environment variables from .env.cli
   */
  public loadEnvFile(): void {
    try {
      if (fs.existsSync(this.envFile)) {
        const envContent = fs.readFileSync(this.envFile, "utf8")
        const lines = envContent.split("\n")

        for (const line of lines) {
          const trimmedLine = line.trim()
          if (
            trimmedLine &&
            !trimmedLine.startsWith("#") &&
            trimmedLine.includes("=")
          ) {
            const [key, ...valueParts] = trimmedLine.split("=")
            const value = valueParts.join("=")
            process.env[key] = value
          }
        }
      }
    } catch {
      console.warn(chalk.yellow("Warning: Failed to load .env.cli file"))
    }
  }

  /**
   * Reset configuration
   */
  public resetConfig(): void {
    try {
      if (fs.existsSync(this.configFile)) {
        fs.unlinkSync(this.configFile)
      }
      if (fs.existsSync(this.envFile)) {
        fs.unlinkSync(this.envFile)
      }
      console.log(chalk.green("✅ Configuration reset successfully"))
    } catch {
      console.error(chalk.red("Error: Failed to reset configuration"))
      throw new Error("Failed to reset configuration")
    }
  } /**
   * Validate Figma access token
   */
  public async validateFigmaToken(token: string): Promise<boolean> {
    try {
      // Basic token validation - check if token is not empty and has reasonable length
      if (!token || token.length < 10) {
        return false
      }

      // You could add actual Figma API validation here
      // const response = await fetch('https://api.figma.com/v1/me', {
      //   headers: { 'X-Figma-Token': token }
      // })
      // return response.ok

      return true
    } catch {
      return false
    }
  }

  /**
   * Get configuration file path for display
   */
  public getConfigPath(): string {
    return this.configFile
  }

  /**
   * Get .env.cli file path for display
   */
  public getEnvFilePath(): string {
    return this.envFile
  }
}

export const configManager = new ConfigManager()
