#!/usr/bin/env node
import chalk from "chalk"
import { Command } from "commander"
import ora from "ora"

import { startMcpServer } from "./mcp/index"
import { createConfigCommand, createSetupCommand } from "./setup"
import { displayWelcome, getConfigWithEnv, requireSetup } from "./utils"

const program = new Command()

program
  .name("cj-apollo")
  .description("CLI for managing Apollo-related tasks")
  .version("1.0.0")

// Add setup and config commands (these don't require configuration)
program.addCommand(createSetupCommand())
program.addCommand(createConfigCommand())

// Define your command: `run mcp`
program
  .command("run <task>") // <task> is a required argument
  .description("Run a specific task")
  .action((task: string) => {
    // Check if CL<PERSON> is configured before running tasks
    if (!requireSetup("run")) {
      process.exit(1)
    }

    const config = getConfigWithEnv()
    console.log(chalk.blue(`🚀 Running task: ${task}`))
    console.log(
      chalk.gray(`Using Documentation API: ${config.documentationApiEndpoint}`)
    )
    console.log(
      chalk.gray(
        `Figma Token: ${config.figmaAccessToken ? "✅ Configured" : "❌ Missing"}`
      )
    )
    console.log()

    if (task === "mcp") {
      const spinner = ora("Running the MCP Server").start()
      // Call your library's logic here
      startMcpServer()
      spinner.clear()
    } else {
      console.log(chalk.red(`❌ Unknown task: ${task}`))
      console.log(chalk.gray("Available tasks: mcp"))
      process.exit(1)
    }
  })

// Add a general "hello" command for demonstration
program
  .command("hello <name>")
  .description("Says hello to the given name")
  .action((name: string) => {
    console.log(chalk.green(`Hello, ${name}! 👋`))

    const config = getConfigWithEnv()
    if (config.initialized) {
      console.log(chalk.gray("Apollo CLI is configured and ready to use"))
    } else {
      console.log(
        chalk.yellow(
          "Apollo CLI is not fully configured. Run 'cj-apollo setup' to get started"
        )
      )
    }
  })

// Add info command to show status
program
  .command("info")
  .description("Show CLI information and status")
  .action(() => {
    console.log(chalk.blue.bold("\n🔧 Apollo CLI Information\n"))
    displayWelcome()

    const config = getConfigWithEnv()
    console.log(chalk.blue("Version:"), "1.0.0")
    console.log(
      chalk.blue("Status:"),
      config.initialized
        ? chalk.green("Configured")
        : chalk.yellow("Not configured")
    )
    console.log(chalk.blue("Project:"), config.projectPath)
    console.log()
    console.log(
      chalk.gray("Run 'cj-apollo config show' for detailed configuration")
    )
    console.log(chalk.gray("Run 'cj-apollo --help' for available commands"))
  })

program.parse(process.argv)
