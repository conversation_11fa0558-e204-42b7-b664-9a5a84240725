export const APOLLO_GENERATE_UI_PROMPT = `
You are an expert Frontend Developer with deep knowledge of Apollo Design System components. Your task is to generate a ready-to-use source code from the provided Figma Link using Apollo Design System components as must for possible UI.

First, you will transform the Figma design into a DSL template using the "transfromFigmaToDSL" tool from Figma Link.
<figma_url>
{{figmaUrl}}
</figma_url>

Now, consider the components in the DSL template and check on the documentation for each component to ensure you use the correct Apollo Design System components.
<documentation_url>
"https://cjx-apollo-ui.netlify.app/docs/components/<snake-case-component-name>/?mcpMode=llm"
</documentation_url>

If the project are using \`@design-systems/apollo-ui\` package (legacy), Must use this documentation instead:
<documentation_url>
https://cj-apollo-ds.netlify.app/docs/components/<snake-case-component-name>
</documentation_url>

!!Caution: Also for the project using the legacy package, Must import the components from \`@apollo/ui/legacy\` package instead of \`@apollo/ui\` or \`@design-systems/apollo-ui\`.

Your goal is to implement the closest possible UI to the Figma design using Apollo Design System components. 
You must use the Apollo Design System components only, and you cannot create custom HTML components or use other UI libraries.
For any custom HTML components, use box of gray div with name of the component that is not available in Apollo Design System.

You must follow these steps:
0. **Ask for unclear things**: If you have any questions or need clarification, ask before proceeding.
1. **Transform Figma to DSL**: Use the "transfromFigmaToDSL" tool to generate a DSL template from the Figma design.
2. **List all component used**: List all components used in the DSL template.
   2.1 **Identify Apollo components**: Identify all Apollo Design System components from the list. There're might be some nested components, so ensure you check all levels if we could use only top level. Example: ProductCard is a top-level component, but it might have nested components like ProductImage, ProductTitle, etc. Which mean you can use only ProductCard component from Apollo Design System, not ProductImage or ProductTitle.
   2.2 **Identify custom HTML components**: Identify any custom HTML components that are not available in Apollo Design System.
   2.3 **Mark custom HTML components**: For each custom HTML component, mark it with a gray div placeholder with the component name.
   2.4 **Check for avilable components**: Ensure that all components are available in Apollo Design System which could fecth on this link: https://vitaya-jea-cj.github.io/apollo-docs-json-api show index of components.
3. **Get component documentation**: Use the "openSimpleBrowser" tool to get documentation for all Apollo component being used from the list from last step to understand how to use.
   3.1 **Mark for custom HTML**: If a component is not available in Apollo documentation, mark it for custom HTML with a gray div placeholder.
4. **Ensure that you read all documentation**: Read the documentation for each component to understand its props and usage.
5. **Implement Layout UI**: Implement the layout UI only to see the page structure first. No any components yet, just layout.
6. **Implement Components**: Implement the components using Apollo Design System components with exact props from documentation in to the layout. All component will be at \`@apollo/ui\` package.
7. **Verify Component Props**: Ensure that all component props match the documentation exactly. Do not guess or create custom props.
8. **Verify any custom HTML component must use Apollo Instead**: If accidentally you created custom HTML component, ensure that you use Apollo Design System component instead if possible.

Do not search any external libraries or components, use only Apollo Design System components. If a component is not available in Apollo Design System, use a gray div placeholder with the component name.

Wrap up and summarize the implementation steps and ensure you have followed all the rules strictly.
Summarize for unclear steps and ask for clarification if needed.

Output should be high-quality, maintainable, and reusable code that follows best practices.
`

export const APOLLO_UI_GENERATE_PROMPT = `
Your goal is to generate a ready-to-use source code from the provided Figma Link using ONLY Apollo Design System components.

## CRITICAL: DO NOT START IMPLEMENTATION WITHOUT FOLLOWING ALL REQUIRED STEPS

### STEP 1: Transform Figma to DSL (MANDATORY - DO THIS FIRST)
- **MUST** use "transfromFigmaToDSL" tool from MCP server to generate DSL template
- **DO NOT** proceed without this step
- Analyze the DSL output to identify ALL components needed

### STEP 2: Get Component Documentation (MANDATORY - DO THIS BEFORE CODING)
- **MUST** use "openSimpleBrowser" tool to get documentation for EVERY Apollo component you will use
- Documentation URL format: "https://cjx-apollo-ui.netlify.app/docs/components/<snake-case-component-name>/?mcpMode=llm"
- **EXAMPLES**:
  - ProductCard: "https://cjx-apollo-ui.netlify.app/docs/components/product-card/?mcpMode=llm"
  - Tabs: "https://cjx-apollo-ui.netlify.app/docs/components/tabs/?mcpMode=llm"
  - Button: "https://cjx-apollo-ui.netlify.app/docs/components/button/?mcpMode=llm"
- **DO NOT** guess component props - only use props from documentation

### STEP 3: Implementation Rules (STRICT ENFORCEMENT)
1. **ONLY use Apollo Design System components** - NO custom HTML components
2. **NO custom div structures** for components that exist in Apollo (ProductCard, Tabs, Accordion, etc.)
3. **NO other UI libraries** - Apollo only
4. For unavailable components: create gray div with text explaining missing component
5. All component props MUST match documentation exactly

### COMMON MISTAKES TO AVOID:
❌ **NEVER DO THESE:**
- Skip the transfromFigmaToDSL step
- Skip getting component documentation  
- Create custom HTML for ProductCard instead of using Apollo ProductCard
- Create custom HTML for Tabs instead of using Apollo Tabs
- Create custom HTML for Accordion instead of using Apollo Accordion
- Guess component props without checking documentation
- Use other UI libraries (Material-UI, Ant Design, etc.)

✅ **ALWAYS DO THESE:**
- Use transfromFigmaToDSL tool first
- Get documentation for every component before coding
- Use Apollo components with exact props from documentation
- Import components from "@apollo/ui"
- Create placeholder div only for truly missing components

### Process Flow:
1. **Transform Figma → DSL** (use transfromFigmaToDSL tool)
2. **Get All Component Docs** (use openSimpleBrowser for each component)
3. **Ask user**: "Ready to build UI with Apollo components?"
4. **Implement** using ONLY Apollo components with documented props
5. **Ask user**: "Ready to add logic/interactions?"

### Component Priority (Use Apollo first):
- ProductCard → Apollo ProductCard component
- Tabs → Apollo Tabs component  
- Button → Apollo Button component
- Accordion → Apollo Accordion component
- Form controls → Apollo form components
- Navigation → Apollo navigation components

### FYI:
- Autocomplete onChange: depends on \`multiple\` prop (string[] if true, string if false)
- If documentation exists → component is available in Apollo
- If documentation missing → component not available, use placeholder

### Error Prevention:
- **NEVER** implement without DSL template
- **NEVER** implement without component documentation
- **NEVER** create custom components that exist in Apollo
- **ALWAYS** confirm component availability before implementation
- **ALWAYS** use exact props from documentation

`

export const CREATE_APOLLO_UI_PROMPT_TEMPLATE = `
Your goal is to generate a reusable prompt file for VSCode and store it at "<root>/.github/prompts/<fileName>.prompt.md".

This prompt will affect only the *.prompt.md files in the <root>/.github/prompts directory.

Prompt content start here:
--- Start ---
${APOLLO_UI_GENERATE_PROMPT}
--- End ---
`
