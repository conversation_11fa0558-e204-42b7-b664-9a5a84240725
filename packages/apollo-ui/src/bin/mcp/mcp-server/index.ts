import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js"
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js"
import { z } from "zod"

import { getFigmaComponentJSON } from "../figma-parser"
import {
  APOLLO_GENERATE_UI_PROMPT,
  CREATE_APOLLO_UI_PROMPT_TEMPLATE,
} from "./prompt-template"

// Create an MCP server
export const mcpServer = new McpServer({
  name: "@apollo/ui MCP Server",
  version: "1.0.0-beta.15",
})

mcpServer.tool(
  "createApolloUIPromptFile",
  "Generate reusable prompt file for Apollo UI code generation",
  {},
  () => {
    return Promise.resolve({
      content: [
        {
          type: "text",
          text: CREATE_APOLLO_UI_PROMPT_TEMPLATE,
        },
      ],
    })
  }
)

mcpServer.tool(
  "generateApolloUIFromFigma",
  "Generate Apollo UI from Figma link",
  {
    figmaUrl: z.string().describe("Figma URL to generate Apollo UI from"),
  },
  async ({ figmaUrl }) => {
    return Promise.resolve({
      content: [
        {
          type: "text",
          text: APOLLO_GENERATE_UI_PROMPT.replace("{{figmaUrl}}", figmaUrl),
        },
      ],
    })
  }
)

// Renaming the tool to be more descriptive (this is only transfrom Figma into DSL not into Apollo)
mcpServer.tool(
  "transfromFigmaToDSL",
  "Transform Figma link into DSL template",
  {
    figmaUrl: z.string().describe("Figma URL to convert"),
  },
  async ({ figmaUrl }) => {
    try {
      const { dsl } = await getFigmaComponentJSON(figmaUrl)
      return Promise.resolve({
        content: [
          {
            type: "text",
            text: `Received DSL Template
            --- Start ---
            ${dsl}
            --- End ---
            `,
          },
        ],
      })
    } catch (error) {
      return Promise.resolve({
        content: [
          {
            type: "text",
            text: `Error parsing Figma link: ${error}`,
          },
        ],
      })
    }
  }
)

export async function startServer() {
  const transport = new StdioServerTransport()
  await mcpServer.connect(transport)
}
