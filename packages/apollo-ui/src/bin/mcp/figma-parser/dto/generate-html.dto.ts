// Types for Figma API response
export interface FigmaCanvas {
  id: string
  name: string
  type: "CANVA<PERSON>"
}
export interface FigmaDocumentNode {
  id: string
  name: string
  type: string
  children?: FigmaDocumentNode[]
}
export interface FigmaFileResponse {
  name: string
  lastModified: string
  thumbnailUrl: string
  document: {
    children: FigmaDocumentNode[]
  }
  rawData: any
}

// Return type for generateHtml
export interface GenerateHtmlResult {
  canvases: FigmaCanvas[]
  fileName: string
  lastModified: string
  thumbnailUrl: string
  rawData: any
}

export interface PsuedoCodeResponse {
  pageName: string
  code: string
}
