import chalk from "chalk"

import { configManager } from "./config"

export function requireSetup(commandName: string): boolean {
  console.log(
    chalk.green("🔧 Checking Apollo CLI configuration...", commandName)
  )
  // Load environment variables from .env.cli if it exists
  configManager.loadEnvFile()

  if (!configManager.isConfigured()) {
    console.log(chalk.red("❌ Apollo CLI is not configured"))
    console.log(chalk.gray("Please run the setup command first:"))
    console.log(chalk.blue("  cj-apollo setup"))
    console.log()
    console.log(
      chalk.gray("Or provide configuration via environment variables")
    )
    return false
  }

  const config = configManager.loadConfig()

  // Check for Figma access token
  if (!config.figmaAccessToken && !process.env.FIGMA_ACCESS_TOKEN) {
    console.log(chalk.red("❌ Figma access token is missing"))
    console.log(chalk.gray("Add your token using one of these methods:"))
    console.log(
      chalk.blue("  cj-apollo config set figmaAccessToken YOUR_TOKEN")
    )
    console.log(chalk.gray("  or set FIGMA_ACCESS_TOKEN environment variable"))
    console.log(chalk.gray("  or add it to .env.cli file"))
    console.log(
      chalk.gray(
        "\n💡 Get your token from: https://www.figma.com/developers/api#access-tokens"
      )
    )
    return false
  }

  return true
}

export function getConfigWithEnv() {
  configManager.loadEnvFile()
  const config = configManager.loadConfig()

  return {
    ...config,
    figmaAccessToken: config.figmaAccessToken || process.env.FIGMA_ACCESS_TOKEN,
    documentationApiEndpoint:
      config.documentationApiEndpoint || process.env.DOCUMENTATION_API_ENDPOINT,
    projectPath:
      config.projectPath || process.env.APOLLO_PROJECT_PATH || process.cwd(),
  }
}

export function displayWelcome(): void {
  const config = configManager.loadConfig()

  if (!config.initialized) {
    console.log(chalk.yellow("⚠️  Apollo CLI is not fully configured"))
    console.log(chalk.gray("Run 'cj-apollo setup' to complete configuration"))
    console.log()
  }
}
