/* Transition */
.apl-transition-all {
  transition: all 240ms cubic-bezier(0.075, 0.82, 0.165, 1);
}

.fade-in {
  animation: fade-in 240ms cubic-bezier(0.075, 0.82, 0.165, 1);
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

/** Typography */
.apl-typography-h4 {
  font-family: var(--apl-typography-h4-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-h4-font-size, 16px);
  font-weight: var(--apl-typography-h4-font-weight, 600);
  font-style: normal;
  letter-spacing: var(--apl-typography-h4-letter-spacing, 0px);
  line-height: var(--apl-typography-h4-line-height);
}

.apl-typography-h5 {
  font-family: var(--apl-typography-h5-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-h5-font-size, 14px);
  font-weight: var(--apl-typography-h5-font-weight, 500);
  font-style: normal;
  letter-spacing: var(--apl-typography-h5-letter-spacing, 0px);
  line-height: var(--apl-typography-h5-line-height);
}


.apl-typography-display1 {
  font-family: var(--apl-typography-display1-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-display1-font-size, 80px);
  font-style: normal;
  font-weight: var(--apl-typography-display1-font-weight, 700);
  line-height: var(--apl-typography-display1-line-height);
  letter-spacing: var(--apl-typography-display1-letter-spacing, 0px);
}

.apl-typography-display2 {
  font-family: var(--apl-typography-display2-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-display2-font-size, 40px);
  font-style: normal;
  font-weight: var(--apl-typography-display2-font-weight, 700);
  line-height: var(--apl-typography-display2-line-height);
  letter-spacing: var(--apl-typography-display2-letter-spacing, 0px);
}

.apl-typography-h1 {
  font-family: var(--apl-typography-h1-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-h1-font-size, 28px);
  font-style: normal;
  font-weight: var(--apl-typography-h1-font-weight, 600);
  line-height: var(--apl-typography-h1-line-height);
  letter-spacing: var(--apl-typography-h1-letter-spacing, 0px);
}

.apl-typography-h2 {
  font-family: var(--apl-typography-h2-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-h2-font-size, 24px);
  font-style: normal;
  font-weight: var(--apl-typography-h2-font-weight);
  line-height: var(--apl-typography-h2-line-height);
  letter-spacing: var(--apl-typography-h2-letter-spacing, 0px);
}

.apl-typography-h3 {
  font-family: var(--apl-typography-h3-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-h3-font-size, 24px);
  font-style: normal;
  font-weight: var(--apl-typography-h3-font-weight, 500);
  line-height: var(--apl-typography-h3-line-height);
  letter-spacing: var(--apl-typography-h3-letter-spacing, 0px);
}

.apl-typography-body1 {
  font-family: var(--apl-typography-body1-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-body1-font-size, 24px);
  font-style: normal;
  font-weight: var(--apl-typography-body1-font-weight);
  line-height: var(--apl-typography-body1-line-height);
  letter-spacing: var(--apl-typography-body1-letter-spacing, 0px);
}

.apl-typography-body2 {
  font-family: var(--apl-typography-body2-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-body2-font-size, 24px);
  font-style: normal;
  font-weight: var(--apl-typography-body2-font-weight);
  line-height: var(--apl-typography-body2-line-height);
  letter-spacing: var(--apl-typography-body2-letter-spacing, 0px);
}

.apl-typography-caption {
  font-family: var(--apl-typography-caption-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-caption-font-size, 24px);
  font-style: normal;
  font-weight: var(--apl-typography-caption-font-weight);
  line-height: var(--apl-typography-caption-line-height);
  letter-spacing: var(--apl-typography-caption-letter-spacing, 0px);
}

.apl-typography-textlink {
  color: var(--apl-colors-content-process-default, #4765F2);

  font-family: var(--apl-typography-text-link-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-typography-text-link-font-size, 16px);
  font-style: normal;
  font-weight: var(--apl-typography-text-link-font-weight);
  line-height: var(--apl-typography-text-link-line-height);
  letter-spacing: var(--apl-typography-text-link-letter-spacing, 0px);
  text-decoration-line: underline;
  text-decoration-style: solid;
  text-decoration-skip-ink: auto;
  text-decoration-thickness: auto;
  text-underline-offset: auto;
  text-underline-position: from-font;
}

/** Typography */
.apl-typography-display-large {
  font-family: var(--apl-alias-typography-display-large-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-display-large-font-size, 57px);
  font-weight: var(--apl-alias-typography-display-large-font-weight, 700);
  line-height: var(--apl-alias-typography-display-large-line-height, 88px);
  font-style: normal;
}

.apl-typography-display-medium {
  font-family: var(--apl-alias-typography-display-medium-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-display-medium-font-size, 45px);
  font-weight: var(--apl-alias-typography-display-medium-font-weight, 700);
  line-height: var(--apl-alias-typography-display-medium-line-height, 88px);
  font-style: normal;
}

.apl-typography-display-small {
  font-family: var(--apl-alias-typography-display-small-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-display-small-font-size, 36px);
  font-weight: var(--apl-alias-typography-display-small-font-weight, 700);
  line-height: var(--apl-alias-typography-display-small-line-height, 56px);
  font-style: normal;
}

.apl-typography-headline-large {
  font-family: var(--apl-alias-typography-headline-large-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-headline-large-font-size, 32px);
  font-weight: var(--apl-alias-typography-headline-large-font-weight, 700);
  line-height: var(--apl-alias-typography-headline-large-line-height, 52px);
  font-style: normal;
}

.apl-typography-headline-medium {
  font-family: var(--apl-alias-typography-headline-medium-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-headline-medium-font-size, 28px);
  font-weight: var(--apl-alias-typography-headline-medium-font-weight, 700);
  line-height: var(--apl-alias-typography-headline-medium-line-height, 80px);
  font-style: normal;
}

.apl-typography-headline-small {
  font-family: var(--apl-alias-typography-headline-small-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-headline-small-font-size, 24px);
  font-weight: var(--apl-alias-typography-headline-small-font-weight, 700);
  line-height: var(--apl-alias-typography-headline-small-line-height, 36px);
  font-style: normal;
}

.apl-typography-title-large {
  font-family: var(--apl-alias-typography-title-large-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-title-large-font-size, 22px);
  font-weight: var(--apl-alias-typography-title-large-font-weight, 500);
  line-height: var(--apl-alias-typography-title-large-line-height, 36px);
  font-style: normal;
}

.apl-typography-title-medium {
  font-family: var(--apl-alias-typography-title-medium-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-title-medium-font-size, 20px);
  font-weight: var(--apl-alias-typography-title-medium-font-weight, 500);
  line-height: var(--apl-alias-typography-title-medium-line-height, 36px);
  font-style: normal;
}

.apl-typography-title-small {
  font-family: var(--apl-alias-typography-title-small-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-title-small-font-size, 18px);
  font-weight: var(--apl-alias-typography-title-small-font-weight, 700);
  line-height: var(--apl-alias-typography-title-small-line-height, 30px);
  font-style: normal;
}

.apl-typography-body-large {
  font-family: var(--apl-alias-typography-body-large-font-family);
  font-size: var(--apl-alias-typography-body-large-font-size);
  font-weight: var(--apl-alias-typography-body-large-font-weight);
  line-height: var(--apl-alias-typography-body-large-line-height);
  font-style: normal;
}

.apl-typography-body-large-emphasized {
  composes: apl-typography-body-large;
  font-weight: var(--apl-alias-typography-body-large-weight-emphasized, 500);
}

.apl-typography-body-medium {
  font-family: var(--apl-alias-typography-body-medium-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-body-medium-font-size, 14px);
  font-weight: var(--apl-alias-typography-body-medium-font-weight, 400);
  line-height: var(--apl-alias-typography-body-medium-line-height, 24px);
  font-style: normal;
}

.apl-typography-body-medium-emphasized {
  composes: apl-typography-body-medium;
  font-weight: var(--apl-alias-typography-body-medium-weight-emphasized, 500);
}

.apl-typography-body-small {
  font-family: var(--apl-alias-typography-body-small-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-body-small-font-size, 12px);
  font-weight: var(--apl-alias-typography-body-small-font-weight, 400);
  line-height: var(--apl-alias-typography-body-small-line-height, 20px);
  font-style: normal;
}

.apl-typography-body-small-emphasized {
  composes: apl-typography-body-small;
  font-weight: var(--apl-alias-typography-body-small-weight-emphasized, 500);
}

.apl-typography-label-large {
  font-family: var(--apl-alias-typography-label-large-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-label-large-font-size, 14px);
  font-weight: var(--apl-alias-typography-label-large-font-weight, 400);
  line-height: var(--apl-alias-typography-label-large-line-height, 20px);
  font-style: normal;
}

.apl-typography-label-large-emphasized {
  composes: apl-typography-label-large;
  font-weight: var(--apl-alias-typography-label-large-weight-emphasized, 500);
}

.apl-typography-label-medium {
  font-family: var(--apl-alias-typography-label-medium-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-label-medium-font-size, 12px);
  font-weight: var(--apl-alias-typography-label-medium-font-weight, 400);
  line-height: var(--apl-alias-typography-label-medium-line-height, 20px);
  font-style: normal;
}

.apl-typography-label-medium-emphasized {
  composes: apl-typography-label-medium;
  font-weight: var(--apl-alias-typography-label-medium-weight-emphasized, 500);
}

.apl-typography-label-small {
  font-family: var(--apl-alias-typography-label-small-font-family, "IBM Plex Sans Thai");
  font-size: var(--apl-alias-typography-label-small-font-size, 10px);
  font-weight: var(--apl-alias-typography-label-small-font-weight, 400);
  line-height: var(--apl-alias-typography-label-small-line-height, 24px);
  font-style: normal;
}

.apl-typography-label-small-emphasized {
  composes: apl-typography-label-small;
  font-weight: var(--apl-alias-typography-label-small-weight-emphasized, 500);
}