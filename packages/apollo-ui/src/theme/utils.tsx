import {
  ApolloDefineToken,
  apolloTheme,
  ApolloToken,
  createStyleProperties,
  generateStyleFromTokens,
} from "@apollo/token"

import {
  ApolloDesignTokenConfig,
  CreateThemeOptions,
  ThemeConfig,
} from "./types"

const COLOR_SCHEME_KEY = "prefers-color-scheme"

export function isObject(data: unknown): data is Record<string, unknown> {
  return typeof data === "object" && data !== null && !Array.isArray(data)
}

/**
 * Overrides the keys of the base object with those from the extending object.
 *
 * This utility function merges two objects, `baseData` and `extendingData`, by overriding the keys of `baseData` with the corresponding keys from `extendingData`.
 * If both values are objects, it recursively merges them. If the overriding value is `undefined` or `null`, the original value from `baseData` is kept.
 *
 * Example:
 * Base data: { a: 1, b: { c: 2, d: 3 }, e: 4 }
 * Extending data: { b: { c: 5 }, e: null }
 * Result: { a: 1, b: { c: 5, d: 3 }, e: 4 }
 */
export function overrideExistedKey<
  BaseData extends object,
  ExtendingData extends object,
>(extendingData: ExtendingData, baseData: BaseData): BaseData {
  if (!isObject(extendingData)) {
    return baseData
  }

  const extendedBaseObject = {
    ...baseData,
    ...extendingData,
  }

  const mergedObject = Object.entries<BaseData>(
    baseData as ArrayLike<BaseData>
  ).reduce<BaseData>((result, [key, value]) => {
    const overridingValue = extendingData?.[key as keyof typeof extendingData]

    const isExtendableValues = isObject(value) && isObject(overridingValue)
    const isNullishValue =
      overridingValue === undefined || overridingValue === null

    // filter out `undefined` and `null` value
    const tokenValue = isNullishValue ? value : overridingValue

    return {
      ...result,
      [key]: isExtendableValues
        ? overrideExistedKey(overridingValue as object, value)
        : tokenValue,
    }
  }, extendedBaseObject)

  return mergedObject
}

export function initializePrefersColorScheme() {
  const injectingScript = `(function(){
    var matchedMedia = window.matchMedia('(prefers-color-scheme: dark)')
    var prefersColorScheme = localStorage.getItem('${COLOR_SCHEME_KEY}')
    if (!prefersColorScheme) {
      var colorScheme = matchedMedia.matches ? 'dark' : 'light'
      localStorage.setItem('${COLOR_SCHEME_KEY}', colorScheme)
    }
  })()
  `
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: injectingScript,
      }}
      data-theme="cjx-apollo-color-scheme-injector"
    />
  )
}

export function parseToken(theme: Partial<ApolloDesignTokenConfig>) {
  return createStyleProperties(theme, true).join("")
}

export function getInjectorId(scope = ":root") {
  return `${scope.replace(/[^a-zA-Z0-9-]/, "")}`
}

export function getThemeWrapperIdentityId(id: string) {
  return `${id}-wrapper-identity`
}

export function createTheme(
  theme: CreateThemeOptions = {}
): Partial<ApolloDesignTokenConfig> {
  return overrideExistedKey(
    theme,
    apolloTheme
  ) as unknown as Partial<ApolloDesignTokenConfig>
}

export function parseTokenV2(tokens: ApolloDefineToken) {
  return generateStyleFromTokens(tokens).join("\n")
}

/**
 * Flattens nested objects by combining nested keys with hyphens.
 * Only flattens objects that contain other objects with primitive values.
 *
 * @param obj - The object to flatten
 * @returns A flattened object with hyphenated keys
 *
 * @example
 * const input = {
 *   "other": {
 *     "badge": {
 *       "low-stock": "{base.color.danger.90}",
 *       "more-item": "{base.color.danger.60}"
 *     }
 *   }
 * }
 *
 * const result = flattenNestedObject(input)
 * // Returns:
 * // {
 * //   "other": {
 * //     "badge-low-stock": "{base.color.danger.90}",
 * //     "badge-more-item": "{base.color.danger.60}"
 * //   }
 * // }
 */
export function flattenNestedObject(
  obj: Record<string, unknown>
): Record<string, unknown> {
  const result: Record<string, unknown> = {}

  /**
   * Combines two keys with hyphen separator
   */
  const combineKeys = (parentKey: string, childKey: string): string => {
    return `${parentKey}-${childKey}`
  }

  for (const [key, value] of Object.entries(obj)) {
    if (isObject(value) && !Array.isArray(value)) {
      const valueObj = value as Record<string, unknown>

      // Check if this object contains nested objects that need flattening
      const hasNestedObjects = Object.values(valueObj).some(
        (v) => isObject(v) && !Array.isArray(v)
      )

      if (hasNestedObjects) {
        // This object has nested objects, process them
        const processedValue: Record<string, unknown> = {}

        for (const [nestedKey, nestedValue] of Object.entries(valueObj)) {
          if (isObject(nestedValue) && !Array.isArray(nestedValue)) {
            // Check if this nested object has only primitive values
            const isLeafObject = Object.values(
              nestedValue as Record<string, unknown>
            ).every(
              (v) =>
                typeof v === "string" ||
                typeof v === "number" ||
                typeof v === "boolean"
            )

            if (isLeafObject) {
              // Flatten this nested object
              for (const [leafKey, leafValue] of Object.entries(
                nestedValue as Record<string, unknown>
              )) {
                const camelCaseKey = combineKeys(nestedKey, leafKey)
                processedValue[camelCaseKey] = leafValue
              }
            } else {
              // Recursively process this nested object
              processedValue[nestedKey] = flattenNestedObject(
                nestedValue as Record<string, unknown>
              )
            }
          } else {
            // Keep primitive values as is
            processedValue[nestedKey] = nestedValue
          }
        }

        result[key] = processedValue
      } else {
        // This object only has primitive values, keep as is
        result[key] = value
      }
    } else {
      // This is a primitive value
      result[key] = value
    }
  }

  return result
}

export function resolveTheme({
  scopeId,
  localTheme,
  parentTheme,
  mode,
  parentMode,
}: {
  scopeId: string
  localTheme: ThemeConfig
  mode: "light" | "dark" | undefined
  parentTheme?: ThemeConfig
  parentMode?: "light" | "dark"
}): Record<string, string | ThemeConfig | string[]> {
  const merged =
    localTheme.inherit && parentTheme
      ? overrideExistedKey(localTheme, parentTheme)
      : localTheme
  const resolvedMode = mode ?? parentMode ?? "light"

  const tokenCSS = merged.tokens
    ? parseTokenV2(
        overrideExistedKey(
          merged.tokens || {},
          ApolloToken
        ) as ApolloDefineToken
      ) // Must be override all tokens (CSS inheritance)
    : ""
  const css = scopeId
    ? `@layer apollo {.${scopeId} {\ncolor-scheme:${resolvedMode};\n${tokenCSS}\n}}`
    : ""

  return { css: css, theme: merged, mode: resolvedMode }
}
