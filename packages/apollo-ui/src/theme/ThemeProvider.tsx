import { HTMLElementType, useMemo, type ComponentType } from "react"
import { apolloTheme } from "@apollo/token"

import { animationStyles } from "./override/animation"
import { baseline } from "./override/baseline"
import { reactDatePickerStyleOverride } from "./override/react-datepicker"
import { zIndexStyles } from "./override/zIndex"
import type { ApolloDesignTokenConfig, ThemeProviderProps } from "./types"
import { getInjectorId, getThemeWrapperIdentityId, parseToken } from "./utils"

const defaultTheme = apolloTheme

const baseTheme: ApolloDesignTokenConfig = {
  ...defaultTheme?.colors,
  ...defaultTheme?.tokens,
}

export function ThemeProvider<WrapperComponentType>({
  children,
  scope,
  theme: propsTheme,
  WrapperComponent,
  ...wrapperProps
}: ThemeProviderProps<WrapperComponentType>) {
  /**
   * Fallback to default theme on `null` and `undefined`
   */
  const theme = useMemo(() => propsTheme ?? baseTheme, [propsTheme])

  const overrideStyles = [
    reactDatePickerStyleOverride,
    animationStyles,
    zIndexStyles,
    baseline,
  ]

  const cssVariables = theme ? parseToken(theme) : ""
  const variableScope = scope ?? ":root"

  const injectorId = `apl-${getInjectorId(variableScope)}`
  const wrapperId = getThemeWrapperIdentityId(injectorId)
  const hasWrapper = Boolean(scope)
  const cssSelector = hasWrapper
    ? `@layer legacy {[data-apl="${wrapperId}"], ${scope}`
    : "@layer base {:root"
  // Determine wrapper component
  let Wrapper: HTMLElementType | ComponentType
  if (WrapperComponent) {
    if (typeof WrapperComponent === "string") {
      Wrapper = WrapperComponent as HTMLElementType
    } else {
      Wrapper = WrapperComponent as ComponentType
    }
  } else {
    Wrapper = "div" as HTMLElementType
  }

  return (
    <>
      <style
        dangerouslySetInnerHTML={{
          __html:
            "@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai:wght@100;200;300;400;500;600;700&display=swap');",
        }}
        data-apl={`${injectorId}-font-family`}
      />
      <style
        dangerouslySetInnerHTML={{
          __html: overrideStyles?.join(""),
        }}
        data-apl={`${injectorId}-style-override`}
      />
      <style
        dangerouslySetInnerHTML={{
          __html: `${cssSelector} {${cssVariables}}}`,
        }}
        data-apl={`${injectorId}-theme`}
      />
      {hasWrapper ? (
        <Wrapper
          data-apl={wrapperId}
          {...(wrapperProps as WrapperComponentType)}
        >
          {children}
        </Wrapper>
      ) : (
        children
      )}
    </>
  )
}
