import React, { ComponentType, useCallback, useState } from "react"
import { Field as BaseField } from "@base-ui-components/react"
import { TextareaAutosize as BaseTextareaAutosize } from "@mui/base/TextareaAutosize"
import classNames from "classnames"

import { Field } from "../field"
import styles from "./textarea.module.css"
import type { NormalTextareaProps, TextareaProps } from "./TextareaProps"

const DEFAULT_MIN_ROWS = 3

export function Textarea({
  label,
  helperText,
  ref,
  rootRef,
  className,
  error,
  fullWidth,
  rootProps,
  fieldProps,
  required,
  rows,
  maxRows,
  size = "medium",
  minRows = DEFAULT_MIN_ROWS,
  labelDecorator,
  helperTextDecorator,
  hasCharacterCount,
  onChange,
  ...props
}: TextareaProps) {
  const TextareaElement =
    BaseTextareaAutosize as ComponentType<NormalTextareaProps>

  const textareaRef = React.useRef<HTMLTextAreaElement>(null)
  const [characterCount, setCharacterCount] = useState<number>(0)

  const handleClick = () => {
    textareaRef.current?.focus()
  }

  const handleChange = useCallback(
      (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange?.(event)
        setCharacterCount(event.target.value.length)
      },
      [onChange]
    )

  return (
    <Field
      {...fieldProps}
      label={label}
      disabled={fieldProps?.disabled || props?.disabled}
      required={required}
      helperText={helperText}
      error={error}
      labelDecorator={labelDecorator}
      helperTextDecorator={helperTextDecorator}
      hasCharacterCount={hasCharacterCount}
      characterCount={characterCount}
      maxLength={props?.maxLength}
      className={classNames("ApolloTextarea-fieldRoot", {
        [styles.textareaRootFullWidth]: fullWidth,
      })}
    >
      <BaseField.Control
        className={className}
        render={(controlProps) => (
          <div
            className={classNames(
              "ApolloTextarea-controlRoot",
              styles.textarea,
              styles.textareaRoot,
              {
                [styles.textareaRootFullWidth]: fullWidth,
                [styles.textareaRootError]: error,
                [styles.textareaElementDisabled]: fieldProps?.disabled || props?.disabled,
              },
              className
            )}
            {...rootProps}
            ref={rootRef}
            onClick={handleClick}
          >
            <TextareaElement
              {...controlProps}
              {...props}
              name={props?.name}
              placeholder={props?.placeholder}
              minRows={rows ?? minRows}
              maxRows={rows ?? maxRows}
              onChange={handleChange}
              className={classNames(
                "ApolloTextarea-textarea",
                styles.textareaElement,
                {
                  [styles.textareaSmall]: size === "small",
                }
              )}
              ref={(el) => {
                textareaRef.current = el
                if (typeof ref === "function") ref(el)
                else if (ref) ref.current = el
              }}
            />
          </div>
        )}
      />
    </Field>
  )
}
