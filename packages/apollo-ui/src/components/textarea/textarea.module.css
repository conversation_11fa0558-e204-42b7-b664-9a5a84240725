@layer legacy {
    .textarea {
        --apl-textarea-padding: unset;
        --apl-textarea-gap: var(--apl-space-gap-xs, 8px);
        --apl-textarea-border: var(--apl-colors-border-default, #D3D7E1);
        --apl-textarea-background: var(--apl-colors-surface-static-ui-default, #FFF);
        --apl-textarea-color: var(--apl-colors-content-default, #0C0E11);
        --apl-textarea-min-height-small: 56px;
        --apl-textarea-min-height-medium: 68px;
        /* hover */
        --apl-textarea-hover-border: var(--apl-colors-border-primary-subdued, #409261);
        /* focus */
        --apl-textarea-focus-border: var(--apl-colors-border-primary-default, #006D2E);
        /* textareaElement */
        --apl-textarea-element-padding: var(--apl-space-padding-xs, 8px) var(--apl-space-padding-md, 16px);
    }

    .textareaRootError {
        --apl-textarea-border: var(--apl-colors-border-danger-default, #E74747);
    }

    .textareaElementDisabled {
        --apl-textarea-border: var(--apl-colors-border-disabled, #BEC4D1);
        --apl-textarea-background: var(--apl-colors-surface-static-ui-disabled, #F6F7FB);
        --apl-textarea-color: var(--apl-colors-content-description, #5C6372);
    }
}

@layer apollo {
    .textarea {
        --apl-textarea-padding: 0px var(--apl-alias-spacing-padding-padding5, 8px) var(--apl-alias-spacing-padding-padding4, 6px) 0px;
        --apl-textarea-gap: var(--apl-alias-spacing-gap-gap3, 4px);
        --apl-textarea-border: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-textarea-background: var(--apl-alias-color-background-and-surface-background, #FFF);
        --apl-textarea-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        --apl-textarea-min-height-small: 56px;
        --apl-textarea-min-height-medium: 64px;
        /* hover */
        --apl-textarea-hover-border: var(--apl-alias-color-primary-hovered, #2C8745);
        /* focus */
        --apl-textarea-focus-border: var(--apl-alias-color-primary-focused, #49A25C);
        /* textareaElement */
        --apl-textarea-element-padding: var(--apl-alias-spacing-padding-padding4, 6px) 0px 0px var(--apl-alias-spacing-padding-padding5, 8px);
        --apl-textarea-placeholder: var(--apl-alias-color-background-and-surface-on-surface-variant, #787777);
    }

    .textareaRootError {
        --apl-textarea-border: var(--apl-alias-color-error-error, #C0000B);
    }

    .textareaElementDisabled {
        --apl-textarea-border: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-textarea-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-textarea-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
    }
}

.textareaRoot {
    min-width: 128px;
    display: inline-flex;
    justify-content: center;
    align-items: flex-start;
    gap: var(--apl-textarea-gap);
    padding: var(--apl-textarea-padding);
    border-radius: 8px;
    border: 1px solid var(--apl-textarea-border);
    background: var(--apl-textarea-background);
    color: var(--apl-textarea-color);
    box-shadow: 1px 1px 0px 0px rgba(154, 154, 154, 0.08);
    transition: background 150ms cubic-bezier(0.4, 0, 0.2, 1);

    &:hover:not(.textareaRootError):not(.textareaElementDisabled) {
        --apl-textarea-border: var(--apl-textarea-hover-border)
    }

    &:focus-within:not(.textareaRootError):not(.textareaElementDisabled) {
        --apl-textarea-border: var(--apl-textarea-focus-border)
    }

    ::placeholder {
        color: var(--apl-textarea-placeholder);
    }

    
}

.textareaRootFullWidth {
    width: 100%;
}


.textareaSmall {
    composes: apl-typography-body2 apl-typography-label-large from '../../base.module.css';
}


.textareaElement {
    width: 100%;
    min-width: 128px;
    background: transparent;
    border: none;
    overflow: hidden;
    outline: none;
    border-radius: 8px;
    padding: var(--apl-textarea-element-padding);

    composes: apl-typography-body-large from '../../base.module.css';
}