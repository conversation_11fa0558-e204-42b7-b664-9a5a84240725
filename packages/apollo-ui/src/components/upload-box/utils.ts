import { UploadBoxErrorState } from "./UploadBoxProps"

export type ValidationResult = {
  isValid: boolean
  errorMessage?: UploadBoxErrorState[]
}

export function validateFileByExtension(options: {
  file: File
  allowedExtensions: string[]
  maxSizeInBytes: number
}): ValidationResult {
  const { file, allowedExtensions, maxSizeInBytes } = options
  const errors: UploadBoxErrorState[] = []

  const fileExtension = file.name.split(".").pop()?.toLowerCase()

  if (allowedExtensions.length > 0) {
    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      errors.push({
        code: "INVALID_EXTENSION",
        message: `Invalid file extension. Allowed extensions are: ${allowedExtensions.join(", ")}`,
      })
    }
  }

  if (maxSizeInBytes > 0 && file.size > maxSizeInBytes) {
    errors.push({
      code: "LIMIT_FILE_SIZE",
      message: `File is too large. Maximum size is ${Number(maxSizeInBytes / (1024 * 1024)).toLocaleString("en-US", { maximumFractionDigits: 2, minimumFractionDigits: 0 })} MB.`,
    })
  }

  return {
    isValid: errors.length === 0,
    errorMessage: errors.length > 0 ? errors : undefined,
  }
}
