"use client"

import { useState } from "react"

import type {
  UploadBoxFile,
  UploadBoxFileState,
  UploadBoxProps,
} from "./UploadBoxProps"

export type UseUploadSingleFileOptions<T> = {
  onDelete?: () => void
  onCancelUpload?: (file: UploadBoxFile<false>) => void
  uploadFileFn: (file: File) => Promise<T>
}

export function useUploadSingleFile<T>(
  options: UseUploadSingleFileOptions<T>
): UploadBoxProps<false> {
  const [file, setFile] = useState<File | null>()
  const [fileState, setFileState] = useState<UploadBoxFileState | null>()

  const clearFile = () => {
    setFile(null)
    setFileState(null)
  }

  const handleDeleteSingleFile = () => {
    clearFile()
    options?.onDelete?.()
  }

  const handleOnCancelUpload = (file: UploadBoxFile<false>) => {
    clearFile()
    options?.onCancelUpload?.(file)
  }

  const handleFileUpload = async (file: File) => {
    setFile(file)
    setFileState({ key: file.name, uploading: true })
    await options.uploadFileFn(file)
    setFileState((prev) =>
      prev?.key === file.name ? { ...prev, uploading: false } : prev
    )
  }

  return {
    value: file,
    fileState,
    onUpload: handleFileUpload,
    onDelete: handleDeleteSingleFile,
    onCancelUpload: handleOnCancelUpload,
  }
}
