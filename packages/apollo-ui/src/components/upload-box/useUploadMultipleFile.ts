"use client"

import { useState } from "react"

import type {
  UploadBoxFile,
  UploadBoxFileState,
  UploadBoxProps,
} from "./UploadBoxProps"

export type UseUploadMultipleFileOptions<T> = {
  uploadFileFn: (file: File) => Promise<T>
  onDelete?: (fileIndex: number) => void
  onCancelUpload?: (file: UploadBoxFile<false>, fileIndex: number) => void
}

export function useUploadMultipleFile<T = File>(
  options: UseUploadMultipleFileOptions<T>
): UploadBoxProps<true> {
  const [files, setFiles] = useState<File[] | null>()
  const [fileState, setFileState] = useState<UploadBoxFileState[] | null>()

  const handleDeleteMultipleFile = (deleteFileIndex: number) => {
    setFiles((prev) => prev?.filter((_, index) => index !== deleteFileIndex))
    setFileState((prev) =>
      prev?.filter((_, index) => index !== deleteFileIndex)
    )
    options?.onDelete?.(deleteFileIndex)
  }

  const uploadSingleFile = async (file: File, index: number) => {
    await options.uploadFileFn(file)
    setFileState((prev) =>
      prev?.map((prevState) =>
        prevState.key === `${file.name}-${index}`
          ? { ...prevState, uploading: false }
          : prevState
      )
    )
  }

  const handleUploadFiles = (uploadingFile: File[]) => {
    setFiles((prev) => [...(prev ?? []), ...uploadingFile])
    const indexOffset = files?.length ?? 0
    const uploadingFileStates =
      uploadingFile?.map((file, index) => ({
        key: `${file.name}-${indexOffset + index}`,
        uploading: true,
      })) ?? []

    setFileState((prev) => [...(prev ?? []), ...uploadingFileStates])

    uploadingFile?.forEach((file, index) => {
      uploadSingleFile(file, index + indexOffset)
    })
  }

  const handleOnCancelUpload = (file: UploadBoxFile<false>, index: number) => {
    handleDeleteMultipleFile(index)
    options?.onCancelUpload?.(file, index)
  }

  return {
    value: files,
    fileState,
    onUpload: handleUploadFiles,
    onDelete: handleDeleteMultipleFile,
    onCancelUpload: handleOnCancelUpload,
    multiple: true,
  }
}
