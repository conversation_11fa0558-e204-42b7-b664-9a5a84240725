@layer legacy {
    .uploadBox {
        /* uploadBoxMultipleWrapper */
        --apl-upload-box-multiple-wrapper-border-color: var(--apl-colors-border-default);
        --apl-upload-box-multiple-wrapper-padding: var(--apl-space-padding-md);
        --apl-upload-box-multiple-wrapper-gap: var(--apl-space-gap-md);
        --apl-upload-box-multiple-wrapper-background: unset;
        /* uploadSection */
        --apl-upload-section-border-color: var(--apl-colors-border-default);
        --apl-upload-section-padding: var(--apl-space-padding-md);
        --apl-upload-section-gap: var(--apl-space-gap-md);
        --apl-upload-section-background: unset;
        /* fileConditionContainer */
        --apl-upload-condition-container-gap: var(--apl-space-gap-sm);
        --apl-upload-condition-container-color: var(--apl-colors-content-description);
        --apl-upload-condition-container-border: none;
        --apl-upload-condition-container-padding: 0px;
        /* fileConditionList */
        --apl-upload-condition-list-padding-left: var(--apl-space-padding-xl);
        /* uploadedFileItem */
        --apl-upload-file-item-padding: var(--apl-space-padding-md);
        --apl-upload-file-item-border: 1px dashed var(--apl-colors-border-default);
        --apl-upload-file-item-background-color: var(--apl-colors-surface-static-default2);
        /* uploadedFileItemInfo */
        --apl-upload-file-item-info-gap: var(--apl-space-gap-sm);
        /* uploadedFileList, uploadedMultipleModeFileList */
        --apl-upload-file-list-gap: var(--apl-space-gap-sm);

        --apl-upload-loading-container-background-color: var(--apl-colors-surface-static-default3);
        --apl-upload-loading-background-color: var(--apl-colors-surface-static-success-active);


    }

    .deleteIcon {
        color: var(--apl-colors-content-danger-default);
    }

    .uploadingText {
        color: var(--apl-colors-content-description);
    }
}

@layer apollo {
    .uploadBox {
        --apl-upload-color: var(--apl-alias-color-background-and-surface-on-surface);
        /* uploadBoxMultipleWrapper */
        --apl-upload-box-multiple-wrapper-border-color: var(--apl-alias-color-outline-and-border-outline);
        --apl-upload-box-multiple-wrapper-padding: var(--apl-alias-spacing-padding-padding8);
        --apl-upload-box-multiple-wrapper-gap: var(--apl-alias-spacing-gap-gap8);
        --apl-upload-box-multiple-wrapper-background: var(--apl-alias-color-background-and-surface-background);
        /* uploadSection */
        --apl-upload-section-border-color: var(--apl-alias-color-outline-and-border-outline);
        --apl-upload-section-padding: var(--apl-alias-spacing-padding-padding8);
        --apl-upload-section-gap: var(--apl-alias-spacing-gap-gap8);
        --apl-upload-section-background: var(--apl-alias-color-background-and-surface-background);
        /* fileConditionContainer */
        --apl-upload-condition-container-gap: var(--apl-alias-spacing-gap-gap8);
        --apl-upload-condition-container-color: var(--apl-upload-color);
        /* fileConditionList */
        --apl-upload-condition-list-padding-left: var(--apl-alias-spacing-padding-padding8);
        /* uploadedFileItem */
        --apl-upload-file-item-padding: var(--apl-alias-spacing-padding-padding8);
        --apl-upload-file-item-border: 1px dashed var(--apl-alias-color-outline-and-border-outline);
        --apl-upload-file-item-background-color: var(--apl-alias-color-background-and-surface-surface);
        --apl-upload-file-item-min-height: 72px;
        /* uploadedFileItemInfo */
        --apl-upload-file-item-info-gap: var(--apl-alias-spacing-gap-gap3);
        /* uploadedFileList, uploadedMultipleModeFileList */
        --apl-upload-file-list-gap: var(--apl-alias-spacing-gap-gap5);

        --apl-upload-loading-container-background-color: var(--apl-alias-color-background-and-surface-surface-variant);
        --apl-upload-loading-background-color: var(--apl-alias-color-primary-primary);

    }


    .deleteIcon {
        color: var(--apl-alias-color-error-error);
    }

    .uploadingText {
        color: var(--apl-alias-color-background-and-surface-on-surface);
    }

    .uploadedFileItemError {
        --apl-upload-file-item-border: 1px solid var(--apl-alias-color-error-hovered, #E9211E);
    }
}

.uploadBoxMultipleWrapper {
    border: 1px solid var(--apl-upload-box-multiple-wrapper-border-color);
    padding: var(--apl-upload-box-multiple-wrapper-padding);
    gap: var(--apl-upload-box-multiple-wrapper-gap);
    background: var(--apl-upload-box-multiple-wrapper-background);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

/* Main UploadBox Styles */
.formControl {
    width: 100%;
}

.uploadSection {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    border: 1px solid var(--apl-upload-section-border-color);
    padding: var(--apl-upload-section-padding);
    gap: var(--apl-upload-section-gap);
    background: var(--apl-upload-section-background);
}

.uploadSectionDisabled {
    --apl-upload-condition-container-color: var(--apl-alias-color-background-and-surface-text-icon-disabled);
    --apl-upload-section-background: var(--apl-alias-color-background-and-surface-container-disabled);
}

.fullWidth {
    width: 100%;
}

.fileConditionContainer {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: var(--apl-upload-condition-container-color);
    gap: var(--apl-upload-condition-container-gap);
}


.fileConditionList {
    list-style-type: disc;
    padding-left: var(--apl-upload-condition-list-padding-left);
    margin: 0;
}

.uploadButton {
    white-space: nowrap;
}

/* Uploaded File Item Styles */
.uploadedFileItem {
    width: 100%;
    padding: var(--apl-upload-file-item-padding);
    border: var(--apl-upload-file-item-border);
    border-radius: 0.5rem;
    background-color: var(--apl-upload-file-item-background-color);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    min-height: var(--apl-upload-file-item-min-height, unset);
    color: var(--apl-upload-color);
}

.uploadedFileItemContent {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    gap: var(--apl-alias-spacing-gap-gap5);
}

.uploadedFileItemInfoContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
}

.uploadedFileItemInfo {
    display: flex;
    flex-direction: row;
    gap: var(--apl-upload-file-item-info-gap);
    justify-content: flex-start;
    align-items: center;
}

.uploadedFileItemIcon {
    width: 1rem;
    height: 1rem;
}

/* Uploaded Files List Styles */
.uploadedFileList {
    display: flex;
    flex-direction: column;
    gap: var(--apl-upload-file-list-gap);
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    color: var(--apl-upload-color);
}

.uploadedMultipleModeFileList {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--apl-upload-file-list-gap);
    justify-content: flex-start;
    align-items: flex-start;
}

.uploadedSingleModeFileItem,
.uploadedMultipleModeFileItem {
    width: 100%;
}

/* Loading Indicator Styles */
.loadingIndicatorContainer {
    width: 100%;
    height: 0.5rem;
    background-color: var(--apl-upload-loading-container-background-color);
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
}

.loadingIndicator {
    background-color: var(--apl-upload-loading-background-color);
    height: 0.5rem;
    width: 50%;
    position: absolute;
    border-radius: 0.5rem;
    top: 0;
    left: 0;
    transition: transform;
    transform-origin: top left;
    animation: progressBarAnimation 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) 0s infinite normal none running;
}

@keyframes progressBarAnimation {
    0% {
        transform: translateX(-100%);
    }

    60% {
        transform: translateX(100%);
    }

    100% {
        transform: translateX(300%);
    }
}