/* eslint-disable @typescript-eslint/no-explicit-any */
import { ComponentProps } from "react"
import { Radio as BaseRadio } from "@base-ui-components/react/radio"
import { RadioGroup as BaseRadioGroup } from "@base-ui-components/react/radio-group"

export type RadioProps = {
  labelProps?: ComponentProps<"label">
} & BaseRadio.Root.Props


export type RadioGroupProps<T = any> = {
  direction?: "horizontal" | "vertical"
  value?: T
  defaultValue?: T
  onValueChange?: (value: T, event: Event) => void
} & Omit<BaseRadioGroup.Props, "value" | "defaultValue" | "onValueChange">
