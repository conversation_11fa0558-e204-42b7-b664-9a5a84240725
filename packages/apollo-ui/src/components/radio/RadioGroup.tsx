import { type PropsWithChildren } from "react"
import { RadioGroup as BaseRadioGroup } from "@base-ui-components/react/radio-group"
import classNames from "classnames"

import styles from "./radio.module.css"
import type { RadioGroupProps } from "./RadioProps"

export function RadioGroup<T>({
  direction = "vertical",
  className,
  children,
  ...radioGroupProps
}: PropsWithChildren<RadioGroupProps<T>>) {
  return (
    <BaseRadioGroup
      {...(radioGroupProps as BaseRadioGroup.Props)}
      className={classNames(
        "ApolloRadioGroup-root",
        styles.radio,
        styles.radioGroup,
        {
          [styles.radioGroupHorizontal]: direction === "horizontal",
        },
        className
      )}
    >
      {children}
    </BaseRadioGroup>
  )
}
