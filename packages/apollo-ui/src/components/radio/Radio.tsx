import { Radio as BaseRadio } from "@base-ui-components/react/radio"
import classNames from "classnames"

import { Typography } from "../typography"
import styles from "./radio.module.css"
import type { RadioProps } from "./RadioProps"

export const Radio: React.FC<RadioProps> = ({
  className,
  children,
  disabled,
  labelProps,
  ...radioProps
}) => {
  return (
    <label
      {...labelProps}
      className={classNames(
        "ApolloRadio-label",
        styles.radio,
        styles.radioLabel,
        labelProps?.className
      )}
    >
      <BaseRadio.Root
        {...radioProps}
        disabled={disabled}
        className={classNames("ApolloRadio-root", styles.radioRoot, className)}
      >
        <BaseRadio.Indicator
          className={classNames("ApolloRadio-indicator", styles.indicator)}
        />
      </BaseRadio.Root>
      {typeof children === "string" ? (
        <Typography
          className={classNames("ApolloRadio-labelText", {
            [styles.labelTextDisabled]: disabled,
          })}
          level="bodyLarge"
        >
          {children}
        </Typography>
      ) : (
        children
      )}
    </label>
  )
}
