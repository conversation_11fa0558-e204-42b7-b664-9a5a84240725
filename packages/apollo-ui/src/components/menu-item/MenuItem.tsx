import classNames from "classnames"

import { Typography } from "../typography"
import styles from "./menuItem.module.css"
import type { MenuItemProps } from "./MenuItemProps"

export function MenuItem(props: MenuItemProps) {
  const { label, className, selected, disabled, startDecoration, ...divProps } =
    props
  return (
    <div
      {...divProps}
      className={classNames(
        "ApolloMenuItem-root",
        styles.menuItem,
        styles.menuItemRoot,
        {
          [styles.menuItemRootSelected]: selected,
          [styles.menuItemRootDisabled]: disabled,
        },
        className
      )}
    >
      {startDecoration}
      <Typography level="bodyLarge">{label}</Typography>
    </div>
  )
}
