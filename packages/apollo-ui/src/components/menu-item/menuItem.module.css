@layer legacy {
    .menuItem {
        --apl-menu-item-background-color: var(--apl-colors-surface-static-ui-default);
        --apl-menu-item-selected-background-color-default: var(--apl-colors-surface-static-ui-primary, #F5FFF7);
        --apl-menu-item-selected-background-color: var(--apl-menu-item-selected-background-color-default);
        --apl-menu-item-hover-background-color: var(--apl-colors-surface-static-ui-hover);
        --apl-menu-item-text-color: var(--apl-colors-content-default);

        --apl-menu-item-disabled-background-color: var(--apl-colors-surface-static-ui-default);
        --apl-menu-item-disabled-text-color: var(--apl-colors-content-disabled);

        --apl-menu-item-padding: var(--apl-space-padding-xs) var(--apl-space-padding-xs);
        --apl-menu-item-gap: var(--apl-space-gap-xs);
    }
}

@layer apollo {
    .menuItem {
        --apl-menu-item-background-color: transparent;
        --apl-menu-item-selected-background-color-default: var(--apl-alias-color-primary-primary-container);
        --apl-menu-item-selected-background-color: var(--apl-alias-color-primary-primary-container);
        --apl-menu-item-hover-background-color: var(--apl-alias-color-background-and-surface-surface);
        --apl-menu-item-text-color: var(--apl-alias-color-background-and-surface-on-surface);

        --apl-menu-item-disabled-background-color: transparent;
        --apl-menu-item-disabled-text-color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);

        --apl-menu-item-padding: var(--apl-alias-spacing-padding-padding5, 8px);
        --apl-menu-item-gap: var(--apl-alias-spacing-gap-gap5, 8px);
    }
}

.menuItemRoot {

    width: 100%;
    outline: none;

    display: flex;
    padding: var(--apl-menu-item-padding);
    justify-content: flex-start;
    align-items: center;
    align-self: stretch;
    gap: var(--apl-menu-item-gap);
    cursor: pointer;

    background: var(--apl-menu-item-background-color);
    color: var(--apl-menu-item-text-color);

    composes: apl-transition-all from '../../base.module.css';

    &:hover,
    &[data-highlighted] {
        background-color: var(--apl-menu-item-hover-background-color);
    }

    &[data-checked],
    &[data-selected] {
        background-color: var(--apl-menu-item-selected-background-color);

        &:hover {
            background-color: var(--apl-menu-item-hover-background-color);
        }

    }


}

.menuItemRootSelected {
    --apl-menu-item-background-color: var(--apl-menu-item-selected-background-color-default);

    &:hover {
        --apl-menu-item-hover-background-color: var(--apl-menu-item-selected-background-color-default);
    }
}

.menuItemRootDisabled {
    --apl-menu-item-background-color: var(--apl-menu-item-disabled-background-color);
    --apl-menu-item-text-color: var(--apl-menu-item-disabled-text-color);

    &:hover {
        --apl-menu-item-hover-background-color: var(--apl-menu-item-disabled-background-color);
        --apl-menu-item-text-color: var(--apl-menu-item-disabled-text-color);
    }
}