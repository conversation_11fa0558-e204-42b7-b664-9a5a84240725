import { HTMLAttributes, PropsWithChildren } from "react"
import classNames from "classnames"

import styles from "./modalFooter.module.css"

export type ModalFooterProps = PropsWithChildren<HTMLAttributes<HTMLDivElement>>

export function ModalFooter({ children, ...footerProps }: ModalFooterProps) {
  return (
    <div
      {...footerProps}
      className={classNames(
        "ApolloModal-footer",
        styles.popupFooter,
        footerProps?.className
      )}
    >
      {children}
    </div>
  )
}
