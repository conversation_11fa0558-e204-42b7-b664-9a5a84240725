@layer legacy {
    .popupFooter {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        flex-wrap: wrap;

        gap: var(--apl-space-gap-xs);

        padding: var(--apl-space-padding-md);
        color: var(--apl-colors-content-default);
        max-width: 100%;
    }

    @media screen and (max-width: 768px) {
        .popupFooter {
            flex-direction: column;
            justify-content: center;
        }
    }
}

@layer apollo {
    .popupFooter {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        flex-wrap: wrap;

        gap: var(--apl-alias-spacing-padding-padding7);

        padding: 0;
        color: var(--apl-alias-color-background-and-surface-on-surface);
        max-width: 100%;
    }

    @media screen and (max-width: 768px) {
        .popupFooter {
            flex-direction: column;
            justify-content: center;
            :global(.ApolloButton-root) {
                width: 100%;
            }
        }
    }
}