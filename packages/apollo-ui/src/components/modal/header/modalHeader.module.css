@layer legacy {
    .popupHeader {
        display: flex;
        justify-content: flex-start;
        gap: var(--apl-space-padding-xs);
        align-items: center;
        padding: var(--apl-space-padding-md);
        padding-bottom: var(--apl-space-padding-xs);
        padding-right: calc(var(--apl-space-padding-md) + 32px);
        /* Space for close button */
        color: var(--apl-colors-content-default);
        position: relative;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Ensure text content can wrap properly */
    .popupHeader>* {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }

    @media screen and (max-width: 768px) {
        .popupHeader {
            padding: var(--apl-space-padding-xs);
            padding-bottom: var(--apl-space-padding-xs);
            /* padding-right: calc(var(--apl-space-padding-xs) + 32px); Space for close button */
            flex-direction: column;
        }
    }
}

@layer apollo {
    .popupHeader {
        display: flex;
        align-items: center;
        gap: var(--apl-alias-spacing-padding-padding7, 12px);
        align-self: stretch;
        color: var(--apl-alias-color-background-and-surface-on-surface);
        padding: 0;
        padding-right: 32px;
        /* Space for close button */
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    :global(.ApolloTypography--root) {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    /* Ensure text content can wrap properly */
    .popupHeader>* {
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }

    @media screen and (max-width: 768px) {
        .popupHeader {
            flex-direction: column;
            padding-right: 0
        }
    }
}