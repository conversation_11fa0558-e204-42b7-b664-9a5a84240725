import type { HTMLAttributes, PropsWithChildren, ReactNode } from "react"
import classNames from "classnames"

import styles from "./modalHeader.module.css"

export type ModalHeaderProps = PropsWithChildren<
  HTMLAttributes<HTMLDivElement> & {
    icon?: ReactNode
  }
>

export function ModalHeader({
  icon,
  children,
  ...headerProps
}: ModalHeaderProps) {
  return (
    <div
      {...headerProps}
      className={classNames(
        "ApolloModal-header",
        styles.popupHeader,
        headerProps.className
      )}
    >
      {icon}
      {children}
    </div>
  )
}
