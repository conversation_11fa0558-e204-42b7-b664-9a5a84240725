import { HTMLAttributes, PropsWithChildren } from "react"
import classNames from "classnames"

import styles from "./modalContent.module.css"

export type ModalContentProps = PropsWithChildren<
  HTMLAttributes<HTMLDivElement>
>

export function ModalContent({ children, ...contentProps }: ModalContentProps) {
  return (
    <div
      {...contentProps}
      className={classNames(
        "ApolloModal-content",
        styles.popupContent,
        contentProps?.className
      )}
    >
      {children}
    </div>
  )
}
