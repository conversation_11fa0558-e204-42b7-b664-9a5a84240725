@layer legacy {
    .popupContent {
        padding: var(--apl-space-padding-md);
        color: var(--apl-colors-content-default);
    }
}

@layer apollo {
    .popupContent {
        padding: 0;
        color: var(--apl-alias-color-background-and-surface-on-surface);
        flex: 1;
        min-height: 0; /* Allows flex item to shrink below content size */
        overflow-y: auto;
        max-height: calc(100vh - 200px); 
    }
}