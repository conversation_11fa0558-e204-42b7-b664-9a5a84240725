import { Dialog } from "@base-ui-components/react"
import classNames from "classnames"

import { Close } from "../../common/Close"
import { IconButton } from "../../icon-button"
import styles from "./modalCloseButton.module.css"

export type ModalCloseButtonProps = {} & Dialog.Close.Props

export function ModalCloseButton({ ...props }: ModalCloseButtonProps) {
  return (
    <Dialog.Close
      {...props}
      render={(baseProps, state) => {
        console.log("baseProps", baseProps);
        
        return (
        <IconButton
          tabIndex={-1}
          disabled={state.disabled}
          size="small"
          className={classNames(
            "ApolloModal-closeButton",
            styles.closeButton,
            styles.popupCloseButton
          )}
          {...baseProps}
          onClick={baseProps.onClick}
          
          color={undefined}
        >
          <Close width={16} height={16} />
        </IconButton>
      )
      }}
    />
  )
}
