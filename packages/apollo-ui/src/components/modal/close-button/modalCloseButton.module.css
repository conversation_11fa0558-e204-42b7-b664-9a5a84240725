@layer legacy {
    .closeButton {
        --apl-popup-close-button-top: var(--apl-space-padding-md);
        --apl-popup-close-button-right: var(--apl-space-padding-md);
        --apl-popup-close-button-color: unset;
    }
}

@layer apollo {
    .closeButton {
        --apl-popup-close-button-top: 0px;
        --apl-popup-close-button-right: 0px;
        --apl-popup-close-button-color: var(--apl-alias-color-background-and-surface-on-surface);
    }
}

.popupCloseButton {
    position: absolute;
    top: var(--apl-popup-close-button-top);
    right: var(--apl-popup-close-button-right);
    color: var(--apl-popup-close-button-color);
}