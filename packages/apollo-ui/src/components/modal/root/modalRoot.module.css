.backdrop {
    position: fixed;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 400;
}

@layer legacy {
    .popup {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 401;
        box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05);

        min-width: 250px;

        border-radius: 16px;
        background-color: var(--apl-colors-surface-static-default1);

        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        transition: all 150ms;

        &[data-starting-style],
        &[data-ending-style] {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
        }

    }
}

@layer apollo {
    .popup {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 401;
        box-shadow: var(--apl-alias-elevation-elevations1-x-axis, 0) var(--apl-alias-elevation-elevations1-y-axis, 2px) var(--apl-alias-elevation-elevations1-blur, 4px) var(--apl-alias-elevation-elevations1-spread, 0) var(--apl-base-color-overlay-black-10, rgba(0, 0, 0, 0.10));

        min-width: 250px;

        border-radius: var(--apl-alias-radius-radius8, 16px);
        background-color: var(--apl-alias-color-background-and-surface-background);

        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        padding: var(--apl-alias-spacing-padding-padding8, 16px);
        gap: var(--apl-alias-spacing-gap-gap8, 16px);

        transition: all 150ms;

        &[data-starting-style],
        &[data-ending-style] {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
        }

    }
}