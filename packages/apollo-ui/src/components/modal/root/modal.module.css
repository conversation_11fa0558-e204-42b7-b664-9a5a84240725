.backdrop {
    position: fixed;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 400;
}

.popup {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 401;
    box-shadow: 0 9px 28px 8px rgba(0, 0, 0, 0.05);

    min-width: 250px;

    border-radius: 16px;
    background-color: var(--apl-colors-surface-static-default1);

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;

}