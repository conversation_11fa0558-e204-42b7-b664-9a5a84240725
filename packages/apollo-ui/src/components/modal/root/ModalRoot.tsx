import type { PropsWithChildren } from "react"
import { Dialog } from "@base-ui-components/react/dialog"
import classNames from "classnames"

import styles from "./modalRoot.module.css"
import { Portal } from "../../portal"

type BaseModalProps = {
  rootProps?: Dialog.Root.Props
  portalProps?: Dialog.Portal.Props
  backdropProps?: Dialog.Backdrop.Props
  popupProps?: Dialog.Popup.Props
  className?: string
}

type BaseRootProps = Omit<Dialog.Root.Props, "children">

export type ModalProps = PropsWithChildren<BaseModalProps & BaseRootProps>

export function ModalRoot({
  // Root props
  open,
  onOpenChange,
  onOpenChangeComplete,
  defaultOpen,
  dismissible,
  // Slots Props
  children,
  rootProps,
  portalProps,
  backdropProps,
  popupProps,
  // Custom props
  className,
}: ModalProps) {
  return (
    <Dialog.Root
      {...rootProps}
      open={open}
      onOpenChange={onOpenChange}
      onOpenChangeComplete={onOpenChangeComplete}
      defaultOpen={defaultOpen}
      dismissible={dismissible}
    >
      <Portal baseComponent={<Dialog.Portal {...portalProps}/>}>
          <Dialog.Backdrop
            {...backdropProps}
            className={classNames("ApolloModal-backdrop", styles.backdrop)}
          />
          <Dialog.Popup
            {...popupProps}
            className={classNames("ApolloModal-popup", styles.popup, className)}
          >
            {children}
          </Dialog.Popup>
      </Portal>
    </Dialog.Root>
  )
}
