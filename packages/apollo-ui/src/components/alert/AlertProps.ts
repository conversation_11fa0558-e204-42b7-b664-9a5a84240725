import type {
  ComponentPropsWithoutRef,
  ReactEventHandler,
  ReactNode,
} from "react"

export type TypeProp = "success" | "information" | "warning" | "error"

export type AlertProps = Omit<ComponentPropsWithoutRef<"div">, "title"> & {
  /** @default "information" */
  type?: TypeProp

  fullWidth?: boolean
  title?: ReactNode
  description?: ReactNode

  startDecorator?: ReactNode
  endDecorator?: ReactNode
  onClose?: ReactEventHandler
  action?: ReactNode
  ref?: React.Ref<HTMLDivElement>
}
