import { cva } from "class-variance-authority"
import classNames from "classnames"

import { isTextElement } from "../../utils"
import { CheckCircle } from "../common/CheckCircle"
import { Close } from "../common/Close"
import { CloseCircle } from "../common/CloseCircle"
import { ExclamationCircle } from "../common/ExclamationCircle"
import { InfoCircle } from "../common/InfoCircle"
import { IconButton } from "../icon-button"
import { Typography } from "../typography"
import styles from "./alert.module.css"
import type { AlertProps, TypeProp } from "./AlertProps"

const alertVariants = cva(styles.alertRoot, {
  variants: {
    type: {
      success: styles.alertSuccess,
      information: styles.alertInformation,
      warning: styles.alertWarning,
      error: styles.alertError,
    },
    width: {
      default: styles.alertDefault,
      full: styles.alertFull,
    },
  },
  defaultVariants: {
    width: "default",
  },
})

export function Alert(props: AlertProps) {
  const {
    type = "information",
    fullWidth,
    title,
    description,
    endDecorator,
    startDecorator,
    onClose,
    action,
    className,
    style,
    ref,
    ...restProps
  } = props

  const getIcon = (type: TypeProp | undefined) => {
    switch (type) {
      case "success":
        return <CheckCircle />
      case "warning":
        return <ExclamationCircle />
      case "error":
        return <CloseCircle />
      default:
        return <InfoCircle />
    }
  }

  return (
    <div
      style={style}
      className={classNames(
        "ApolloAlert-root",
        styles.alert,
        alertVariants({ type, width: fullWidth ? "full" : "default" }),
        className
      )}
      ref={ref}
      role="alert"
      {...restProps}
    >
      <div className={classNames(styles.alertStartDecorator, "ApolloAlert-startDecorator")}>
        {startDecorator ? startDecorator : getIcon(type)}
      </div>
      <div className={styles.alertContentContainer}>
        <div className={styles.alertContent}>
          {isTextElement(title) ? (
            <Typography
              level="bodyLarge"
              className={classNames(styles.alertTitle, "ApolloAlert-title")}
            >
              {title}
            </Typography>
          ) : (
            title
          )}
          {isTextElement(description) ? (
            <Typography level="bodyMedium" className="ApolloAlert-description">
              {description}
            </Typography>
          ) : (
            description
          )}
        </div>
        {action ? <div className="ApolloAlert-action">{action}</div> : null}
      </div>
      {endDecorator ? (
        <div className="ApolloAlert-endDecorator">{endDecorator}</div>
      ) : null}
      {onClose ? (
        <IconButton
          className={classNames(
            "ApolloAlert-closeButton",
            styles.alertCloseButton
          )}
          onClick={onClose}
          size="small"
          type="button"
        >
          <Close width={18} height={18} />
        </IconButton>
      ) : null}
    </div>
  )
}
