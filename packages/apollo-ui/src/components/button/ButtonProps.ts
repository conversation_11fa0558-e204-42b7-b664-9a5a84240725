import type {
  AnchorHTMLAttributes,
  ButtonHTMLAttributes,
  ReactNode,
  Ref,
} from "react"

export type BaseButtonProps = {
  fullWidth?: boolean
  variant?: "filled" | "outline" | "text"
  size?: "large" | "small"
  color?: "primary" | "negative"
  startDecorator?: ReactNode
  endDecorator?: ReactNode
  ref?: Ref<HTMLButtonElement>
}

export type LinkButtonProps = {
  href: string
  fileType?: string
} & Omit<AnchorHTMLAttributes<HTMLAnchorElement>, "type">

export type NormalButtonProps = ButtonHTMLAttributes<HTMLButtonElement>

export type ButtonProps = (
  | ({ href: string } & LinkButtonProps)
  | ({
      href?: never
    } & NormalButtonProps)
) &
  BaseButtonProps
