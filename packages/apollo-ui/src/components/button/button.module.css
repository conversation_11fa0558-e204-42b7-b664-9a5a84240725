@layer legacy {
    .buttonRoot {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        gap: var(--apl-space-gap-xs, 8px);
        padding: var(--apl-space-padding-xs, 8px) var(--apl-space-padding-md, 16px);
        border-radius: 8px;
        font-weight: 500;
        text-align: center;
        transition: background 150ms cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        --apl-button-large-font-size: var(--apl-typography-h4-font-size);
        --apl-button-small-font-size: var(--apl-typography-h5-font-size);
        /** Base Color Variables **/
        --apl-button-filled-border: 1px solid var(--apl-button-primary-color);
        --apl-button-outline-color: var(--apl-colors-border-focus, #D5F0E0);
        --apl-button-outline: 3px solid var(--apl-button-outline-color);
        --apl-button-disabled-bg-color: var(--apl-colors-surface-action-primary-disabled, #F6F7FB);
        --apl-button-disabled-color: var(--apl-colors-border-disabled, #BEC4D1);
        --apl-button-disabled-border-color: var(--apl-colors-border-disabled, #BEC4D1);
        --apl-button-primary-disabled-color: var(--apl-button-disabled-color);
        --apl-button-plain-hover-bg: var(--apl-colors-surface-static-ui-hover, #F6F7FB);
        --apl-button-plain-selected-bg: var(--apl-colors-surface-static-ui-active, #E9EBF0);

        --apl-button-primary-active-bg-color: var(--apl-button-primary-dark-color);
        --apl-button-primary-focus-bg-color: var(--apl-button-primary-color);
        --apl-button-primary-hover-bg-color: var(--apl-button-primary-light-color);
        --apl-button-outline-hover-text-color: var(--apl-button-primary-light-color);
        --apl-button-outline-hover-border-color: var(--apl-button-primary-light-color);
        --apl-button-outline-active-text-color: var(--apl-button-primary-color);
        --apl-button-outline-active-border-color: var(--apl-button-primary-dark-color);
        --apl-button-outline-focus-text-color: var(--apl-button-outline-active-text-color);
        --apl-button-outline-focus-border-color: var(--apl-button-outline-active-border-color);
        --apl-button-plain-hover-text-color: var(--apl-button-primary-text-color);
    }

    .buttonPrimary {
        /** Primary Color Variables **/
        --apl-button-primary-text-color: var(--apl-colors-content-primary-default, #006D2E);
        --apl-button-primary-contrast-text-color: var(--apl-colors-content-inversed, #FFF);
        --apl-button-primary-color: var(--apl-colors-surface-action-primary-default, #006D2E);
        --apl-button-primary-light-color: var(--apl-colors-surface-action-primary-hover, #409261);
        --apl-button-primary-dark-color: var(--apl-colors-surface-action-primary-active, #004A1F);
    }

    .buttonNegative {
        /** Negative Color Variables **/
        --apl-button-primary-text-color: var(--apl-colors-surface-action-delete-default, #E74747);
        --apl-button-primary-contrast-text-color: var(--apl-colors-content-inversed, #FFF);
        --apl-button-primary-color: var(--apl-colors-surface-action-delete-default, #E74747);
        --apl-button-primary-light-color: var(--apl-colors-surface-action-delete-hover, #EA8484);
        --apl-button-primary-dark-color: var(--apl-colors-surface-action-delete-active, #B41414);
    }

}

@layer apollo {
    .buttonRoot {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        gap: var(--apl-alias-spacing-gap-gap4, 6px);
        padding: var(--apl-alias-spacing-padding-padding4, 6px) var(--apl-alias-spacing-padding-padding8, 16px);
        border-radius: var(--apl-alias-radius-radius4, 8px);
        font-weight: 500;
        text-align: center;
        transition: background 150ms cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        --apl-button-large-font-size: var(--apl-alias-typography-body-large-font-size);
        --apl-button-small-font-size: var(--apl-alias-typography-body-large-font-size);
        --apl-button-height: 48px;
        /** Base Color Variables **/
        --apl-button-filled-border: none;
        --apl-button-outline: none;
        --apl-button-disabled-bg-color: var(--apl-alias-color-background-and-surface-container-disabled, #F3F0F0);
        --apl-button-disabled-color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
        --apl-button-disabled-border-color: var(--apl-alias-color-outline-and-border-border-disabled, #ADABAB);
        --apl-button-primary-disabled-color: var(--apl-button-disabled-color);
        --apl-button-plain-hover-bg: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-button-plain-selected-bg: transparent;

        --apl-button-primary-active-text-color: var(--apl-button-primary-contrast-text-color);
        --apl-button-primary-active-bg-color: var(--apl-button-active-color);
        --apl-button-primary-focus-bg-color: var(--apl-button-focus-color);
        --apl-button-primary-hover-bg-color: var(--apl-button-hover-color);
        --apl-button-outline-hover-text-color: var(--apl-button-hover-color);
        --apl-button-outline-hover-border-color: var(--apl-button-hover-color);
        --apl-button-outline-focus-text-color: var(--apl-button-focus-color);
        --apl-button-outline-focus-border-color: var(--apl-button-focus-color);
        --apl-button-outline-active-text-color: var(--apl-button-active-color);
        --apl-button-outline-active-border-color: var(--apl-button-active-color);
        --apl-button-plain-hover-text-color: var(--apl-button-hover-color);

    }


    .buttonPrimary {
        /** Primary Color Variables **/
        --apl-button-primary-text-color: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-button-primary-contrast-text-color: var(--apl-alias-color-primary-on-primary, #FFFFFF);
        --apl-button-primary-color: var(--apl-alias-color-primary-primary, #016E2E);
        /* state */
        --apl-button-hover-color: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-button-focus-color: var(--apl-alias-color-primary-focused, #49A25C);
        --apl-button-active-color: var(--apl-alias-color-primary-pressed, #005321);
        --apl-button-plain-hover-bg: var(--apl-alias-color-primary-text-only-background-hovered, #E5E2E2);
    }

    .buttonNegative {
        /** Negative Color Variables **/
        --apl-button-primary-text-color: var(--apl-alias-color-error-error, #C0000B);
        --apl-button-primary-contrast-text-color: var(--apl-alias-color-error-on-error, #FFFFFF);
        --apl-button-primary-color: var(--apl-alias-color-error-error, #C0000B);
        /* state */
        --apl-button-hover-color: var(--apl-alias-color-error-hovered, #E9211E);
        --apl-button-focus-color: var(--apl-alias-color-error-focused, #FF5546);
        --apl-button-active-color: var(--apl-alias-color-error-pressed, #930006);
        --apl-button-plain-hover-bg: var(--apl-alias-color-error-text-only-background-hovered, #E5E2E2);

        --apl-button-disabled-color: var(--apl-alias-color-error-disabled, #FFDAD5);
        --apl-button-disabled-bg-color: var(--apl-alias-color-error-container-disabled, #FFF4F3);
        --apl-button-disabled-border-color: var(--apl-button-disabled-color);
        --apl-button-primary-disabled-color: var(--apl-alias-error-text-icon-disabled, #C0000B);

    }

}




.buttonLarge {
    /** Extend bodyLarge typography style */
    composes: apl-typography-body-large-emphasized from '../../base.module.css';
    font-size: var(--apl-button-large-font-size);
    min-width: 48px;
    height: var(--apl-button-height, 42px);
    min-height: var(--apl-button-height, 42px);
    max-height: var(--apl-button-height, 42px);
}

.buttonSmall {
    /** Extend bodyLarge typography style */
    composes: apl-typography-body-large-emphasized from '../../base.module.css';
    font-size: var(--apl-button-small-font-size);
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    min-width: 48px;
}

.buttonFullWidth {
    width: 100%;
}

.buttonFilled {
    color: var(--apl-button-primary-contrast-text-color);
    background-color: var(--apl-button-primary-color);
    border: var(--apl-button-filled-border);

    &:enabled,
    &[href] {
        &:hover {
            background: var(--apl-button-primary-hover-bg-color);
        }

        &:active {
            background: var(--apl-button-primary-active-bg-color);
            color: var(--apl-button-primary-active-text-color);
        }

        &:focus-visible {
            outline: var(--apl-button-outline);
            background: var(--apl-button-primary-focus-bg-color);
        }
    }

    &:disabled {
        color: var(--apl-button-primary-disabled-color);
        border-color: var(--apl-button-disabled-border-color);
        background: var(--apl-button-disabled-bg-color);
    }

}

.buttonOutline {
    color: var(--apl-button-primary-text-color);
    border: 1px solid var(--apl-button-primary-color);
    background-color: transparent;

    &:enabled,
    &[href] {
        &:hover {
            border: 1px solid var(--apl-button-outline-hover-border-color);
            color: var(--apl-button-outline-hover-text-color);
        }

        &:active {
            color: var(--apl-button-outline-active-text-color);
            border: 1px solid var(--apl-button-outline-active-border-color);
        }

        &:focus-visible {
            outline: var(--apl-button-outline);
            color: var(--apl-button-outline-focus-text-color);
            border: 1px solid var(--apl-button-outline-focus-border-color);

        }
    }

    &:disabled {
        color: var(--apl-button-disabled-color);
        border: 1px solid var(--apl-button-disabled-color);
    }
}

.buttonText {
    color: var(--apl-button-primary-color);
    background-color: transparent;
    border: 0;

    &[href] {
        text-decoration-line: underline;
        text-decoration-style: solid;
        text-decoration-skip-ink: auto;
        text-decoration-thickness: auto;
        text-underline-offset: auto;
    }

    &:enabled,
    &[href] {
        &:hover {
            background: var(--apl-button-plain-hover-bg);
            color: var(--apl-button-plain-hover-text-color);
        }

        &:active {
            background: var(--apl-button-plain-selected-bg);
            color: var(--apl-button-outline-active-text-color);
        }

        &:focus-visible {
            outline: var(--apl-button-outline);
            color: var(--apl-button-outline-focus-text-color);
        }
    }

    &:disabled {
        color: var(--apl-button-disabled-color);
    }
}

.buttonWithDecorators {
    justify-content: space-between;

}

.buttonWithDecoratorLabel {
    flex: 1;
    text-align: center;
}

.buttonDecorator {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}
