@layer legacy {
    .checkbox {
        --apl-checkbox-size: 16px;
        --apl-checkbox-root-gap: var(--apl-space-gap-xs, 8px);
        --apl-checkbox-border-radius: 2px;
        --apl-checkbox-border-color: var(--apl-colors-border-default);
        --apl-checkbox-background: var(--apl-colors-surface-static-ui-default);
        --apl-checkbox-text-color: var(--apl-colors-content-default);
        /* hover */
        --apl-checkbox-hover-border-color: var(--apl-colors-border-primary-subdued);
        /* focus */
        --apl-checkbox-focus-outline: 2px solid var(--apl-colors-border-focus, #D5F0E0);
        --apl-checkbox-focus-outline-offset: 2px;
        /* active */
        --apl-checkbox-active-outline: 2px solid var(--apl-colors-border-focus, #D5F0E0);
        /* disabled */
        --apl-checkbox-disabled-border-color: var(--apl-colors-border-default);
        --apl-checkbox-disabled-background: var(--apl-colors-surface-static-ui-disabled);
        --apl-checkbox-disabled-text-color: var(--apl-colors-content-disabled);
        /* data-checked */
        --apl-checkbox-checked-after-width: 10px;
        --apl-checkbox-checked-after-height: 6px;
        --apl-checkbox-checked-after-background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2210.62%22%20height%3D%227.62%22%20viewBox%3D%220%200%2012%208%22%20fill%3D%22none%22%3E%3Cpath%20d%3D%22M5.10536%207.60407L5.08774%207.62169L0.687897%203.22185L2.12057%201.78917L5.10542%204.77402L9.87944%200L11.3121%201.43268L5.12304%207.62175L5.10536%207.60407Z%22%20fill%3D%22white%22%20/%3E%3C/svg%3E');
        --apl-checkbox-checked-border-color: var(--apl-colors-surface-action-primary-default);
        --apl-checkbox-checked-background: var(--apl-colors-surface-action-primary-default);
        --apl-checkbox-checked-hover-border-color: var(--apl-colors-surface-action-primary-hover);
        --apl-checkbox-checked-hover-background: var(--apl-colors-surface-action-primary-hover);
        --apl-checkbox-checked-disabled-background: var(--apl-colors-surface-static-ui-disabled);
        --apl-checkbox-checked-disabled-after-background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2210.62%22%20height%3D%227.62%22%20viewBox%3D%220%200%2012%208%22%20fill%3D%22none%22%3E%3Cpath%20d%3D%22M5.10536%207.60407L5.08774%207.62169L0.687897%203.22185L2.12057%201.78917L5.10542%204.77402L9.87944%200L11.3121%201.43268L5.12304%207.62175L5.10536%207.60407Z%22%20fill%3D%22%23BEC4D1%22%20/%3E%3C/svg%3E');
        /* data-indeterminate */
        --apl-checkbox-indeterminate-after-size: 8px;
        --apl-checkbox-indeterminate-border-color: var(--apl-colors-border-default);
        --apl-checkbox-indeterminate-background: var(--apl-colors-surface-static-ui-default);
        --apl-checkbox-indeterminate-after-background: var(--apl-colors-surface-action-primary-default);
        --apl-checkbox-indeterminate-hover-border-color: var(--apl-colors-border-default);
        --apl-checkbox-indeterminate-hover-after-background: var(--apl-colors-surface-action-primary-hover);
        --apl-checkbox-indeterminate-disabled-background: var(--apl-colors-surface-static-ui-disabled);
        --apl-checkbox-indeterminate-disabled-after-background: var(--apl-colors-content-disabled);
    }
}

@layer apollo {
    .checkbox {
        --apl-checkbox-size: 18px;
        --apl-checkbox-root-gap: var(--apl-alias-spacing-gap-gap4, 6px);
        --apl-checkbox-border-radius: var(--apl-alias-radius-radius2, 4px);
        --apl-checkbox-border-color: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-checkbox-background: var(--apl-alias-color-background-and-surface-background, #FFF);
        --apl-checkbox-text-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        /* state */
        --apl-checkbox-hover: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-checkbox-focus: var(--apl-alias-color-primary-focused, #49A25C);
        --apl-checkbox-active: var(--apl-alias-color-primary-pressed, #005321);
        --apl-checkbox-disabled: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
        /* hover */
        --apl-checkbox-hover-border-color: var(--apl-checkbox-hover);
        /* focus */
        --apl-checkbox-focus-outline: 1px solid var(--apl-alias-color-primary-primary-container, #C5FFC8);
        --apl-checkbox-focus-outline-offset: 1px;
        --apl-checkbox-focus-border-color: var(--apl-checkbox-focus);
        /* active */
        --apl-checkbox-active-outline: 2px solid var(--apl-alias-color-primary-primary-container, #C5FFC8);
        --apl-checkbox-active-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-checkbox-active-border-color: var(--apl-checkbox-active);
        /* disabled */
        --apl-checkbox-disabled-border-color: var(--apl-checkbox-disabled);
        --apl-checkbox-disabled-background: transparent;
        --apl-checkbox-disabled-text-color: var(--apl-checkbox-disabled);
        /* data-checked */
        --apl-checkbox-checked-after-width: 11.667px;
        --apl-checkbox-checked-after-height: 9.333px;
        --apl-checkbox-checked-after-background-image: url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.7187 0.333313H10.7194C10.5793 0.333313 10.4463 0.398546 10.3605 0.510166L4.46594 8.08149L1.63946 4.45021C1.5967 4.39517 1.5422 4.35066 1.48005 4.32003C1.4179 4.2894 1.34971 4.27345 1.28061 4.27336H0.281262C0.185474 4.27336 0.132575 4.38498 0.191192 4.46036L4.10709 9.49052C4.29009 9.72536 4.64179 9.72536 4.82622 9.49052L11.8088 0.518863C11.8674 0.444933 11.8145 0.333313 11.7187 0.333313V0.333313Z' fill='white'/%3E%3C/svg%3E ");
        --apl-checkbox-checked-border-color: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-checkbox-checked-background: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-checkbox-checked-hover-border-color: var(--apl-checkbox-hover);
        --apl-checkbox-checked-hover-background: var(--apl-checkbox-hover);
        --apl-checkbox-checked-focus-border-color: var(--apl-checkbox-focus);
        --apl-checkbox-checked-focus-background: var(--apl-checkbox-focus);
        --apl-checkbox-checked-active-border-color: var(--apl-checkbox-active);
        --apl-checkbox-checked-active-background: var(--apl-checkbox-active);
        --apl-checkbox-checked-disabled-background: var(--apl-checkbox-disabled);
        --apl-checkbox-checked-disabled-after-background-image: var(--apl-checkbox-checked-after-background-image);
        /* data-indeterminate */
        --apl-checkbox-indeterminate-after-size: 10px;
        --apl-checkbox-indeterminate-border-color: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-checkbox-indeterminate-background: var(--apl-alias-color-background-and-surface-background, #FFF);
        --apl-checkbox-indeterminate-after-background: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-checkbox-indeterminate-hover-border-color: var(--apl-checkbox-hover);
        --apl-checkbox-indeterminate-hover-after-background: var(--apl-checkbox-hover);
        --apl-checkbox-indeterminate-focus-border-color: var(--apl-checkbox-focus);
        --apl-checkbox-indeterminate-focus-after-background: var(--apl-checkbox-focus);
        --apl-checkbox-indeterminate-active-border-color: var(--apl-checkbox-active);
        --apl-checkbox-indeterminate-active-after-background: var(--apl-checkbox-active);
        --apl-checkbox-indeterminate-disabled-background: transparent;
        --apl-checkbox-indeterminate-disabled-after-background: var(--apl-alias-color-background-and-surface-on-surface-variant, #787777);
    }
}

.checkboxLabelRoot {
    display: inline-flex;
    justify-content: flex-start;
    gap: var(--apl-checkbox-root-gap);
}

.checkboxWrapper {
    position: relative;
    width: var(--apl-checkbox-size);
    height: var(--apl-checkbox-size);
}

.checkboxRoot {
    appearance: none;
    position: relative;
    width: var(--apl-checkbox-size);
    min-width: var(--apl-checkbox-size);
    height: var(--apl-checkbox-size);
    min-height: var(--apl-checkbox-size);
    margin: 0;
    cursor: pointer;
    border-radius: var(--apl-checkbox-border-radius);
    border: 1px solid var(--apl-checkbox-border-color);
    background: var(--apl-checkbox-background);
    composes: apl-transition-all from '../../base.module.css';

    &:hover:not([data-disabled]) {
        border-color: var(--apl-checkbox-hover-border-color);
    }

    &:focus-visible {
        outline: var(--apl-checkbox-focus-outline);
        outline-offset: var(--apl-checkbox-focus-outline-offset);
        border-color: var(--apl-checkbox-focus-border-color);
    }

    &:active:not([data-disabled]) {
        outline: var(--apl-checkbox-active-outline);
        border-color: var(--apl-checkbox-active-border-color);
        background: var(--apl-checkbox-active-background);
    }


    &[data-disabled] {
        cursor: default;
        border-color: var(--apl-checkbox-disabled-border-color);
        background: var(--apl-checkbox-disabled-background);

        &+.ApolloCheckbox-label {
            cursor: default;
            color: var(--apl-checkbox-disabled-text-color);
        }

    }

    &[data-checked] {
        border-color: var(--apl-checkbox-checked-border-color);
        background: var(--apl-checkbox-checked-background);

        &::after {
            content: "";
            position: absolute;
            left: 2px;
            top: 4px;
            width: var(--apl-checkbox-checked-after-width);
            height: var(--apl-checkbox-checked-after-height);
            background-image: var(--apl-checkbox-checked-after-background-image);
            background-repeat: no-repeat;
            background-position: center;
        }

        &:hover:not([data-disabled]) {
            border-color: var(--apl-checkbox-checked-hover-border-color);
            background: var(--apl-checkbox-checked-hover-background);
        }

        &:focus-visible:not([data-disabled]) {
            border-color: var(--apl-checkbox-checked-focus-border-color);
            background: var(--apl-checkbox-checked-focus-background);
        }

        &:active:not([data-disabled]) {
            border-color: var(--apl-checkbox-checked-active-border-color);
            background: var(--apl-checkbox-checked-active-background);
        }

        &[data-disabled] {
            border-color: var(--apl-checkbox-disabled-border-color);
            background: var(--apl-checkbox-checked-disabled-background);

            &::after {
                background-image: var(--apl-checkbox-checked-disabled-after-background-image);
            }
        }
    }

    &[data-indeterminate] {
        border: 1px solid var(--apl-checkbox-indeterminate-border-color);
        background: var(--apl-checkbox-indeterminate-background);

        &::after {
            content: "";
            position: absolute;
            left: 3px;
            top: 3px;
            width: var(--apl-checkbox-indeterminate-after-size);
            height: var(--apl-checkbox-indeterminate-after-size);
            background: var(--apl-checkbox-indeterminate-after-background);
            border-radius: 1px;
        }

        &:hover:not([data-disabled]) {
            border: 1px solid var(--apl-checkbox-indeterminate-hover-border-color);

            &::after {
                background: var(--apl-checkbox-indeterminate-hover-after-background);
            }
        }

        &:focus-visible:not([data-disabled]) {
            border: 1px solid var(--apl-checkbox-indeterminate-focus-border-color);

            &::after {
                background: var(--apl-checkbox-indeterminate-focus-after-background);
            }
        }

        &:active:not([data-disabled]) {
            border: 1px solid var(--apl-checkbox-indeterminate-active-border-color);

            &::after {
                background: var(--apl-checkbox-indeterminate-active-after-background);
            }
        }

        &[data-disabled] {
            border-color: var(--apl-checkbox-disabled-border-color);
            background: var(--apl-checkbox-indeterminate-disabled-background);

            &::after {
                background: var(--apl-checkbox-indeterminate-disabled-after-background);
            }
        }
    }
}

.label {
    cursor: pointer;
    color: var(--apl-checkbox-text-color);
    margin: 0px;
}

.checkboxWithLabel {
    margin-top: 2px;
}

.checkboxLabelDisabled {
    cursor: default;
    color: var(--apl-checkbox-disabled-text-color);
}