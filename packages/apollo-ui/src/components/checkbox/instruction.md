## Checkbox component create instruction

- You will create a Checkbox component for me using base-ui as the core of the component. I have provide the information for you about base-ui library below
- You need to fix all mismatch CSS variable with `apolloTheme` files
- I will add a group of files as example for you (In this case will be Button files). You need to keep the creating file consist with the examples
- You need to create file follow the information below.
- Please add the component to the labs app. There will be a playground that I'm using for testing and develop component. Please check on page.tsx about pattern. You will see the examples Component that I added for each component. You also need to create a new file for Checkbox (checkboxs.tsx) and import to the page.tsx file.

For the instruction below. be execute only after I tell you to do the unit-test. Let focusing on the above instruction first.

- You also need to create unit-test for this component which I will add an unit-test file as an example. You need to generate as a new file for the checkbox component

## File Structure

- checkbox.module.css - CSS module files Ref: https://github.com/css-modules/css-modules
- Checkbox.tsx - The main component file
- CheckboxProps.tsx - The type file for component
- index.tsx - export component and types

## Library

- base-ui - https://base-ui.com/react/components/checkbox

## Styles from Figma

### Unselect

Styling for unselect mode

- normal state

```css
border-radius: 2px;
border: 10px solid var(--border-default, #D3D7E1);
background: var(--surface-static-ui-default, #FFF);
```

- hover state

```css
border-radius: 2px;
border: 10px solid var(--border-primary-subdued, #409261);
background: var(--surface-static-ui-default, #FFF);
```

- disabled state

```css
border-radius: 2px;
border: 10px solid var(--border-default, #D3D7E1);
background: var(--surface-static-ui-disabled, #F6F7FB);
```

### Select

Styling for Selected mode. You also need to embed this svg as check icon

```svg
<svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" fill="none">
  <path d="M5.10536 7.60407L5.08774 7.62169L0.687897 3.22185L2.12057 1.78917L5.10542 4.77402L9.87944 0L11.3121 1.43268L5.12304 7.62175L5.10536 7.60407Z" fill="#BEC4D1"/>
</svg>
```

- normal state

```css
border-radius: 2px;
border: 1px solid var(--surface-action-primary-default, #006D2E);
background: var(--surface-action-primary-default, #006D2E);
```

- hover state

```css
border-radius: 2px;
border: 1px solid var(--surface-action-primary-hover, #409261);
background: var(--surface-action-primary-hover, #409261);
```

- disabled state

```css
border-radius: 2px;
border: 1px solid var(--border-default, #D3D7E1);
background: var(--surface-static-ui-disabled, #F6F7FB);
```

### Indeterminate

Styling for indeterminate mode. You also need to embed this svg as check icon

The styles of each state are the same as default but differences will be the square box at the center. So the snippet below for the square box only

- normal state

```css
width: 10px;
height: 10px;
position: absolute;
left: 3px;
top: 3px;
border-radius: 1px;
background: var(--surface-action-primary-default, #006D2E);
```

- hover state

```css
width: 10px;
height: 10px;
position: absolute;
left: 3px;
top: 3px;
border-radius: 1px;
background: var(--surface-action-primary-default, #006D2E);
```

- disabled state

```css
width: 10px;
height: 10px;
position: absolute;
left: 3px;
top: 3px;
border-radius: 1px;
background: var(--content-disabled, #BEC4D1);
```

## The Example of Usage from `base-ui`

```tsx
import * as React from "react"
import { Checkbox } from "@base-ui-components/react/checkbox"

import styles from "./index.module.css"

export default function ExampleCheckbox() {
  return (
    <label className={styles.Label}>
      <Checkbox.Root defaultChecked className={styles.Checkbox}>
        <Checkbox.Indicator className={styles.Indicator}>
          <CheckIcon className={styles.Icon} />
        </Checkbox.Indicator>
      </Checkbox.Root>
      Enable notifications
    </label>
  )
}

function CheckIcon(props: React.ComponentProps<"svg">) {
  return (
    <svg
      fill="currentcolor"
      width="10"
      height="10"
      viewBox="0 0 10 10"
      {...props}
    >
      <path d="M9.1603 1.12218C9.50684 1.34873 9.60427 1.81354 9.37792 2.16038L5.13603 8.66012C5.01614 8.8438 4.82192 8.96576 4.60451 8.99384C4.3871 9.02194 4.1683 8.95335 4.00574 8.80615L1.24664 6.30769C0.939709 6.02975 0.916013 5.55541 1.19372 5.24822C1.47142 4.94102 1.94536 4.91731 2.2523 5.19524L4.36085 7.10461L8.12299 1.33999C8.34934 0.993152 8.81376 0.895638 9.1603 1.12218Z" />
    </svg>
  )
}
```
