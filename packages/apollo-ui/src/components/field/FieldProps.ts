import { PropsWithChildren, ReactNode, Ref } from "react"
import { Field } from "@base-ui-components/react"

export type FieldProps = PropsWithChildren<{
  label?: ReactNode
  helperText?: ReactNode
  error?: boolean
  required?: boolean
  fullWidth?: boolean
  ref?: Ref<HTMLDivElement>
  labelRef?: Ref<HTMLLabelElement>
  labelDecorator?: ReactNode
  helperTextDecorator?: ReactNode
  hasCharacterCount?: boolean
  characterCount?: number
  maxLength?: number
}> &
  Field.Root.Props
