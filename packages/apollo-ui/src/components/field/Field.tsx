import { Field as BaseField } from "@base-ui-components/react/field"
import classNames from "classnames"

import { isTextElement } from "../../utils"
import { Typography } from "../typography"
import styles from "./field.module.css"
import { FieldProps } from "./FieldProps"

export function Field({
  label,
  helperText,
  children,
  className,
  fullWidth,
  error,
  required,
  ref,
  labelRef,
  labelDecorator,
  helperTextDecorator,
  hasCharacterCount,
  characterCount,
  maxLength,
  ...rootProps
}: FieldProps) {
  const isInvalid = error ?? rootProps?.invalid
  
  return (
    <BaseField.Root
      {...rootProps}
      ref={ref}
      invalid={isInvalid}
      className={classNames(
        "ApolloField-root",
        styles.field,
        styles.fieldRoot,
        { [styles.fieldFullWidth]: fullWidth },
        className
      )}
    >
      {label ? (
        <BaseField.Label
          ref={labelRef}
          className={classNames("ApolloField-label", styles.fieldLabel)}
        >
          {label}
          {required ? (
            <span className={styles.fieldLabelRequiredSymbol}>*</span>
          ) : null}
          {labelDecorator && (
            <span
              className={classNames(
                "ApolloField-labelDecorator",
                styles.fieldLabelDecorator
              )}
            >
              {labelDecorator}
            </span>
          )}
        </BaseField.Label>
      ) : null}
      {children}
      <div
        className={classNames(
          "ApolloField-description-root",
          styles.fieldHelperTextRoot,
        )}
        style={{ justifyContent: hasCharacterCount ? "flex-end" : "flex-start" }}
      >
        {isTextElement(helperText) ? (
          <div
            className={classNames(
              "ApolloField-helperText-container",
              styles.fieldHelperTextContainer,
              styles.fieldHelperText,
              {
                [styles.fieldHelperTextError]: isInvalid
              }
            )}
          >
            {helperTextDecorator && (
              <span
                className={classNames(
                  "ApolloField-helperTextDecorator",
                  styles.fieldHelperTextDecorator
                )}
              >
                {helperTextDecorator}
              </span>
            )}
            {isInvalid ? (
              <BaseField.Error
                match
                className={classNames(
                  "ApolloField-errorMessage",
                  styles.fieldHelperText,
                  {
                    [styles.fieldHelperTextError]: isInvalid
                  }
                )}
              >
                {helperText}
              </BaseField.Error>
            ) : (
              <BaseField.Description
                className={classNames(
                  "ApolloField-helperTextMessage",
                  styles.fieldHelperText
                )}
              >
                {helperText}
              </BaseField.Description>
            )}
          </div>
        ) : (
          helperText
        )}
        {hasCharacterCount && (
          <Typography
            level="labelMedium"
            className={styles.fieldHelperTextCount}
          >
            {characterCount} / {maxLength}
          </Typography>
        )}
      </div>
    </BaseField.Root>
  )
}
