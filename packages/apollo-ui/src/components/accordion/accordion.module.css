@layer legacy {
  .accordion {
    --apl-accordion-padding: 12px 16px;
    --apl-accordion-gap: 0.5rem;
    /* container */
    --apl-accordion-container-border-color: var(--apl-colors-border-default, #E5E7EB);
    --apl-accordion-container-border-radius: 4px;
    /* default */
    --apl-accordion-default-background: var(--apl-colors-surface-static-ui-default, #FFF);
    --apl-accordion-default-hover-background: var(--apl-colors-surface-hover, #F9FAFB);
    /* error */
    --apl-accordion-error-background: var(--apl-colors-surface-static-danger-default, #FFF2F2);
    --apl-accordion-error-hover-background: var(--apl-colors-surface-static-danger-default, #FFF2F2);
    /* icon */
    --apl-accordion-icon-color: var(--apl-colors-content-primary-default);
  }
}

@layer apollo {
  .accordion {
    --apl-accordion-padding: var(--apl-alias-spacing-padding-padding7, 12px) var(--apl-alias-spacing-padding-padding8, 16px);
    --apl-accordion-gap: var(--apl-alias-spacing-gap-gap5, 8px);
    --apl-accordion-color: var(--apl-alias-color-background-and-surface-on-surface);
    /* container */
    --apl-accordion-container-border-color: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
    --apl-accordion-container-border-radius: var(--apl-alias-radius-radius2, 4px);
    /* default */
    --apl-accordion-default-background: var(--apl-alias-color-background-and-surface-background, #FFF);
    --apl-accordion-default-hover-background: var(--apl-alias-color-background-and-surface-on-background, #F8F7F7);
    /* error */
    --apl-accordion-error-background: var(--apl-alias-color-error-error-container, #FFF4F3);
    --apl-accordion-error-hover-background: v var(--apl-alias-color-error-error-container, #FFF4F3);
    /* disabled */
    --apl-accordion-disabled-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
    --apl-accordion-disabled-color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    --apl-accordion-disabled-border-color: var(--apl-alias-color-outline-and-border-border-disabled, #ADABAB);
    /* icon */
    --apl-accordion-icon-color: var(--apl-alias-color-primary-primary, #016E2E);
  }
}

.container {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--apl-accordion-container-border-color);
  border-radius: var(--apl-accordion-container-border-radius);
  overflow: hidden;
  background: var(--apl-accordion-default-background);
  color: var(--apl-accordion-color);

  &[data-disabled] {
    cursor: auto;
    background-color: var(--apl-accordion-disabled-background);
    border-color: var(--apl-accordion-disabled-border-color);
  }
}

.containerFullWidth {
  width: 100%;
}

.borderless {
  border: none;
}

.error {
  background: var(--apl-accordion-error-background);

  & :global(.ApolloAccordion-trigger):hover:not([data-disabled]) {
    background: var(--apl-accordion-error-hover-background);
  }
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  padding: var(--apl-accordion-padding);
  margin: 0;
}

.trigger {
  display: flex;
  align-items: center;
  width: 100%;
  gap: var(--apl-accordion-gap);
  padding: var(--apl-accordion-padding);
  background: transparent;
  border: none;
  cursor: pointer;
  text-align: left;
  composes: apl-typography-body-large from '../../base.module.css';

  &:focus {
    outline: none;
  }

  &:hover:not([data-disabled]) {
    background-color: var(--apl-accordion-default-hover-background);
  }

  &[data-disabled] {
    cursor: auto;
    color: var(--apl-accordion-disabled-color);
  }
}


.triggerContent {
  flex: 1;
  font-weight: 500;
}

.icon {
  width: 16px;
  height: 16px;
  transition: transform 150ms ease-out;
}

.iconPrimary {
  color: var(--apl-accordion-icon-color)
}

.trigger[data-panel-open] .icon:first-child,
.trigger[data-panel-open] .icon:last-child {
  transform: rotate(180deg);
}

.panel {
  display: flex;
  height: var(--collapsible-panel-height);
  overflow: hidden;
  composes: apl-transition-all from '../../base.module.css';
  color: var(--apl-accordion-color);

  &[data-starting-style],
  &[data-ending-style] {
    height: 0;
  }

  &[data-disabled] {
    cursor: auto;
    color: var(--apl-accordion-disabled-color);
  }
}

.content {
  padding: var(--apl-accordion-padding);
  width: 100%;
}

.divider {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  border-width: 0;
  border-style: solid;
  border-color: var(--apl-alias-color-outline-and-border-outline-variant, #C8C6C6);
  border-bottom-width: 1px;
  margin: 0 var(--apl-alias-spacing-padding-padding8, 16px) var(--apl-alias-spacing-padding-padding7, 12px);
}