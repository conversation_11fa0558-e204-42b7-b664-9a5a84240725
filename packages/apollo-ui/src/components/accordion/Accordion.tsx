import { Collapsible } from "@base-ui-components/react/collapsible"
import classNames from "classnames"

import { ChevronIcon } from "../common"
import styles from "./accordion.module.css"
import type { AccordionProps } from "./AccordionProps"

export function Accordion({
  ref,
  open,
  label,
  children,
  fullWidth,
  keepMounted,
  iconVariant,
  onOpenChange,
  disabled = false,
  borderless = false,
  defaultOpen = false,
  variant = "default",
  iconPosition = "end",
  hasDivider = false,
  rootProps,
  triggerProps,
  panelProps,
  ...divProps
}: AccordionProps) {
  const showStartIcon = iconPosition === "start" || iconPosition === "both"
  const showEndIcon = iconPosition === "end" || iconPosition === "both"

  return (
    <Collapsible.Root
      {...divProps}
      {...rootProps}
      ref={ref}
      open={open}
      defaultOpen={defaultOpen}
      onOpenChange={onOpenChange}
      disabled={disabled}
      className={classNames(
        "ApolloAccordion-container",
        styles.accordion,
        styles.container,
        {
          [styles.containerFullWidth]: fullWidth,
          [styles.borderless]: borderless || hasDivider,
          [styles.error]: variant === "danger",
        },
        divProps?.className
      )}
    >
      <Collapsible.Trigger
        {...triggerProps}
        render={(props) => <div {...props}>{props.children}</div>}
        className={classNames("ApolloAccordion-trigger", styles.trigger)}
      >
        {showStartIcon && (
          <ChevronIcon
            className={classNames(styles.icon, {
              [styles.iconPrimary]: iconVariant === "primary",
            })}
          />
        )}
        <section
          className={classNames(
            "ApolloAccordion-trigger-content",
            styles.triggerContent
          )}
        >
          {label}
        </section>
        {showEndIcon && (
          <ChevronIcon
            className={classNames(styles.icon, {
              [styles.iconPrimary]: iconVariant === "primary",
            })}
          />
        )}
      </Collapsible.Trigger>

      <Collapsible.Panel
        {...panelProps}
        keepMounted={keepMounted}
        className={classNames("ApolloAccordion-panel", styles.panel)}
      >
        <div className={styles.content}>{children}</div>
      </Collapsible.Panel>
      {hasDivider ? (
        <div className={classNames("ApolloAccordion-divider", styles.divider)} />
      ): null}
    </Collapsible.Root>
  )
}
