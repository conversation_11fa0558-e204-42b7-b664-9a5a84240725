import type { <PERSON>psWithChildren, ReactNode, Ref } from "react"
import { Collapsible } from "@base-ui-components/react/collapsible"

export type AccordionProps = PropsWithChildren<{
  ref?: Ref<HTMLDivElement>
  label?: ReactNode
  iconPosition?: "start" | "end" | "both"
  borderless?: boolean
  variant?: "default" | "danger"
  iconVariant?: "default" | "primary"
  fullWidth?: boolean
  keepMounted?: boolean
  hasDivider?: boolean
  rootProps?: Collapsible.Root.Props
  triggerProps?: Collapsible.Trigger.Props
  panelProps?: Collapsible.Panel.Props
}> &
  Collapsible.Root.Props
