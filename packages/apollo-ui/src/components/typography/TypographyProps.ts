import type {
  HTMLAttributes,
  PropsWithChildren,
  Ref,
} from "react"

export type TypographyVariant =
  | "displayLarge"
  | "displayMedium"
  | "displaySmall"
  | "headlineLarge"
  | "headlineMedium"
  | "headlineSmall"
  | "titleLarge"
  | "titleMedium"
  | "titleSmall"
  | "bodyLarge"
  | "bodyLargeEmphasized"
  | "bodyMedium"
  | "bodyMediumEmphasized"
  | "bodySmall"
  | "bodySmallEmphasized"
  | "labelLarge"
  | "labelLargeEmphasized"
  | "labelMedium"
  | "labelMediumEmphasized"
  | "labelSmall"
  | "labelSmallEmphasized"

export type TypographyBaseProps = PropsWithChildren<{
  level?: TypographyVariant
  align?: "left" | "center" | "right" | "justify"
  color?: "default" | "negative" | "warning" | "success" | "process" | "primary"
  gutterBottom?: boolean
  noWrap?: boolean
  emphasize?: boolean
}>

export type HeadingProps = {
  ref?: Ref<HTMLHeadingElement>
} & HTMLAttributes<HTMLHeadingElement>

export type ParagraphProps = {
  ref?: Ref<HTMLParagraphElement>
} & HTMLAttributes<HTMLParagraphElement>

export type SpanProps = {
  ref?: Ref<HTMLSpanElement>
} & HTMLAttributes<HTMLSpanElement>

export type TypographyProps = (
  | ({
      level?:
        | "displayLarge"
        | "displayMedium"
        | "displaySmall"
        | "headlineLarge"
        | "headlineMedium"
        | "headlineSmall"
        | "titleLarge"
        | "titleMedium"
        | "titleSmall"
    } & HeadingProps)
  | ({
      level?:
        | "bodyLarge"
        | "bodyMedium"
        | "bodySmall"
        | "bodyLargeEmphasized"
        | "bodyMediumEmphasized"
        | "bodySmallEmphasized"
    } & ParagraphProps)
  | ({
      level?:
        | "labelLarge"
        | "labelMedium"
        | "labelSmall"
        | "labelLargeEmphasized"
        | "labelMediumEmphasized"
        | "labelSmallEmphasized"
    } & SpanProps)
) &
  TypographyBaseProps
