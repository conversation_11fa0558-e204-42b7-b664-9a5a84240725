import React from "react"
import classNames from "classnames"

import styles from "./typography.module.css"
import { TypographyProps, TypographyVariant } from "./TypographyProps"

const defaultLevelMapping: Record<TypographyVariant, string> = {
  displayLarge: "h1",
  displayMedium: "h1",
  displaySmall: "h1",
  headlineLarge: "h1",
  headlineMedium: "h2",
  headlineSmall: "h2",
  titleLarge: "h3",
  titleMedium: "h3",
  titleSmall: "h4",
  bodyLarge: "p",
  bodyLargeEmphasized: "p",
  bodyMedium: "p",
  bodyMediumEmphasized: "p",
  bodySmall: "p",
  bodySmallEmphasized: "p",
  labelLarge: "span",
  labelLargeEmphasized: "span",
  labelMedium: "span",
  labelMediumEmphasized: "span",
  labelSmall: "span",
  labelSmallEmphasized: "span",
}

const alignmentMap: Record<string, string> = {
  left: styles.alignLeft,
  center: styles.alignCenter,
  right: styles.alignRight,
  justify: styles.alignJustify,
}

const colorMap: Record<string, string> = {
  default: styles.colorDefault,
  negative: styles.colorNegative, //apollo (m3)
  warning: styles.colorWarning,
  success: styles.colorSuccess,
  process: styles.colorProcess,
  primary: styles.colorPrimary,
}

const levelMap: Record<TypographyVariant, string> = {
  displayLarge: styles.aplTypographyDisplayLarge,
  displayMedium: styles.aplTypographyDisplayMedium,
  displaySmall: styles.aplTypographyDisplaySmall,
  headlineLarge: styles.aplTypographyHeadlineLarge,
  headlineMedium: styles.aplTypographyHeadlineMedium,
  headlineSmall: styles.aplTypographyHeadlineSmall,
  titleLarge: styles.aplTypographyTitleLarge,
  titleMedium: styles.aplTypographyTitleMedium,
  titleSmall: styles.aplTypographyTitleSmall,
  bodyLarge: styles.aplTypographyBodyLarge,
  bodyLargeEmphasized: styles.aplTypographyBodyLargeEmphasized,
  bodyMedium: styles.aplTypographyBodyMedium,
  bodyMediumEmphasized: styles.aplTypographyBodyMediumEmphasized,
  bodySmall: styles.aplTypographyBodySmall,
  bodySmallEmphasized: styles.aplTypographyBodySmallEmphasized,
  labelLarge: styles.aplTypographyLabelLarge,
  labelLargeEmphasized: styles.aplTypographyLabelLargeEmphasized,
  labelMedium: styles.aplTypographyLabelMedium,
  labelMediumEmphasized: styles.aplTypographyLabelMediumEmphasized,
  labelSmall: styles.aplTypographyLabelSmall,
  labelSmallEmphasized: styles.aplTypographyLabelSmallEmphasized,
}

export function Typography({
  level = "bodyLarge",
  align = "left",
  color,
  gutterBottom,
  noWrap,
  className,
  children,
  ...rest
}: TypographyProps) {
  const Root = (defaultLevelMapping?.[level] ?? "span") as React.ElementType
  const alignmentClass = alignmentMap[align]
  const colorClass = color ? colorMap[color] : undefined
  const levelClass = levelMap[level]

  return (
    <Root
      className={classNames(
        "ApolloTypography--root",
        styles.typography,
        styles.root,
        colorClass,
        alignmentClass,
        levelClass,
        className,
        {
          [styles.gutterBottom]: gutterBottom,
          [styles.noWrap]: noWrap,
        }
      )}
      {...rest}
    >
      {children}
    </Root>
  )
}
