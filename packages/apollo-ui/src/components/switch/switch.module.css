@layer legacy {
    .switch {
        /* switchWrapper */
        --apl-switch-wrapper-gap: var(--apl-space-gap-xs, 8px);
        /* switchRoot */
        --apl-switch-padding: 1px;
        --apl-switch-width: 44px;
        --apl-switch-height: 22px;
        --apl-switch-border-radius: 23px;
        --apl-switch-disabled-opacity: 0.4;
        /* switchRoot:checked */
        --apl-switch-background: var(--apl-colors-surface-action-primary-default, #006D2E);
        --apl-switch-hover-background: var(--apl-colors-surface-action-primary-hover, #409261);
        --apl-switch-disabled-background: var(--apl-colors-surface-action-primary-hover, #409261);
        /* switchRoot:unchecked */
        --apl-switch-unchecked-background: var(--apl-colors-surface-static-ui-active, #E9EBF0);
        --apl-switch-unchecked-hover-background: var(--apl-colors-surface-static-ui-hover, #F6F7FB);
        --apl-switch-unchecked-disabled-background: var(--apl-colors-surface-static-ui-disabled, #F6F7FB);
        /* thumbRoot */
        --apl-switch-thumb-background-color: white;
        /* actionTextDisabled */
        --apl-action-text-disabled-color: var(--apl-colors-content-disabled, #BEC4D1);
    }
}

@layer apollo {
    .switch {
        /* switchWrapper */
        --apl-switch-wrapper-gap: var(--apl-alias-spacing-gap-gap5, 8px);
        /* switchRoot */
        --apl-switch-padding: 2px;
        --apl-switch-width: 40px;
        --apl-switch-height: 18px;
        --apl-switch-border-radius: 16px;
        --apl-switch-disabled-opacity: 1;
        /* switchRoot:checked */
        --apl-switch-background: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-switch-hover-background: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-switch-disabled-background: var(--apl-alias-color-background-and-surface-container-disabled, #F3F0F0);
        /* switchRoot:unchecked */
        --apl-switch-unchecked-background: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
        --apl-switch-unchecked-hover-background: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
        --apl-switch-unchecked-disabled-background: var(--apl-alias-color-background-and-surface-container-disabled, #F3F0F0);
        /* thumbRoot */
        --apl-switch-thumb-background-color: var(--apl-alias-color-primary-on-primary, #FFF);
        /* actionText */
        --apl-action-text-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        /* actionTextDisabled */
        --apl-action-text-disabled-color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }
}

.switchRoot {
    position: relative;
    display: flex;
    appearance: none;
    border: 0;
    margin: 0;
    padding: var(--apl-switch-padding);
    width: var(--apl-switch-width);
    height: var(--apl-switch-height);
    border-radius: var(--apl-switch-border-radius);
    background: var(--apl-switch-background);
    transition: background 0.2s ease;

    &:hover {
        background: var(--apl-switch-hover-background);

        &[data-unchecked] {
            background: var(--apl-switch-unchecked-hover-background);
        }
    }

    &:disabled {
        opacity: var(--apl-switch-disabled-opacity);
        background: var(--apl-switch-disabled-background);

        &[data-unchecked] {
            opacity: 1;
            background: var(--apl-switch-unchecked-disabled-background);
        }
    }

    &[data-unchecked] {
        background: var(--apl-switch-unchecked-background);
    }
}

.thumbRoot {
    aspect-ratio: 1 / 1;
    height: 100%;
    border-radius: 100%;
    background-color: var(--apl-switch-thumb-background-color);
    transition: translate 150ms ease;
    filter: drop-shadow(0px 2px 4px rgba(0, 35, 11, 0.20));

    &[data-checked] {
        translate: 22px 0;
    }
}

.switchWrapper {
    display: flex;
    flex-direction: row;
    gap: var(--apl-switch-wrapper-gap);
    justify-content: flex-start;
    align-items: center;
}


.actionText {
    composes: apl-typography-body-medium from '../../base.module.css';
    color: var(--apl-action-text-color);
}

.actionTextDisabled {
    color: var(--apl-action-text-disabled-color);
}