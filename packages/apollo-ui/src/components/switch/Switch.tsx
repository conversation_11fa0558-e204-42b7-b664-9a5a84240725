import { useCallback } from "react"
import { Switch as BaseSwitch } from "@base-ui-components/react/switch"
import classNames from "classnames"

import { Field } from "../field"
import styles from "./switch.module.css"
import type { SwitchProps } from "./SwitchProps"

export function Switch({
  label,
  labelDecorator,
  required,
  actionText,
  className,
  fieldProps,
  onCheckedChange,
  onChange,
  ...baseSwitchProps
}: SwitchProps) {
  const handleChange = useCallback(
    (checked: boolean, eventDetails: { event?: Event }) => {
      const event = eventDetails?.event
      if (event) {
        onChange?.(event)
      }
      onCheckedChange?.(
        checked,
        eventDetails as BaseSwitch.Root.ChangeEventDetails
      )
    },
    [onChange, onCheckedChange]
  )

  return (
    <Field
      {...fieldProps}
      className={classNames("ApolloSwitch-fieldRoot", fieldProps?.className)}
      label={label}
      labelDecorator={labelDecorator}
      required={required}
    >
      <label
        className={classNames(
          "ApolloSwitch-wrapperRoot",
          styles.switch,
          styles.switchWrapper
        )}
      >
        <BaseSwitch.Root
          {...baseSwitchProps}
          onCheckedChange={handleChange}
          className={classNames(
            "ApolloSwitch-switchRoot",
            styles.switchRoot,
            className
          )}
        >
          <BaseSwitch.Thumb
            className={classNames("ApolloSwitch-thumbRoot", styles.thumbRoot)}
          />
        </BaseSwitch.Root>
        {actionText && (
          <span
            className={classNames(
              "ApolloSwitch-actionText",
              styles.actionText,
              {
                [styles.actionTextDisabled]: baseSwitchProps?.disabled,
              }
            )}
          >
            {actionText}
          </span>
        )}
      </label>
    </Field>
  )
}
