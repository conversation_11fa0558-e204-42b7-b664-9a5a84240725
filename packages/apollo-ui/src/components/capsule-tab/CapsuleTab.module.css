@layer legacy {
    .capsuletab {
        /* container */
        --apl-capsuletab-border-color: var(--apl-colors-border-primary-default);
        --apl-capsuletab-border-radius: var(--apl-global-spacing-md);
        /* item */
        --apl-capsuletab-item-background-color: var(--apl-colors-surface-action-secondary);
        --apl-capsuletab-item-padding: var(--apl-global-spacing-xs);
        --apl-capsuletab-item-color: var(--apl-colors-content-primary-default);
        --apl-capsuletab-item-not-first-child-border-left-color: var(--apl-colors-border-primary-default);
        --apl-capsuletab-item-selected-background-color: var(--apl-colors-surface-static-success-default);
        /* itemText */
        --apl-capsuletab-item-text-font-size: var(--apl-font-font-size-head-line5);
        --apl-capsuletab-item-text-font-weight: var(--apl-font-font-weight-head-line5);
    }
}

@layer apollo {
    .capsuletab {
        /* container */
        --apl-capsuletab-border-color: var(--apl-alias-color-primary-primary);
        --apl-capsuletab-border-radius: var(--apl-alias-radius-radius4);
        /* item */
        --apl-capsuletab-item-background-color: var(--apl-alias-color-background-and-surface-surface);
        --apl-capsuletab-item-padding: var(--apl-alias-spacing-padding-padding5);
        --apl-capsuletab-item-color:  var(--apl-alias-color-primary-primary);
        --apl-capsuletab-item-not-first-child-border-left-color: var(--apl-alias-color-primary-primary);
        --apl-capsuletab-item-selected-background-color: var(--apl-alias-color-success-success-container);
        /* itemText */
        --apl-capsuletab-item-text-font-size: var(--apl-alias-typography-body-large-font-size);
        --apl-capsuletab-item-text-font-weight: var(--apl-alias-typography-body-large-font-weight);
    }
}

.root {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 45px;
}

.container {
    display: flex;
    flex: 1;
    max-width: 800px;
    justify-content: center;
    border: 2px solid var(--apl-capsuletab-border-color);
    border-radius: var(--apl-capsuletab-border-radius);
    overflow: hidden;
    transition: all 240ms cubic-bezier(0.075, 0.82, 0.165, 1);
}

.item {
    appearance: none;
    border: 0;
    background-color: var(--apl-capsuletab-item-background-color);
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: var(--apl-capsuletab-item-padding);
    color: var(--apl-capsuletab-item-color);
    cursor: pointer;
}

.item:not(:first-child) {
    border-left: 2px solid var(--apl-capsuletab-item-not-first-child-border-left-color);
}

.itemSelected {
    background-color: var( --apl-capsuletab-item-selected-background-color);
}

.itemText {
    font-size: var(--apl-capsuletab-item-text-font-size);
    font-weight: var(--apl-capsuletab-item-text-font-weight);
}