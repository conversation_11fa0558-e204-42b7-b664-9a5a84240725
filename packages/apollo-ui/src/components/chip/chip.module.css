@layer legacy {
    .chip {
        --apl-chip-padding: 2px var(--apl-global-spacing-xs);
        --apl-chip-gap: var(--apl-global-spacing-2xs);
        --apl-chip-icon-size: 12px;
        /* disabled */
        --apl-chip-disabled-border-color: var(--apl-colors-content-disabled);
        --apl-chip-disabled-background: var(--apl-colors-surface-static-ui-disabled);
        --apl-chip-disabled-color: var(--apl-colors-content-disabled, #BEC4D1);
        --apl-chip-outline-disabled-color: var(--apl-colors-content-disabled, #BEC4D1);

        .chipSmall {
            --apl-chip-padding: 2px var(--apl-global-spacing-xs);

            & p {
                font-size: 10px;
            }
        }

        .chipMedium {
            --apl-chip-padding: 2px var(--apl-global-spacing-xs);
        }

        .chipPrimary {
            --apl-chip-background: var(--apl-colors-surface-static-ui-primary);
            --apl-chip-border: 1px solid var(--apl-colors-border-focus);
            /* hover */
            --apl-chip-hover-color: inherit;
            --apl-chip-hover-background: var(--apl-colors-surface-static-ui-primary);
            --apl-chip-hover-boder-color: var(--apl-colors-surface-static-ui-primary);
            /* focus */
            --apl-chip-focus-color: inherit;
            --apl-chip-focus-background: var(--apl-colors-surface-static-ui-primary);
            --apl-chip-focus-boder-color: var(--apl-colors-surface-static-ui-primary);
        }

        .chipNegative {
            --apl-chip-background: var(--apl-colors-surface-action-delete-default);
            --apl-chip-border: 1px solid var(--apl-colors-surface-action-delete-default);
            /* hover */
            --apl-chip-hover-color: inherit;
            --apl-chip-hover-background: var(--apl-colors-surface-action-delete-default);
            --apl-chip-hover-boder-color: var(--apl-colors-surface-action-delete-default);
            --apl-chip-focus-color: inherit;
            --apl-chip-focus-background: var(--apl-colors-surface-action-delete-default);
            --apl-chip-focus-boder-color: var(--apl-colors-surface-action-delete-default);
        }

        .chipDisabled {
            border-color: var(--apl-chip-disabled-border-color) !important;
            background: var(--apl-chip-disabled-background) !important;

            & p {
                color: var(--apl-chip-disabled-color) !important;
            }

            & button {
                color: var(--apl-chip-disabled-color) !important;
            }
        }
    }
}

@layer apollo {
    .chip {
        --apl-chip-padding: 2px 8px;
        --apl-chip-gap: 8px;
        --apl-chip-icon-size: 14px;
        --apl-chip-outline-border: 1px solid var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-chip-outline-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

        /* hover */
        --apl-chip-hover-background: var(--apl-chip-hover);
        --apl-chip-hover-boder-color: var(--apl-chip-hover);
        --apl-chip-outline-hover-color: var(--apl-chip-hover);
        /* focus */
        --apl-chip-focus-background: var( --apl-chip-focus);
        --apl-chip-focus-boder-color: var( --apl-chip-focus);
        --apl-chip-outline-focus-color: var( --apl-chip-focus);

        /* disabled */
        --apl-chip-disabled-border-color: var(--apl-alias-color-background-and-surface-on-surface-variant);
        --apl-chip-disabled-background: var(--apl-alias-color-background-and-surface-on-surface-variant);
        --apl-chip-disabled-color: var(--apl-alias-color-background-and-surface-surface-variant,#FFF);
        --apl-chip-outline-disabled-color: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
    }

    .chipSmall {
        --apl-chip-padding: 0 8px;
        --apl-chip-min-height: 24px;
        --apl-chip-max-height: 24px;
    }

    .chipMedium {
        --apl-chip-padding: 2px 8px;
        --apl-chip-min-height: 28px;
        --apl-chip-max-height: 28px;
    }

    .chipLarge {
        --apl-chip-padding: 6px 8px;
        --apl-chip-min-height: 36px;
        --apl-chip-max-height: 36px;
    }

    .chipPrimary {
        --apl-chip-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-chip-border: 1px solid var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-chip-outline-border: 1px solid var(--apl-alias-color-primary-primary, #016E2E);
        --apl-chip-outline-color: var(--apl-alias-color-primary-primary, #016E2E);
        /* state */
        --apl-chip-hover-color: var(--apl-alias-color-primary-on-primary, #FFFFFF);
        --apl-chip-hover: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-chip-focus-color: var(--apl-alias-color-primary-on-primary, #FFFFFF);
        --apl-chip-focus: var(--apl-alias-color-primary-focused, #49A25C);

    }

    .chipNegative {
        --apl-chip-background: var(--apl-alias-color-error-error-container);
        --apl-chip-border: 1px solid var(--apl-alias-color-error-error-container);
        --apl-chip-outline-border: 1px solid var(--apl-alias-color-error-error, #C0000B);
        --apl-chip-outline-color: var(--apl-alias-color-error-error, #C0000B);
        /* state */
        --apl-chip-hover-color: var(--apl-alias-color-error-on-error, #FFFFFF);
        --apl-chip-hover: var(--apl-alias-color-error-hovered, #E9211E);
        --apl-chip-focus-color: var(--apl-alias-color-error-on-error, #FFFFFF);
        --apl-chip-focus: var(--apl-alias-color-error-focused, #FF5546);
    }

    .chipDisabled {
        border-color: var(--apl-chip-disabled-border-color) !important;
        background: var(--apl-chip-disabled-background) !important;

        & p {
            color: var(--apl-chip-disabled-color) !important;
        }

        & button {
            color: var(--apl-chip-disabled-color) !important;
        }
    }
}

.chipRoot {
    display: inline-flex;
    justify-content: center;
    padding: var(--apl-chip-padding);
    align-items: center;
    gap: var(--apl-chip-gap);
    width: fit-content;
    height: fit-content;
    max-width: 100%;
    min-height: var(--apl-chip-min-height, unset);
    max-height: var(--apl-chip-max-height, unset);

    border-radius: 4px;

    &:hover {
        border-color: var(--apl-chip-hover-boder-color);
    }

    &:focus,
    &:focus-visible {
        border-color: var(--apl-chip-focus-boder-color);
    }

    & p {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    & button {
        padding: 0;
        --apl-icon-button-size: var(--apl-chip-icon-size);
    }

}

.chipFilled {
    border: var(--apl-chip-border);
    background: var(--apl-chip-background);

    &:hover {
        & p {
            color: var(--apl-chip-hover-color);
        }

        & button {
            color: var(--apl-chip-hover-color);
        }

        background: var(--apl-chip-hover-background);
    }

    &:focus,
    &:focus-visible {
        & p {
            color: var(--apl-chip-focus-color);
        }

        & button {
            color: var(--apl-chip-focus-color);
        }

        background: var(--apl-chip-focus-background);
    }

    & button {

        &:enabled,
        &[href] {
            &:hover {
                color: var(--apl-chip-hover-color);
                background: transparent;
            }

            &:focus-visible {
                color: var(--apl-chip-focus-color);
                background: transparent;
            }
        }

    }
}

.chipOutline {
    --apl-chip-disabled-background: transparent;
    --apl-chip-disabled-color: var(--apl-chip-outline-disabled-color);

    border: var(--apl-chip-outline-border);
    background: transparent;

    & p {
        color: var(--apl-chip-outline-color);
    }

    & button {
        color: var(--apl-chip-outline-color);

        &:enabled,
        &[href] {
            &:hover {
                color: var(--apl-chip-outline-hover-color);
                background: transparent;
            }

            &:focus-visible {
                color: var(--apl-chip-outline-focus-color);
                border-color: var(--apl-chip-outline-focus-color);
                background: transparent;
            }
        }
    }

    &:hover {
        & p {
            color: var(--apl-chip-outline-hover-color);
        }

        & button {
            color: var(--apl-chip-outline-hover-color);
        }
    }

    &:focus,
    &:focus-visible {
        & p {
            color: var(--apl-chip-outline-focus-color);
        }

        & button {
            color: var(--apl-chip-outline-focus-color);
        }
    }


}