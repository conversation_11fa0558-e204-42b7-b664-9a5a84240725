import { H<PERSON><PERSON><PERSON>ributes, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ref } from "react"

export type ChipProps = {
  label: string
  disabled?: boolean
  variant?: "filled" | "outline"
  size?: "large" | "medium" | "small"
  color?: "primary" | "negative"
  ref?: Ref<HTMLDivElement>
  onClose?: <PERSON><PERSON>vent<PERSON>and<PERSON>
  onCheck?: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  onClick?: <PERSON><PERSON>ventHandler
} & HTMLAttributes<HTMLDivElement>
