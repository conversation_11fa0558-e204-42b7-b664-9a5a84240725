@layer legacy {
    .pagination {
        --apl-pagination-gap: var(--apl-space-gap-2xs);
        --apl-pagination-prev-next-button-color: var(--apl-colors-content-description);
        --apl-pagination-item-border-color: var(--apl-colors-border-default);
        --apl-pagination-item-color: var(--apl-colors-content-description);
        --apl-pagination-item-selected-color: var(--apl-colors-content-primary-default);
        --apl-pagination-item-selected-border-color: var(--apl-colors-border-primary-default);
        --apl-pagination-item-disabled-color: var(--apl-colors-content-disabled);
        --apl-pagination-item-disabled-border-color: var(--apl-colors-border-disabled);
    }
}

@layer apollo {
    .pagination {
        --apl-pagination-gap: 8px;
        --apl-pagination-prev-next-button-color: var(--apl-alias-color-primary-primary);
        --apl-pagination-item-border-color: var(--apl-alias-color-outline-and-border-outline-variant);
        --apl-pagination-item-color: var(--apl-alias-color-background-and-surface-on-surface);
        --apl-pagination-item-selected-color: var(--apl-alias-color-primary-primary);
        --apl-pagination-item-selected-border-color: var(--apl-alias-color-primary-primary);
        --apl-pagination-item-disabled-color: var(--apl-alias-color-outline-and-border-border-disabled);
        --apl-pagination-item-disabled-border-color: var(--apl-alias-color-background-and-surface-text-icon-disabled);
    }
}

.paginationRoot {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;
}

.paginationFull {
    display: flex;
    flex-direction: row;
    gap: var(--apl-pagination-gap);
}

.paginationCompact {
    display: none;
}

.paginationFirstPageButton,
.paginationLastPageButton {
    min-width: 32px;
    min-height: 32px;
}

.paginationPrevButton,
.paginationNextButton {
    min-width: 32px;
    min-height: 32px;
    color: var(--apl-pagination-prev-next-button-color);
}

.paginationItem {
    min-width: 42px;
    min-height: 32px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--apl-pagination-item-border-color);
    color: var(--apl-pagination-item-color);
    border-radius: 6px;
    padding: 4px 8px;
    cursor: pointer;
}

.paginationItemBorderless {
    border: none;
}

.paginationItemSelected {
    color: var(--apl-pagination-item-selected-color);
    border-color: var(--apl-pagination-item-selected-border-color);
}

.paginationItemDisabled {
    color: var(--apl-pagination-item-disabled-color);
    border-color: var(--apl-pagination-item-disabled-border-color);
}

.paginationPageInfo {
    color: var(--apl-pagination-item-color);
}

.paginationPageInfoDisabled {
    color: var(--apl-pagination-item-disabled-color);
}

.paginationEllipsis {
    pointer-events: none;
}

.paginationPageSize {
    max-width: 128px;
    max-height: 32px;
}

.paginationDisplayScreen {
    display: flex;
    align-items: center;
    gap: var(--apl-pagination-gap);
    flex-wrap: wrap;
}

.paginationNotDisplayScreen {
    display: none;
}

@media screen and (max-width: 768px) {
    .paginationFull:not([data-fixed-screen="true"]) {
        display: none;
    }

    .paginationCompact:not([data-fixed-screen="true"]) {
        display: flex;
        align-items: center;
        gap: var(--apl-pagination-gap);
        flex-wrap: wrap;
    }
}