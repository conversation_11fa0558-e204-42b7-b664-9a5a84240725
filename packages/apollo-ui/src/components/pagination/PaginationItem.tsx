import { forwardRef, type ButtonHTMLAttributes } from "react"
import classNames from "classnames"

import { But<PERSON> } from "../button"
import styles from "./pagination.module.css"

export type PaginationItemProps = Omit<
  ButtonHTMLAttributes<HTMLButtonElement>,
  "color"
> & {
  /**
   * If `true`, the item is selected.
   */
  selected?: boolean
  /**
   * If `true`, the item will not have a border.
   */
  borderless?: boolean
}

export const PaginationItem = forwardRef<
  HTMLButtonElement,
  PaginationItemProps
>(function PaginationItem(props, ref) {
  const {
    selected,
    disabled,
    borderless,
    children,
    className,
    ...buttonProps
  } = props

  return (
    <Button
      size="small"
      variant="outline"
      {...buttonProps}
      className={classNames(
        "ApolloPaginationItem-button",
        styles.paginationItem,
        {
          [styles.paginationItemSelected]: selected,
          [styles.paginationItemDisabled]: disabled,
          [styles.paginationItemBorderless]: borderless,
        },
        className
      )}
      disabled={disabled}
      ref={ref}
    >
      {children}
    </Button>
  )
})
