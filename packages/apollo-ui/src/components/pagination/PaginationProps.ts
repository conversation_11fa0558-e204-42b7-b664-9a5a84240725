import type { HTMLAttributes, ReactNode } from "react"
import { OptionProps } from "../select"

export type PaginationProps = Omit<
  HTMLAttributes<HTMLDivElement>,
  "onChange"
> & {
  /**
   * The number of minimum pagination item from start or end that will be show when the current page in the range
   * @default 6
   */
  minimumEdgeRange?: number
  /**
   * The total number of pages.
   * @default 1
   */
  count?: number

  /**
   * The current page (controlled).
   */
  page?: number

  /**
   * The default page (uncontrolled).
   * @default 1
   */
  defaultPage?: number

  /**
   * Callback fired when the page is changed.
   */
  onChange?: (event: React.MouseEvent<HTMLButtonElement>, page: number) => void

  /**
   * The number of minimum pages to show.
   * @default 10
   */
  minimumVisibleCount?: number
  /**
   * The number of sibling pages to show around the current page.
   * @default 1
   */
  siblingCount?: number

  /**
   * The number of boundary pages to show at the start and end.
   * @default 1
   */
  boundaryCount?: number

  /**
   * If `true`, the previous page button is displayed.
   * @default true
   */
  showPrevPageButton?: boolean

  /**
   * If `true`, the next page button is displayed.
   * @default true
   */
  showNextPageButton?: boolean

  /**
   * If `true`, disables the pagination component.
   * @default false
   */
  disabled?: boolean
  /**
   * If `true`, disables the prev page button.
   * @default false
   */
  disabledPrevPageButton?: boolean
  /**
   * If `true`, disables the next page button.
   * @default false
   */
  disabledNextPageButton?: boolean

  pageSize?: ReactNode
  pageSizeOptions?: number[] | OptionProps<number>[]
  onPageSizeChange?: (value: number) => void

  displayType?: "full" | "compact"

  showFirstPageButton?: boolean
  showLastPageButton?: boolean

  disabledFirstPageButton?: boolean
  disabledLastPageButton?: boolean
  /**
   * Custom CSS class for the root element.
   */
  className?: string
}
