import { Tabs as BaseTabs } from "@base-ui-components/react/tabs"
import classNames from "classnames"

import styles from "./tabs.module.css"
import type { TabsIndicatorProps } from "./TabsProps"

export function TabsIndicator({ className, ...props }: TabsIndicatorProps) {
  return (
    <BaseTabs.Indicator
      className={classNames(
        "ApolloTabs-indicator",
        styles.tabsIndicator,
        className
      )}
      {...props}
    />
  )
}
