import { Tabs as BaseTabs } from "@base-ui-components/react/tabs"
import classNames from "classnames"

import styles from "./tabs.module.css"
import type { TabsRootProps } from "./TabsProps"

export function TabsRoot({ className, ...props }: TabsRootProps) {
  return (
    <BaseTabs.Root
      className={classNames(
        "ApolloTabs-root",
        styles.tabs,
        className
      )}
      {...props}
    />
  )
}
