import { Tab } from "./Tab"
import { TabsIndicator } from "./TabsIndicator"
import { TabsList } from "./TabsList"
import { TabsPanel } from "./TabsPanel"
import { TabsRoot } from "./TabsRoot"

export const Tabs = {
  Root: TabsRoot,
  List: TabsList,
  Tab: Tab,
  Indicator: TabsIndicator,
  Panel: TabsPanel,
}

export { TabsRoot, TabsList, Tab, TabsIndicator, TabsPanel }
export type {
  TabsRootProps,
  TabsListProps,
  TabProps,
  TabsIndicatorProps,
  TabsPanelProps,
} from "./TabsProps"
