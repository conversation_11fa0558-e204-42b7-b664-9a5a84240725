import { Tabs as BaseTabs } from "@base-ui-components/react/tabs"
import classNames from "classnames"

import styles from "./tabs.module.css"
import type { TabProps } from "./TabsProps"

export function Tab({
  className,
  variant = "fit",
  align = "center",
  ...props
}: TabProps) {
  return (
    <BaseTabs.Tab
      className={classNames(
        "ApolloTabs-tab",
        styles.tabItem,
        {
          [styles.tabsFillContent]: variant === "fill",
          [styles.tabAlignLeft]: align === "left",
          [styles.tabAlignRight]: align === "right",
        },
        className
      )}
      {...props}
    />
  )
}
