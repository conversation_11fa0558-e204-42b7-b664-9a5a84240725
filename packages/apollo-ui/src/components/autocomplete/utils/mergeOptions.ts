/* eslint-disable @typescript-eslint/no-explicit-any */
import type { AutocompleteOption } from "../AutocompleteProps"

// Cache to store all previously seen options
const optionsCache = new Map<string, Map<any, AutocompleteOption<any>>>()

export function mergeOptions<T>(
  currentOptions: AutocompleteOption<T>[] | undefined,
  allOptions: AutocompleteOption<T>[] | undefined,
  selectedValues: T | T[] | null | undefined,
  cacheKey = "default"
): AutocompleteOption<T>[] {
  if (!selectedValues) {
    return currentOptions ?? []
  }

  // Initialize cache for this key if it doesn't exist
  if (!optionsCache.has(cacheKey)) {
    optionsCache.set(cacheKey, new Map())
  }
  const cache = optionsCache.get(cacheKey)!

  // Update cache with new options
  allOptions?.forEach((opt) => cache.set(opt.value, opt))
  currentOptions?.forEach((opt) => cache.set(opt.value, opt))

  const values = Array.isArray(selectedValues)
    ? selectedValues
    : [selectedValues]

  // Get selected options from cache or current options
  const selectedOptions = values
    .map((value) => {
      // Try to find in current options first
      const currentOption = currentOptions?.find((opt) => opt.value === value)
      if (currentOption) return currentOption

      // Then try allOptions
      const allOption = allOptions?.find((opt) => opt.value === value)
      if (allOption) return allOption

      // Finally try cache
      return cache.get(value)
    })
    .filter((opt): opt is AutocompleteOption<T> => opt !== undefined)

  const optionsMap = new Map<T, AutocompleteOption<T>>()

  // Add selected options first
  selectedOptions.forEach((opt) => optionsMap.set(opt.value, opt))

  // Add current options, potentially overwriting selected ones
  currentOptions?.forEach((opt) => optionsMap.set(opt.value, opt))

  return Array.from(optionsMap.values())
}

// Utility to clear cache if needed
export function clearOptionsCache(cacheKey?: string) {
  if (cacheKey) {
    optionsCache.delete(cacheKey)
  } else {
    optionsCache.clear()
  }
}
