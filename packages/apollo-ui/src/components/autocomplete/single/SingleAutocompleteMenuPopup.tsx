import { use<PERSON><PERSON>back, type ComponentProps, type ReactNode } from "react"
import { Menu } from "@base-ui-components/react/menu"
import classNames from "classnames"

import { ChevronIcon, CloseIcon } from "../../common"
import { IconButton } from "../../icon-button"
import { Input } from "../../input"
import { Typography } from "../../typography"
import styles from "../autocomplete.module.css"
import type { AutocompleteOption, AutocompleteSize } from "../AutocompleteProps"
import { AutocompleteMenuItem } from "../commons/AutocompleteMenuItem"
import { ScrollTriggerAutocomplete } from "../commons/ScrollTriggerAutocomplete"

type SingleAutocompleteMenuPopupProps<ValueType> = {
  options?: AutocompleteOption<ValueType>[]
  value?: ValueType | null
  placeholder?: string
  error?: boolean
  loading?: boolean
  search?: string
  disableSearch?: boolean
  size?: AutocompleteSize
  loadingMore?: boolean
  loadMoreLabel?: ReactNode
  hasMore?: boolean
  loadingComponent?: ReactNode
  noOptionsComponent?: ReactNode
  onClear?: (event: React.MouseEvent) => void
  onChange: (
    option: AutocompleteOption<ValueType>,
    event: React.MouseEvent<HTMLElement, MouseEvent>
  ) => void
  onSearch: (event: React.ChangeEvent<HTMLInputElement>) => void
  onKeyboardEvent: (
    props: ComponentProps<"div">
  ) => (event: React.KeyboardEvent<HTMLDivElement>) => void
  onLoadMore?: () => void
  menuProps?: React.HTMLAttributes<HTMLUListElement>
  menuLabelText?: string
}

export function SingleAutocompleteMenuPopup<ValueType>({
  options,
  value,
  placeholder,
  error,
  loading,
  search,
  disableSearch,
  loadingMore,
  loadMoreLabel,
  hasMore,
  loadingComponent,
  noOptionsComponent,
  size = "medium",
  onChange,
  onClear,
  onSearch,
  onKeyboardEvent,
  onLoadMore,
  menuProps,
  menuLabelText,
}: SingleAutocompleteMenuPopupProps<ValueType>) {
  const handleClearSearch = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      onSearch?.({
        target: { value: "" },
      } as React.ChangeEvent<HTMLInputElement>)
    },
    [onSearch]
  )

  return (
    <Menu.Popup
      className={classNames(
        "ApolloAutocomplete-menuPopup",
        styles.autocomplete,
        styles.autocompletePopoverRoot,
        menuProps?.className
      )}
      render={(props) => {
        return (
          <div
            {...props}
            className={classNames(props?.className)}
            onKeyDown={onKeyboardEvent(props)}
            onKeyUp={onKeyboardEvent(props)}
          >
            <Input
              autoFocus
              className={classNames(
                "ApolloAutocomplete-inputRoot",
                styles.autocompleteRoot,
                styles[size]
              )}
              readOnly={disableSearch}
              placeholder={placeholder}
              onChange={onSearch}
              value={
                disableSearch
                  ? (options?.find((o) => o.value === value)?.label ?? "")
                  : search
              }
              error={error}
              size={size}
              endDecorator={
                <>
                  {(search && search.length > 0) || disableSearch ? (
                    <IconButton
                      size="small"
                      onClick={disableSearch ? onClear : handleClearSearch}
                    >
                      <CloseIcon />
                    </IconButton>
                  ) : null}
                  <IconButton size="small">
                    <ChevronIcon className="ApolloAutocomplete-chevronIcon" />
                  </IconButton>
                </>
              }
            />
            {props.children}
          </div>
        )
      }}
    >
      <div
        className={classNames(
          "ApolloAutocomplete-menuRoot",
          styles.autocomplete,
          styles.autocompleteMenuRoot,
          menuProps?.className
        )}
        aria-label={menuProps?.["aria-label"]}
        aria-labelledby={menuProps?.["aria-labelledby"]}
        aria-describedby={menuProps?.["aria-describedby"]}
      >
        {loading ? (
          <div className={styles.autocompleteMenuItem}>
            {loadingComponent || (
              <Typography level="bodyMedium" className={styles.loadingText}>Loading...</Typography>
            )}
          </div>
        ) : options?.length ? (
          <>
            {menuLabelText ? (
              <Typography
                level="bodyMedium"
                className={styles.menuOptionMenuLabelText}
              >
                {menuLabelText}
              </Typography>
            ) : null}
            {options.map((option, index) => (
              <AutocompleteMenuItem
                key={index}
                onClick={(event) => {
                  if (option?.disabled) return
                  onChange(option, event)
                }}
                disabled={option?.disabled}
                selected={value === option.value}
                label={option.label}
                value={option.value}
                className={classNames(styles.menuOption, {
                  [styles.menuOptionIndented]: menuLabelText,
                })}
              />
            ))}
            {hasMore && (
              <ScrollTriggerAutocomplete
                onVisible={onLoadMore}
                loading={loadingMore}
                label={loadMoreLabel}
              />
            )}
          </>
        ) : (
          <div className={styles.autocompleteMenuItem}>
            {noOptionsComponent || (
              <Typography level="bodyLarge">Not found</Typography>
            )}
          </div>
        )}
      </div>
    </Menu.Popup>
  )
}
