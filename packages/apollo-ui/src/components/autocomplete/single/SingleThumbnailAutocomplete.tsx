import { Fragment } from "react"
import classNames from "classnames"

import { ChevronIcon, CloseIcon } from "../../common"
import { FieldProps } from "../../field"
import { IconButton } from "../../icon-button"
import { Input } from "../../input"
import styles from "../autocomplete.module.css"
import { AutocompleteOption, AutocompleteSize } from "../AutocompleteProps"
import { InputWrapper } from "../commons/InputWrapper"

type SingleThumbnailAutocompleteProps<T> = {
  ref?: React.Ref<HTMLDivElement>
  onClick?: (event: React.MouseEvent) => void
  selectedOption?: AutocompleteOption<T>
  onClear?: (event: React.MouseEvent) => void
  error?: boolean
  fullWidth?: boolean
  label?: string
  helperText?: string
  labelDecorator?: React.ReactNode
  helperTextDecorator?: React.ReactNode
  placeholder?: string
  size?: AutocompleteSize
  required?: boolean
  disabled?: boolean
  open?: boolean
  labelRef?: FieldProps["labelRef"]
  // New props
  className?: string
  style?: React.CSSProperties
  id?: string
  name?: string
  autoFocus?: boolean
  tabIndex?: number
  "aria-label"?: string
  "aria-labelledby"?: string
  "aria-describedby"?: string
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>
}

export function SingleThumbnailAutocomplete<T>({
  ref,
  onClick,
  selectedOption,
  onClear,
  error,
  placeholder,
  label,
  helperText,
  labelDecorator,
  helperTextDecorator,
  required,
  disabled,
  fullWidth,
  labelRef,
  open,
  size = "medium",
  // New props
  className,
  style,
  id,
  name,
  autoFocus,
  tabIndex,
  "aria-label": ariaLabel,
  "aria-labelledby": ariaLabelledby,
  "aria-describedby": ariaDescribedby,
  inputProps,
}: SingleThumbnailAutocompleteProps<T>) {
  // Extract inputProps to avoid duplication and handle them safely
  const mergedInputProps = inputProps || {}
  const finalPlaceholder = mergedInputProps.placeholder || placeholder

  return (
    <Input
      readOnly
      id={id}
      name={name}
      autoFocus={autoFocus || mergedInputProps.autoFocus}
      tabIndex={tabIndex ?? mergedInputProps.tabIndex}
      aria-label={ariaLabel || mergedInputProps["aria-label"]}
      aria-labelledby={ariaLabelledby || mergedInputProps["aria-labelledby"]}
      aria-describedby={ariaDescribedby || mergedInputProps["aria-describedby"]}
      style={style || mergedInputProps.style}
      // Apply additional input attributes
      maxLength={mergedInputProps.maxLength}
      min={mergedInputProps.min}
      max={mergedInputProps.max}
      pattern={mergedInputProps.pattern}
      fieldProps={{
        labelRef,
        ref,
        className: classNames(
          "ApolloAutocomplete-trigger",
          styles.autocompleteTriggerWrapper,
          { open: open, [styles.autocompleteFullWidth]: fullWidth }
        ),
        onClick,
      }}
      required={required}
      disabled={disabled}
      label={label}
      helperText={helperText}
      labelDecorator={labelDecorator}
      helperTextDecorator={helperTextDecorator}
      className={classNames(
        "ApolloAutocomplete-root",
        "thumbnail",
        styles.autocomplete,
        styles.autocompleteRoot,
        styles.autocompleteRootDefault,
        styles[size],
        className
      )}
      placeholder={finalPlaceholder}
      error={error}
      size={size}
      value={selectedOption?.label ?? ""}
      endDecorator={
        <Fragment>
          {selectedOption && (
            <IconButton
              className="ApolloAutocomplete-clearButton"
              size="small"
              disabled={disabled}
              onClick={onClear}
            >
              <CloseIcon />
            </IconButton>
          )}
          <IconButton className="ApolloAutocomplete-toggleButton" size="small" disabled={disabled}>
            <ChevronIcon className="ApolloAutocomplete-chevronIcon" />
          </IconButton>
        </Fragment>
      }
      inputWrapper={InputWrapper}
    />
  )
}
