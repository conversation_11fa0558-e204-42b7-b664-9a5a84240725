import { useEffect, useRef, type ReactNode } from "react"
import classNames from "classnames"

import { Typography } from "../../typography"
import styles from "../autocomplete.module.css"

type ScrollTriggerAutocompleteProps = {
  onVisible?: () => void
  loading?: boolean
  label?: ReactNode
}

export function ScrollTriggerAutocomplete({
  onVisible,
  loading = false,
  label = "Loading more",
}: ScrollTriggerAutocompleteProps) {
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !loading) {
          onVisible?.()
        }
      },
      {
        rootMargin: "0px",
        threshold: 0.1,
      }
    )

    const currentElement = elementRef.current
    if (currentElement) {
      observer.observe(currentElement)
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement)
      }
    }
  }, [onVisible, loading])

  if (!onVisible) return null

  return (
    <div
      ref={elementRef}
      className={classNames(
        "ApolloAutocomplete-scrollTrigger",
        styles.autocomplete,
        styles.autocompleteMenuItem,
      )}
    >
      {typeof label === "string" ? (
        <div className={styles.autocompleteScrollTrigger}>
          <Loading3Quarters className="animate-spin" size={16} />
          <Typography level="bodyMedium" className={styles.loadingText}>{label}</Typography>
        </div>
      ) : (
        label
      )}
    </div>
  )
}

function Loading3Quarters({
  className,
  size = 16,
}: {
  className?: string
  size?: number
}) {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
    >
      <g clipPath="url(#clip0_12370_3100)">
        <path
          d="M12.001 22C10.6512 22 9.34056 21.7363 8.10802 21.2148C6.9165 20.7109 5.84803 19.9883 4.92997 19.0703C4.01192 18.1523 3.28919 17.084 2.78523 15.8926C2.2637 14.6602 2 13.3496 2 12C2 11.6113 2.31448 11.2969 2.70319 11.2969C3.0919 11.2969 3.40639 11.6113 3.40639 12C3.40639 13.1602 3.63297 14.2852 4.08223 15.3457C4.51587 16.3691 5.13507 17.2891 5.92421 18.0781C6.71335 18.8672 7.63336 19.4883 8.6569 19.9199C9.7156 20.3672 10.8407 20.5938 12.001 20.5938C13.1612 20.5938 14.2864 20.3672 15.347 19.918C16.3705 19.4844 17.2906 18.8652 18.0797 18.0762C18.8688 17.2871 19.49 16.3672 19.9217 15.3438C20.369 14.2852 20.5956 13.1602 20.5956 12C20.5956 10.8398 20.369 9.71484 19.9197 8.6543C19.4876 7.63332 18.8621 6.70549 18.0777 5.92188C17.2949 5.13656 16.3668 4.51101 15.3451 4.08008C14.2864 3.63281 13.1612 3.40625 12.001 3.40625C11.6123 3.40625 11.2978 3.0918 11.2978 2.70312C11.2978 2.31445 11.6123 2 12.001 2C13.3507 2 14.6614 2.26367 15.8939 2.78516C17.0855 3.28906 18.1539 4.01172 19.072 4.92969C19.99 5.84766 20.7108 6.91797 21.2148 8.10742C21.7363 9.33984 22 10.6504 22 12C22 13.3496 21.7363 14.6602 21.2148 15.8926C20.7128 17.084 19.99 18.1523 19.072 19.0703C18.1539 19.9883 17.0835 20.709 15.8939 21.2129C14.6614 21.7363 13.3507 22 12.001 22Z"
          fill="#ADABAB"
        />
      </g>
      <defs>
        <clipPath id="clip0_12370_3100">
          <rect
            width="24"
            height="24"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
