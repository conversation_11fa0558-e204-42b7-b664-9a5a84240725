import type { PropsWithChildren } from "react"
import classNames from "classnames"

import styles from "../autocomplete.module.css"

export function InputWrapper(props: PropsWithChildren) {
  return (
    <div
      className={classNames(
        "ApolloAutocomplete-inputWrapper",
        styles.autocomplete,
        styles.autocompleteInputWrapper,
        styles.autocompleteFullTags,
      )}
    >
      {props?.children}
    </div>
  )
}
