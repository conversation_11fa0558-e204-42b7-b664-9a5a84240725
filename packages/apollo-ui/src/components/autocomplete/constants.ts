export const AUTOCOMPLETE_CONSTANTS = {
  CHIP: {
    MIN_WIDTH_PERCENTAGE: 100,
    SPACING: 8,
    CHARACTER_WIDTH: 8,
    PADDING: 32,
    MAX_FIRST_CHIP_PERCENTAGE: 70,
  },
  COUNTER: {
    WIDTH: 40,
  },
  INPUT: {
    HEIGHT: 15,
    HEIGHT_WITH_LABEL: 42,
    HELPER_TEXT_HEIGHT: 20,
    BASE_POPOVER_OFFSET: {
      medium: -27,
      small: -17,
    },
  },
  KEYBOARD: {
    ALLOWED_KEYS: [
      "Escape",
      "ArrowUp",
      "ArrowDown",
      "ArrowR<PERSON>",
      "ArrowLef<PERSON>",
      "Enter",
    ] as const,
  },
  DEFAULTS: {
    DEBOUNCE_MS: 300,
    SELECT_ALL_TEXT: "Select All",
    MIN_SEARCH_LENGTH: 2,
  },
  SIZE: {
    SMALL: {
      HEIGHT: 32,
      HEIGHT_WITH_LABEL: 32,
      CHIP_HEIGHT: 24,
    },
    MEDIUM: {
      HEIGHT: 42,
      HEIGHT_WITH_LABEL: 42,
      CHIP_HEIGHT: 32,
    },
  },
} as const

export type AutocompleteConstants = typeof AUTOCOMPLETE_CONSTANTS
