/* eslint-disable react-refresh/only-export-components */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createContext,
  useCallback,
  useContext,
  useState,
  type ReactNode,
} from "react"

import type {
  AutocompleteOption,
  AutocompleteProps,
  AutocompleteSize,
} from "../AutocompleteProps"
import { AUTOCOMPLETE_CONSTANTS } from "../constants"
import { useDebounce } from "../hooks/useDebounce"
import { useFilterLogic } from "../hooks/useFilterLogic"

type AutocompleteContextType<T = any> = {
  // Base props
  options?: AutocompleteOption<T>[]
  size?: AutocompleteSize
  fullWidth?: boolean
  label?: string
  helperText?: string
  labelDecorator?: ReactNode
  helperTextDecorator?: ReactNode
  loading?: boolean
  disableSearch?: boolean
  onSearch?: (value: string) => void
  debounceMs?: number
  filterLogic?: (
    options: AutocompleteOption<T>[],
    search: string
  ) => AutocompleteOption<T>[]

  // Styling
  className?: string
  style?: React.CSSProperties

  // Form State
  disabled?: boolean
  error?: boolean
  required?: boolean
  placeholder?: string
  onFocus?: (event?: Event) => void
  onBlur?: (event?: Event) => void

  // Accessibility
  id?: string
  name?: string
  autoFocus?: boolean
  tabIndex?: number
  "aria-label"?: string
  "aria-labelledby"?: string
  "aria-describedby"?: string

  // Advanced customization
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>
  menuProps?: React.HTMLAttributes<HTMLUListElement>

  // State
  search: string
  setSearch: (search: string) => void
  handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  filteredOptions: AutocompleteOption<T>[]

  // Shared handlers
  handleSearch: (event: React.ChangeEvent<HTMLInputElement>) => void
  handleOpenChange: (open: boolean) => void
  clearSearch: () => void

  onLoadMore?: () => Promise<void> | void
  loadingMore?: boolean
  loadMoreLabel?: ReactNode
  hasMore?: boolean

  loadingComponent?: ReactNode
  noOptionsComponent?: ReactNode
}

const AutocompleteContext = createContext<AutocompleteContextType | undefined>(
  undefined
)

type AutocompleteProviderProps<T> = Omit<
  AutocompleteProps<T>,
  "multiple" | "value" | "onChange"
> & {
  children: React.ReactNode
  search?: string
}

export function AutocompleteProvider<T>({
  children,
  options,
  size = "medium",
  fullWidth,
  label,
  helperText,
  labelDecorator,
  helperTextDecorator,
  loading,
  disableSearch,
  onSearch,
  search: controlledSearch,
  debounceMs = AUTOCOMPLETE_CONSTANTS.DEFAULTS.DEBOUNCE_MS,
  filterLogic,
  onLoadMore,
  loadingMore,
  loadMoreLabel,
  hasMore,
  loadingComponent,
  noOptionsComponent,
  error,
  required,
  placeholder,
  onFocus,
  onBlur,
  disabled,
  // New props
  className,
  style,
  id,
  name,
  autoFocus,
  tabIndex,
  "aria-label": ariaLabel,
  "aria-labelledby": ariaLabelledby,
  "aria-describedby": ariaDescribedby,
  inputProps,
  menuProps,
}: AutocompleteProviderProps<T>) {
  const [internalSearch, setInternalSearch] = useState("")
  const search = controlledSearch ?? internalSearch
  const filteredOptions = useFilterLogic(options, search, filterLogic)

  const debouncedSearch = useDebounce((value: string) => {
    onSearch?.(value)
  }, debounceMs)

  const setSearch = useCallback(
    (value: string) => {
      if (controlledSearch !== undefined) {
        onSearch?.(value)
      } else {
        setInternalSearch(value)
      }
    },
    [controlledSearch, onSearch]
  )

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value
      setSearch(value)
      debouncedSearch(value)
    },
    [setSearch, debouncedSearch]
  )

  const handleSearch = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value
      setSearch(value)
      debouncedSearch(value)
    },
    [debouncedSearch, setSearch]
  )

  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (!open) {
        setSearch("")
      }
    },
    [setSearch]
  )

  const clearSearch = useCallback(() => {
    setSearch("")
  }, [setSearch])

  const value = {
    // Props
    options,
    size,
    fullWidth,
    label,
    helperText,
    labelDecorator,
    helperTextDecorator,
    loading,
    onSearch,
    debounceMs,
    filterLogic,
    disableSearch,

    // Styling
    className,
    style,

    // State
    search,
    setSearch,
    handleSearchChange,
    filteredOptions,

    // Handlers
    handleSearch,
    handleOpenChange,
    clearSearch,

    onLoadMore,
    loadingMore,
    loadMoreLabel,
    hasMore,

    loadingComponent,
    noOptionsComponent,

    // Form State
    error,
    required,
    placeholder,
    onFocus,
    onBlur,
    disabled,

    // Accessibility
    id,
    name,
    autoFocus,
    tabIndex,
    "aria-label": ariaLabel,
    "aria-labelledby": ariaLabelledby,
    "aria-describedby": ariaDescribedby,

    // Advanced customization
    inputProps,
    menuProps,
  }

  return (
    <AutocompleteContext.Provider value={value as AutocompleteContextType<T>}>
      {children}
    </AutocompleteContext.Provider>
  )
}

export function useAutocomplete<T>() {
  const context = useContext(AutocompleteContext)
  if (!context) {
    throw new Error("useAutocomplete must be used within AutocompleteProvider")
  }
  return context as AutocompleteContextType<T>
}
