import {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  type RefObject,
} from "react"
import classNames from "classnames"

import { Chip } from "../../chip"
import { ChevronIcon, CloseIcon } from "../../common"
import type { FieldProps } from "../../field"
import { IconButton } from "../../icon-button"
import { Input } from "../../input"
import { Typography } from "../../typography"
import styles from "../autocomplete.module.css"
import type { AutocompleteOption, AutocompleteSize } from "../AutocompleteProps"
import { InputWrapper } from "../commons/InputWrapper"
import { AUTOCOMPLETE_CONSTANTS } from "../constants"

type ThumbnailAutocompleteProps<ValueType> = {
  ref?: React.Ref<HTMLDivElement>
  value?: ValueType[] | null
  options?: AutocompleteOption<ValueType>[]
  fullWidth?: boolean
  label?: string
  helperText?: string
  labelDecorator?: React.ReactNode
  helperTextDecorator?: React.ReactNode
  error?: boolean
  required?: boolean
  disabled?: boolean
  open?: boolean
  placeholder?: string
  size?: AutocompleteSize
  labelRef?: FieldProps["labelRef"]
  onClick?: (event: React.MouseEvent) => void
  onClose?: (event: React.MouseEvent) => void
  onClearAll?: (event: React.MouseEvent) => void
  onChipClose?: (
    option: AutocompleteOption<ValueType>
  ) => (event: React.MouseEvent) => void
  // New props
  className?: string
  style?: React.CSSProperties
  id?: string
  name?: string
  autoFocus?: boolean
  tabIndex?: number
  "aria-label"?: string
  "aria-labelledby"?: string
  "aria-describedby"?: string
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>
}

export function ThumbnailAutocomplete<ValueType>({
  ref,
  fullWidth,
  label,
  helperText,
  labelDecorator,
  helperTextDecorator,
  error,
  placeholder,
  required,
  disabled,
  labelRef,
  open,
  value = [],
  options = [],
  size = "medium",
  onClick,
  onChipClose,
  onClearAll,
  // New props
  className,
  style,
  id,
  name,
  autoFocus,
  tabIndex,
  "aria-label": ariaLabel,
  "aria-labelledby": ariaLabelledby,
  "aria-describedby": ariaDescribedby,
  inputProps,
}: ThumbnailAutocompleteProps<ValueType>) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [visibleChips, setVisibleChips] = useState<
    AutocompleteOption<ValueType>[]
  >([])

  const [hiddenCount, setHiddenCount] = useState(0)

  const isClearable = useMemo(
    () => value && value?.length > 0 && !disabled,
    [disabled, value]
  )

  const selectedOptions = useMemo(
    () => options.filter((option) => value?.includes(option.value)),
    [options, value]
  )

  const calculateVisibleChips = useCallback(() => {
    if (!containerRef.current || selectedOptions.length === 0) {
      return
    }

    const container = containerRef.current
    const containerWidth = container.offsetWidth
    const {
      COUNTER: { WIDTH: COUNTER_WIDTH },
      CHIP: { MIN_WIDTH_PERCENTAGE, SPACING, CHARACTER_WIDTH, PADDING },
    } = AUTOCOMPLETE_CONSTANTS

    const firstChipFullWidth =
      selectedOptions[0].label.length * CHARACTER_WIDTH + PADDING
    const minFirstChipWidth = containerWidth * (MIN_WIDTH_PERCENTAGE / 100)

    // Try to show first chip in full width if possible
    const visible: AutocompleteOption<ValueType>[] = [selectedOptions[0]]
    const firstChipWidth =
      firstChipFullWidth <= containerWidth * 0.7
        ? firstChipFullWidth // Use full width if it's reasonable
        : minFirstChipWidth // Fallback to minimum width if too long

    let remainingWidth = containerWidth - firstChipWidth - SPACING
    let hidden = selectedOptions.length - 1

    // Rest of chip calculations
    for (let i = 1; i < selectedOptions.length; i++) {
      const chipWidth =
        selectedOptions[i].label.length * CHARACTER_WIDTH + PADDING
      if (remainingWidth - chipWidth - SPACING - COUNTER_WIDTH > 0) {
        visible.push(selectedOptions[i])
        remainingWidth -= chipWidth + SPACING
        hidden--
      } else {
        break
      }
    }

    setVisibleChips(visible)
    setHiddenCount(hidden)
  }, [selectedOptions])

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      calculateVisibleChips()
    })

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [calculateVisibleChips])

  useEffect(() => {
    calculateVisibleChips()
  }, [calculateVisibleChips])

  // Merge inputProps safely
  const mergedInputProps = inputProps || {}
  const finalPlaceholder = mergedInputProps.placeholder || placeholder

  return (
    <Input
      readOnly
      id={id}
      name={name}
      autoFocus={autoFocus || mergedInputProps.autoFocus}
      tabIndex={tabIndex ?? mergedInputProps.tabIndex}
      aria-label={ariaLabel || mergedInputProps["aria-label"]}
      aria-labelledby={ariaLabelledby || mergedInputProps["aria-labelledby"]}
      aria-describedby={ariaDescribedby || mergedInputProps["aria-describedby"]}
      style={style || mergedInputProps.style}
      // Apply additional input attributes
      maxLength={mergedInputProps.maxLength}
      min={mergedInputProps.min}
      max={mergedInputProps.max}
      pattern={mergedInputProps.pattern}
      fieldProps={{
        labelRef,
        ref: (node) => {
          containerRef.current = node
          if (typeof ref === "function") {
            ref(node)
          } else if (ref) {
            ;(ref as RefObject<HTMLDivElement | null>).current = node
          }
        },
        className: classNames(
          "ApolloAutocomplete-trigger",
          styles.autocompleteTriggerWrapper,
          {
            [styles.autocompleteFullWidth]: fullWidth,
            open: open,
          }
        ),
        onClick: disabled ? undefined : onClick,
      }}
      label={label}
      helperText={helperText}
      labelDecorator={labelDecorator}
      helperTextDecorator={helperTextDecorator}
      error={error}
      size={size}
      required={required}
      disabled={disabled}
      className={classNames(
        "ApolloAutocomplete-root",
        styles.autocomplete,
        styles.autocompleteRoot,
        "thumbnail",
        "multiple",
        styles[size],
        className
      )}
      rootRef={containerRef}
      placeholder={finalPlaceholder}
      startInputAddOn={
        (selectedOptions && selectedOptions?.length) > 0 && (
          <div
            className={classNames(
              "ApolloAutocomplete-chipContainer",
              styles.chipContainer
            )}
            style={
              {
                "--apl-autocomplete-chip-min-width": `${AUTOCOMPLETE_CONSTANTS.CHIP.MIN_WIDTH_PERCENTAGE}%`,
              } as React.CSSProperties
            }
          >
            {visibleChips.map((option, index) => (
              <Chip
                size={size}
                disabled={disabled}
                key={option.label}
                label={option.label}
                onClose={onChipClose?.(option)}
                className={index === 0 ? styles.truncatedChip : undefined}
              />
            ))}
            {hiddenCount > 0 && (
              <Typography
                color={disabled ? undefined : "primary"}
                level="bodyLarge"
              >
                +{hiddenCount}
              </Typography>
            )}
          </div>
        )
      }
      endDecorator={
        <Fragment>
          {isClearable ? (
            <IconButton
              className="ApolloAutocomplete-clearButton"
              size="small"
              onClick={onClearAll}
            >
              <CloseIcon />
            </IconButton>
          ) : null}
          <IconButton
            disabled={disabled}
            className="ApolloAutocomplete-toggleButton"
            size="small"
          >
            <ChevronIcon className="ApolloAutocomplete-chevronIcon" />
          </IconButton>
        </Fragment>
      }
      inputWrapper={InputWrapper}
    />
  )
}
