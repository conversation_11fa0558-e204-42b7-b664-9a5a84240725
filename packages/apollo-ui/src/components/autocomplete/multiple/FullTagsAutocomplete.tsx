import { Fragment, useMemo } from "react"
import classNames from "classnames"

import { Chip } from "../../chip"
import { ChevronIcon, CloseIcon } from "../../common"
import { IconButton } from "../../icon-button"
import { Input } from "../../input"
import styles from "../autocomplete.module.css"
import type { AutocompleteOption, AutocompleteSize } from "../AutocompleteProps"
import { InputWrapper } from "../commons/InputWrapper"
import { mergeOptions } from "../utils/mergeOptions"

type FullTagsAutocompleteProps<ValueType> = {
  value?: ValueType[]
  options?: AutocompleteOption<ValueType>[]
  search?: string
  disableSearch?: boolean
  size?: AutocompleteSize
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
  onClose?: (event: React.MouseEvent<HTMLElement>) => void
  onClearAll?: (event: React.MouseEvent<HTMLElement>) => void
  onChipClose?: (
    option: AutocompleteOption<ValueType>
  ) => (event: React.MouseEvent<HTMLElement>) => void
}

export function FullTagsAutocomplete<ValueType>({
  disableSearch,
  value = [],
  options = [],
  search = "",
  size = "medium",
  onChange,
  onClearAll,
  onChipClose,
}: FullTagsAutocompleteProps<ValueType>) {
  const selectedOptions = useMemo(() => {
    const selected = options.filter((option) => value.includes(option.value))
    return mergeOptions(selected, options, value)
  }, [options, value])

  const isClearable = useMemo(() => value && value?.length > 0, [value])

  return (
    <Input
      size={size}
      className={classNames(
        "ApolloAutocomplete-inputRoot",
        styles.autocomplete,
        styles.autocompleteRoot,
        styles[size]
      )}
      autoFocus
      placeholder="Search"
      onChange={disableSearch ? undefined : onChange}
      value={disableSearch ? "" : search}
      readOnly={disableSearch}
      startInputAddOn={
        <>
          {selectedOptions.map((option) => (
            <Chip
              className="ApolloAutocomplete-chip"
              size={size}
              key={option.label}
              label={option.label}
              onClose={onChipClose?.(option)}
            />
          ))}
         </>
      }
      endDecorator={
        <Fragment>
          {isClearable ? (
            <IconButton
              className="ApolloAutocomplete-clearButton"
              size="small"
              onClick={onClearAll}
            >
              <CloseIcon />
            </IconButton>
          ) : null}
          <IconButton className="ApolloAutocomplete-toggleButton" size="small">
            <ChevronIcon className="ApolloAutocomplete-chevronIcon" />
          </IconButton>
        </Fragment>
      }
      inputWrapper={InputWrapper}
    />
  )
}
