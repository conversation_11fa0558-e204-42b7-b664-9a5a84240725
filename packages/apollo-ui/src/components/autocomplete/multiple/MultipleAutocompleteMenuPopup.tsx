import type { ComponentProps } from "react"
import { Menu } from "@base-ui-components/react/menu"
import classNames from "classnames"

import { Typography } from "../../typography"
import styles from "../autocomplete.module.css"
import type { AutocompleteOption } from "../AutocompleteProps"
import { AutocompleteMenuItem } from "../commons/AutocompleteMenuItem"
import { ScrollTriggerAutocomplete } from "../commons/ScrollTriggerAutocomplete"
import { useAutocomplete } from "../context/AutocompleteContext"
import { FullTagsAutocomplete } from "./FullTagsAutocomplete"

type MultipleAutocompleteMenuPopupProps<ValueType> = {
  value: ValueType[]
  options?: AutocompleteOption<ValueType>[]
  showSelectAll?: boolean
  isAllSelected?: boolean
  selectAllText?: string
  hideCheckbox?: boolean
  menuLabelText?: string
  onChange: (
    option: AutocompleteOption<ValueType>,
    event: React.MouseEvent<HTMLElement, MouseEvent>
  ) => void
  onSearch: (event: React.ChangeEvent<HTMLInputElement>) => void
  onChipClose: (
    option: AutocompleteOption<ValueType>
  ) => (event: React.MouseEvent) => void
  onKeyboardEvent: (
    props: ComponentProps<"div">
  ) => (event: React.KeyboardEvent<HTMLDivElement>) => void
  onClearAll: (event: React.MouseEvent) => void
  onSelectAll: (event: React.MouseEvent<HTMLElement, MouseEvent>) => void
}

export function MultipleAutocompleteMenuPopup<ValueType>({
  value,
  showSelectAll,
  isAllSelected,
  options,
  selectAllText = "Select All",
  onChange,
  onSearch,
  onChipClose,
  onKeyboardEvent,
  onClearAll,
  onSelectAll,
  hideCheckbox,
  menuLabelText,
}: MultipleAutocompleteMenuPopupProps<ValueType>) {
  const {
    search,
    loading,
    disableSearch,
    size,
    onLoadMore,
    loadingMore,
    loadMoreLabel,
    hasMore,
    loadingComponent,
    noOptionsComponent,
    menuProps,
  } = useAutocomplete<ValueType>()

  return (
    <Menu.Popup
      className={classNames(
        "ApolloAutocomplete-menuPopup",
        styles.autocomplete,
        styles.autocompletePopoverRoot,
        menuProps?.className
      )}
      render={(props) => (
        <div
          {...props}
          className={classNames(props?.className)}
          onKeyDown={onKeyboardEvent(props)}
          onKeyUp={onKeyboardEvent(props)}
        >
          <FullTagsAutocomplete
            onChange={onSearch}
            onChipClose={onChipClose}
            onClearAll={onClearAll}
            value={value}
            options={options}
            search={search}
            size={size}
            disableSearch={disableSearch}
          />
          <div
            className={classNames(
              "ApolloAutocomplete-menuRoot",
              styles.autocomplete,
              styles.autocompleteMenuRoot,
              menuProps?.className
            )}
            aria-label={menuProps?.["aria-label"]}
            aria-labelledby={menuProps?.["aria-labelledby"]}
            aria-describedby={menuProps?.["aria-describedby"]}
          >
            {loading ? (
              <div className={styles.autocompleteMenuItem}>
                {loadingComponent || (
                 <Typography level="bodyMedium" className={styles.loadingText}>Loading...</Typography>
                )}
              </div>
            ) : options?.length ? (
              <>
                {showSelectAll ? (
                  <AutocompleteMenuItem
                    key="select-all"
                    onClick={onSelectAll}
                    checkable={showSelectAll && !hideCheckbox}
                    indeterminate={!isAllSelected && value.length > 0}
                    selected={isAllSelected}
                    label={selectAllText}
                    value="all"
                    className={styles.menuOptionShowCheckbox}
                  />
                ) : menuLabelText ? (
                  <Typography
                    level="bodyMedium"
                    className={styles.menuOptionMenuLabelText}
                  >
                    {menuLabelText}
                  </Typography>
                ) : null}
                {options.map((option, index) => (
                  <AutocompleteMenuItem
                    key={option.label ?? index}
                    onClick={(event) => {
                      onChange(option, event)
                    }}
                    checkable={!hideCheckbox}
                    disabled={option?.disabled}
                    selected={value.includes(option.value)}
                    label={option.label}
                    value={option.value}
                    className={classNames(
                      styles.menuOption,
                      {
                        [styles.menuOptionIndented]: showSelectAll || menuLabelText,
                        [styles.menuOptionShowCheckbox]: !hideCheckbox,
                      }
                    )}
                  />
                ))}
              </>
            ) : (
              <div className={styles.autocompleteMenuItem}>
                {noOptionsComponent || (
                  <Typography level="bodyLarge">Not found</Typography>
                )}
              </div>
            )}
            {hasMore && (
              <ScrollTriggerAutocomplete
                onVisible={onLoadMore}
                loading={loadingMore}
                label={loadMoreLabel}
              />
            )}
          </div>
        </div>
      )}
    />
  )
}
