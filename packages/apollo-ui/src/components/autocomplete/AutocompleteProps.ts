import type { CSSProperties, ReactNode } from "react"

export type AutocompleteOption<T> = {
  label: string
  value: T
  disabled?: boolean
}

export type AutocompleteSize = "small" | "medium"

export type AutocompleteBaseProps = {
  // Styling
  fullWidth?: boolean
  className?: string
  style?: CSSProperties

  // Core functionality
  showSelectAll?: boolean
  filterLogic?: <T>(
    options: AutocompleteOption<T>[],
    search: string
  ) => AutocompleteOption<T>[]
  selectAllText?: string
  loading?: boolean
  onSearch?: (searchValue: string) => Promise<void> | void
  debounceMs?: number
  disableSearch?: boolean
  size?: AutocompleteSize
  search?: string
  hideCheckbox?: boolean
  menuLabelText?: string

  // Infinite loading
  onLoadMore?: () => Promise<void> | void
  loadingMore?: boolean
  loadMoreLabel?: ReactNode
  hasMore?: boolean
  loadingComponent?: ReactNode
  noOptionsComponent?: ReactNode

  // Form State Props
  error?: boolean
  required?: boolean
  placeholder?: string
  disabled?: boolean

  // Accessibility
  id?: string
  name?: string
  autoFocus?: boolean
  tabIndex?: number
  "aria-label"?: string
  "aria-labelledby"?: string
  "aria-describedby"?: string

  // Event Handlers
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void

  // Advanced customization
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>
  menuProps?: React.HTMLAttributes<HTMLUListElement>
}

export type SingleAutocompleteChangeEvent<T> = (
  value?: T,
  event?: React.MouseEvent<HTMLElement>
) => void

export type MultipleAutocompleteChangeEvent<T> = (
  value?: T[],
  event?: React.MouseEvent<HTMLElement>
) => void

export type MultipleAutocompleteProps<T> = {
  value?: T[]
  onChange?: MultipleAutocompleteChangeEvent<T>
  options?: AutocompleteOption<T>[]
  fullWidth?: boolean
  label?: string
  helperText?: string
  labelDecorator?: ReactNode
  helperTextDecorator?: ReactNode
} & AutocompleteBaseProps

export type SingleAutocompleteProps<T> = {
  value?: T
  onChange?: SingleAutocompleteChangeEvent<T>
  options?: AutocompleteOption<T>[]
  fullWidth?: boolean
  label?: string
  helperText?: string
  labelDecorator?: ReactNode
  helperTextDecorator?: ReactNode
} & AutocompleteBaseProps

export type AutocompleteProps<T> =
  | ({
      multiple: true
      defaultValue?: T[]
    } & MultipleAutocompleteProps<T>)
  | ({
      multiple?: never
      defaultValue?: T
    } & SingleAutocompleteProps<T>)
