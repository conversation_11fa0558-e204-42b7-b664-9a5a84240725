import { useCallback, useState } from "react"

import { AutocompleteProps } from "./AutocompleteProps"
import { AutocompleteProvider } from "./context/AutocompleteContext"
import { MultipleAutocomplete } from "./multiple/MultipleAutocomplete"
import { SingleAutocomplete } from "./single/SingleAutocomplete"

export function Autocomplete<ValueType>({
  // Extract props specific to Multiple/Single components
  multiple,
  value,
  defaultValue,
  onChange,
  showSelectAll,
  selectAllText,
  hideCheckbox,
  menuLabelText,
  // Rest of the props for Provider
  ...providerProps
}: AutocompleteProps<ValueType>) {
  const [internalValue, setInternalValue] = useState(defaultValue)

  const handleChange = useCallback(
    (
      newValue?: ValueType | ValueType[] | undefined,
      event?: React.MouseEvent<HTMLElement>
    ) => {
      if (value === undefined) {
        setInternalValue(newValue)
      }
      if (multiple) {
        onChange?.(newValue as ValueType[], event)
      } else {
        onChange?.(newValue as ValueType, event)
      }
    },
    [multiple, onChange, value]
  )

  const effectiveValue = value !== undefined ? value : internalValue

  const commonProps = {
    onSearch: providerProps.onSearch,
    menuLabelText,
  }

  return (
    <AutocompleteProvider {...providerProps}>
      {multiple ? (
        <MultipleAutocomplete
          {...commonProps}
          onChange={handleChange}
          value={effectiveValue as ValueType[]}
          showSelectAll={showSelectAll}
          selectAllText={selectAllText}
          hideCheckbox={hideCheckbox}
        />
      ) : (
        <SingleAutocomplete
          {...commonProps}
          onChange={handleChange}
          value={effectiveValue as ValueType}
        />
      )}
    </AutocompleteProvider>
  )
}
