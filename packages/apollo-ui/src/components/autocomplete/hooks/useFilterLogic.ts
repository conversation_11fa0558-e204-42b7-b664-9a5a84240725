import { useMemo } from "react"

import type { AutocompleteOption } from "../AutocompleteProps"

export function useFilterLogic<T>(
  options: AutocompleteOption<T>[] | undefined,
  search: string,
  customFilterLogic?: (
    options: AutocompleteOption<T>[],
    search: string
  ) => AutocompleteOption<T>[]
) {
  return useMemo(() => {
    if (!search) return options
    if (!options?.length) return []

    if (customFilterLogic) {
      return customFilterLogic(options, search)
    }

    return options.filter((option) =>
      option.label.toLowerCase().includes(search.toLowerCase())
    )
  }, [options, search, customFilterLogic])
}
