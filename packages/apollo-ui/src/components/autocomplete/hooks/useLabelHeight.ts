import { useCallback, useEffect, useRef, useState } from "react"

export const useLabelHeight = (hasLabel?: boolean) => {
  const [labelHeight, setLabelHeight] = useState(0)
  const labelRef = useRef<HTMLLabelElement | null>(null)

  const updateHeight = useCallback(() => {
    if (labelRef.current && hasLabel) {
      setLabelHeight(labelRef.current.offsetHeight + 8)
    } else {
      setLabelHeight(0)
    }
  }, [hasLabel])

  useEffect(() => {
    const resizeObserver = new ResizeObserver(updateHeight)
    if (labelRef.current) {
      resizeObserver.observe(labelRef.current)
    }
    return () => resizeObserver.disconnect()
  }, [updateHeight])

  return { labelHeight, labelRef }
}
