import { PropsWithChildren } from "react"
import { Select as BaseSelect } from "@base-ui-components/react/select"

import type { FieldProps } from "../field"
import { InputProps } from "../input"

export type SelectProps<ValueType> = PropsWithChildren<
  {
    ref?: React.Ref<HTMLDivElement>
    fieldProps?: FieldProps
    onChange?: (value: ValueType) => void
    size?: "medium" | "small"
  } & Pick<InputProps, "placeholder"> &
    Pick<
      FieldProps,
      "label" | "fullWidth" | "disabled" | "helperText" | "error" | "required" | "labelDecorator" | "helperTextDecorator"
    > &
    Omit<BaseSelect.Root.Props<ValueType>, "onValueChange">
>

export type OptionProps<T = string> = {
  label: string
  value: T
} & BaseSelect.Item.Props
