@layer legacy {
  .selectMenuPositioner {
    max-height: 100%;
    overflow-y: auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    z-index: 701;
  }


  .selectMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    border-radius: 8px;
    background: var(--apl-colors-surface-static-ui-default, #FFF);

    width: var(--anchor-width);
    outline: none;

    &>* {
      flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

      &:first-of-type,
      &:last-of-type {
        border-radius: 8px;
      }
    }
  }
}

@layer apollo {
  .selectMenuPositioner {
    max-height: 100%;
    overflow-y: auto;
    box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
    z-index: 701;
  }


  .selectMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    border-radius: var(--apl-alias-radius-radius4, 8px);
    background: var(--apl-alias-color-background-and-surface-background, #FFF);

    width: var(--anchor-width);
    outline: none;

    &>* {
      flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

      &:first-of-type,
      &:last-of-type {
        border-radius: var(--apl-alias-radius-radius4, 8px);
      }
    }
  }
}

.selectRoot {
  outline: none;
  width: fit-content;
}

.selectRootFullWidth {
  width: 100%;
}

.selectMenuItem {
  width: 100%;
  outline: none;
}