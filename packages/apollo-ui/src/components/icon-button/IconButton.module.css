@layer legacy {
    .iconButtonLarge {
        --apl-icon-button-size: 40px;
        --apl-icon-button-svg-size: 24px;
    }

    .iconButtonSmall {
        --apl-icon-button-size: 24px;
        --apl-icon-button-padding: var(--apl-space-padding-2xs, 4px);

        --apl-icon-button-svg-size: 16px;
    }

}

@layer apollo {
    .iconButtonLarge {
        --apl-icon-button-size: 48px;
        --apl-icon-button-svg-size: 24px;
        --apl-icon-button-padding: var(--apl-alias-spacing-padding-padding7, 12px);
    }

    .iconButtonSmall {
        --apl-icon-button-size: 32px;
        --apl-icon-button-padding: var(--apl-alias-spacing-padding-padding4, 6px);

        --apl-icon-button-svg-size: 16px;
    }
}

.iconButtonRoot {
    padding: var(--apl-icon-button-padding, 8px);

    width: var(--apl-icon-button-size, 40px);
    height: var(--apl-icon-button-size, 40px);
    min-width: var(--apl-icon-button-size, 40px);
    max-width: var(--apl-icon-button-size, 40px);
    min-height: var(--apl-icon-button-size, 40px);
    max-height: var(--apl-icon-button-size, 40px);

    & svg {
        width: var(--apl-icon-button-svg-size, 24px);
    }
}