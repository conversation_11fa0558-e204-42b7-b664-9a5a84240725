import type { Ref } from "react"

import type {
  BaseButtonProps,
  LinkButtonProps,
  NormalButtonProps,
} from "../button"

export type IconButtonSize = "small" | "large"
export type IconButtonVariant = "filled" | "outline" | "icon"

export type IconButtonBaseProps = {
  ref?: Ref<HTMLButtonElement>
  size?: IconButtonSize
  variant?: IconButtonVariant
} & Pick<BaseButtonProps, "color">

export type IconButtonProps = (
  | ({ href: string } & LinkButtonProps)
  | ({
      href?: never
    } & NormalButtonProps)
) &
  IconButtonBaseProps
