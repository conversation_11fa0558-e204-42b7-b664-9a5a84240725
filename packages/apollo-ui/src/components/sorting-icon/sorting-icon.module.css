@layer legacy {
    .sortingIconAsc {
        height: 16px;
        color: var(--apl-colors-content-subdued);
        transform: translateY(2px);
    }

    .sortingIconDesc {
        height: 16px;
        color: var(--apl-colors-content-subdued);
        transform: translateY(-2px);
    }

    .sortingIconActive {
        color: var(--apl-colors-content-primary-default);
    }
}

@layer apollo {
    .sortingIconAsc {
        max-height: inherit;
        color: var(--apl-alias-color-outline-and-border-outline);
    }

    .sortingIconDesc {
        max-height: inherit;
        color: var(--apl-alias-color-outline-and-border-outline);
    }

    .sortingIconActive {
        color: var(--apl-alias-color-primary-primary);
    }
}

.sortingIconRoot {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}