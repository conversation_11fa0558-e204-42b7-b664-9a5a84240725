import classNames from "classnames"

import { CaretDown } from "../common/CaretDown"
import { CaretUp } from "../common/CaretUp"
import styles from "./sorting-icon.module.css"
import type { SortingIconProps } from "./SortingIconProps"

export function SortingIcon({
  status = "default",
  className,
  ref,
  ...spanProps
}: SortingIconProps) {
  return (
    <span
      ref={ref}
      {...spanProps}
      className={classNames(
        "ApolloSortingIcon-root",
        styles.sortingIconRoot,
        className
      )}
    >
      <CaretUp
        aria-label="ascending"
        className={classNames(
          "ApolloSortingIcon-iconASC",
          styles.sortingIconAsc,
          {
            [styles.sortingIconActive]: status === "asc",
          }
        )}
      />
      <CaretDown
        aria-label="descending"
        className={classNames(
          "ApolloSortingIcon-iconDESC",
          styles.sortingIconDesc,
          {
            [styles.sortingIconActive]: status === "desc",
          }
        )}
      />
    </span>
  )
}
