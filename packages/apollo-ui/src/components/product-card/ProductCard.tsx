import type { ElementType } from "react"
import classNames from "classnames"

import { Picture } from "../common/Picture"
import { Typography } from "../typography"
import styles from "./product-card.module.css"
import type { ProductCardProps } from "./ProductCardProps"

export function ProductCard<TImageComponent extends ElementType = "img">(
  props: ProductCardProps<TImageComponent>
) {
  const {
    title,
    extra,
    body,
    footer,
    className,
    imageSrc,
    imageOverlay,
    imageProps,
    noImage,
    size = "160px",
    ImageComponent = "img",
    ref,
    ...divProps
  } = props

  const hasImage = imageSrc ?? imageProps?.src

  return (
    <article
      {...divProps}
      className={classNames(
        "ApolloProductCard-root",
        styles.root,
        { [styles.fillSize]: size === "fill" },
        className
      )}
      ref={ref}
      style={{
        ...divProps?.style,
        width: size !== "fill" ? size : undefined,
        minWidth: size !== "fill" ? size : undefined,
      }}
    >
      <header
        className={classNames(
          "ApolloProductCard-mediaContainer",
          styles.mediaContainer
        )}
      >
        <figure
          className={classNames(
            "ApolloProductCard-imageContainer",
            styles.imageContainer,
            { [styles.imageContainerWithImage]: hasImage }
          )}
        >
          {hasImage ? (
            <ImageComponent
              {...imageProps}
              className={classNames(
                "ApolloProductCard-image",
                styles.image,
                imageProps?.className
              )}
              src={imageSrc ?? imageProps?.src}
            />
          ) : (
            <div
              className={classNames(
                "ApolloProductCard-noImageBox",
                styles.noImageBox
              )}
            >
              {noImage ?? (
                <div className={classNames(styles.noImageDefault)}>
                  <Picture />
                </div>
              )}
            </div>
          )}
          {imageOverlay ? (
            ["string", "number"].includes(typeof imageOverlay) ? (
              <div
                className={classNames(
                  "ApolloProductCard-imageOverlay",
                  styles.imageOverlay
                )}
              >
                <Typography
                  className={classNames(
                    "ApolloProductCard-imageOverlayText",
                    styles.imageOverlayText
                  )}
                  level="titleSmall"
                >
                  {imageOverlay}
                </Typography>
              </div>
            ) : (
              imageOverlay
            )
          ) : null}
        </figure>
      </header>
      {extra ? (
        <section
          className={classNames(
            "ApolloProductCard-extraContainer",
            styles.extraContainer
          )}
        >
          {extra}
        </section>
      ) : null}
      <section
        className={classNames(
          "ApolloProductCard-titleContainer",
          styles.titleContainer
        )}
      >
        {["string", "number"].includes(typeof title) ? (
          <Typography
            className={classNames("ApolloProductCard-title", styles.title)}
            level="titleSmall"
          >
            {title}
          </Typography>
        ) : (
          title
        )}
      </section>
      <main
        className={classNames(
          "ApolloProductCard-bodyContainer",
          styles.bodyContainer
        )}
      >
        {body}
      </main>
      <footer
        className={classNames(
          "ApolloProductCard-footerContainer",
          styles.footerContainer
        )}
      >
        {footer}
      </footer>
    </article>
  )
}
