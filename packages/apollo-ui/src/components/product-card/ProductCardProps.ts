import type {
  ComponentProps,
  ElementType,
  HTMLAttributes,
  ImgHTMLAttributes,
  ReactNode,
  Ref,
} from "react"

export type ProductCardProps<
  TImageComponent extends ElementType = "img",
  TImageProps = TImageComponent extends "img"
    ? ImgHTMLAttributes<HTMLImageElement>
    : ComponentProps<TImageComponent>,
> = HTMLAttributes<HTMLDivElement> & {
  ref?: Ref<HTMLDivElement>
  /**
   * @default "160px"
   */
  size?: string | "fill"
  /**
   * Extra content to render above the title
   */
  extra?: ReactNode
  /**
   * Title of the product card
   */
  title: ReactNode
  /**
   * Main content body of the product card
   */
  body?: ReactNode
  /**
   * Footer content of the product card
   */
  footer?: ReactNode
  /**
   * Source URL for the product image
   */
  imageSrc?: string
  /**
   * Additional props for the image component
   */
  imageProps?: Partial<TImageProps>
  /**
   * Content to display when there is no image
   */
  noImage?: ReactNode
  /**
   * Component to use for rendering images
   * @default "img"
   */
  ImageComponent?: TImageComponent
  /**
   * Overlay content to display on top of the image
   */
  imageOverlay?: ReactNode
}
