.root {
    padding: var(--apl-space-padding-xs);
    display: flex;
    flex-direction: column;
    gap: var(--apl-space-gap-xs);
    justify-content: flex-start;
    align-items: center;
    border-radius: 8px;
    overflow: hidden;
}

.fillSize {
    min-width: 100px;
    width: 100%;
    max-width: 280px;
}

.mediaContainer {
    align-self: stretch;
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: var(--apl-space-padding-xs);
    background-color: var(--apl-colors-surface-static-ui-default);
}

.imageContainer {
    position: relative;
    align-self: stretch;
    flex: 1;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0;
}

.imageContainerWithImage {
    background-color: var(--apl-global-colors-gray-10);
}

.image {
    width: 100%;
    height: 100%;
    max-height: 280px;
    aspect-ratio: 1 / 1;
    object-fit: contain;
}

.noImageBox {
    width: 100%;
    aspect-ratio: 1 / 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.noImageDefault {
    width: 100%;
    height: 100%;
    background-color: var(--apl-colors-surface-static-ui-active);
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    color: var(--apl-colors-content-disabled);
}

.imageOverlay {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.imageOverlayText {
    color: var(--apl-colors-content-inversed);
}

.extraContainer {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    align-self: stretch;
}

.titleContainer {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    align-self: stretch;
}

.title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    line-height: 23.1px;
}

.bodyContainer {
    align-self: stretch;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: var(--apl-space-gap-2xs);
}

.footerContainer {
    align-self: stretch;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}