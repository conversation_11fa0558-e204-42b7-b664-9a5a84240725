@layer legacy {
    .input {
        --apl-input-border-radius: 8px;
        --apl-input-border: var(--apl-colors-border-default, #D3D7E1);
        --apl-input-background: var(--apl-colors-surface-static-ui-default, #FFF);
        --apl-input-text: var(--apl-colors-content-default, #0C0E11);
        --apl-input-gap: var(--apl-space-gap-xs, 8px);
        --apl-input-hover-border: var(--apl-colors-border-primary-subdued, #409261);
        --apl-input-focus-border: var(--apl-colors-border-primary-default, #006D2E);
        --apl-input-padding-lr: var(--apl-space-padding-xs, 8px);
        --apl-input-small-padding-tb: var(--apl-space-padding-2xs, 4px);
        --apl-input-medium-padding-tb: var(--apl-space-padding-xs, 8px);

        /* invalid input */
        --apl-input-invalid-border: var(--apl-colors-border-danger-default, #E74747);

        /* disabled input */
        --apl-input-disabled-border: var(--apl-colors-border-disabled, #BEC4D1);
        --apl-input-disabled-background: var(--apl-colors-surface-static-ui-disabled, #F6F7FB);
        --apl-input-disabled-text-color: var(--apl-colors-content-description, #5C6372);

        ::placeholder {
            color: var(--apl-colors-content-placeholder, #BEC4D1);
        }

        /* font */
        --apl-input-small-font-size: var(--apl-typography-body2-font-size, 14px);
        --apl-input-medium-font-size: var(--apl-typography-body1-font-size, 16px);

    }
}

@layer apollo {
    .input {
        --apl-input-border-radius: var(--apl-alias-radius-radius4, 8px);
        --apl-input-border: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-input-background: var(--apl-alias-color-background-and-surface-background, #FFF);
        --apl-input-text: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        --apl-input-placeholder: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
        --apl-input-gap: var(--apl-alias-spacing-gap-gap4, 6px);
        --apl-input-hover-border: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-input-focus-border: var(--apl-alias-color-primary-focused, #49A25C);
        --apl-input-padding-lr: var(--apl-alias-spacing-padding-padding5, 8px);
        --apl-input-small-padding-tb: var(--apl-alias-spacing-padding-padding3, 4px);
        --apl-input-medium-padding-tb: var(--apl-alias-spacing-padding-padding4, 6px);

        /* invalid input */
        --apl-input-invalid-border: var(--apl-alias-color-error-error, #C0000B);

        /* disabled input */
        --apl-input-disabled-border: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-input-disabled-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-input-disabled-text-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

        ::placeholder {
            color: var(--apl-input-placeholder);
        }

        /* font */
        --apl-input-small-font-size: var(--apl-alias-typography-body-large-font-size, 16px);
        --apl-input-medium-font-size: var(--apl-alias-typography-body-large-font-size, 16px);
    }
}


.inputRoot {
    border-radius: var(--apl-input-border-radius);
    border: 1px solid var(--apl-input-border);
    background: var(--apl-input-background);
    color: var(--apl-input-text);

    min-height: var(--apl-input-height);
    max-height: var(--apl-input-height);

    /* Shadow text input */
    box-shadow: 1px 1px 0px 0px rgba(154, 154, 154, 0.08);

    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: var(--apl-input-gap);
    align-self: stretch;
    outline: none;

    transition: background 150ms cubic-bezier(0.4, 0, 0.2, 1);

    &:hover:not(.inputRootError):not(.inputElementDisabled) {
        --apl-input-border: var(--apl-input-hover-border);
    }

    &:focus-within:not(.inputRootError):not(.inputElementDisabled) {
        --apl-input-border: var(--apl-input-focus-border);
    }


}

.inputRootError,
.inputRoot:has(input[data-invalid]) {
    --apl-input-border: var(--apl-input-invalid-border);
    background: var(--apl-input-background);

    box-shadow: 1px 1px 0px 0px rgba(154, 154, 154, 0.08);
}

.inputRootSmall {
    --apl-input-height: 32px;
    padding: 0px var(--apl-input-padding-lr);

    & input {
        padding: var(--apl-input-small-padding-tb) 0px;
    }
}

.inputSmall {
    composes: apl-typography-body-large from '../../base.module.css';
    font-size: var(--apl-input-small-font-size);
}

.inputRootMedium {
    --apl-input-height: 42px;
    padding: 0px var(--apl-input-padding-lr);

    & input {
        padding: var(--apl-input-medium-padding-tb) 0px;
    }
}

.inputRootFullWidth {
    width: 100%;
}

.inputElement {
    border: none;
    background: transparent;
    outline: none;
    width: 100%;
    align-self: stretch;

    composes: apl-typography-body-large from '../../base.module.css';
    font-size: var(--apl-input-medium-font-size);

}

.inputElementDisabled {
    --apl-input-border: var(--apl-input-disabled-border);
    --apl-input-background: var(--apl-input-disabled-background);
    color: var(--apl-input-disabled-text-color);
}

.inputStartDecorator,
.inputEndDecorator {
    color: inherit;
    display: flex;
    justify-content: center;
    align-items: center;
}