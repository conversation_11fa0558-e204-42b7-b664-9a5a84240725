import type {
  ComponentType,
  HTMLAttributes,
  InputHTMLAttributes,
  ReactNode,
  Ref,
} from "react"

import { FieldProps } from "../field"

export type BaseInputProps = {
  fullWidth?: boolean
  size?: "medium" | "small"
  startInputAddOn?: ReactNode
  endInputAddOn?: ReactNode
  inputWrapper?: ComponentType
  startDecorator?: ReactNode
  endDecorator?: ReactNode
  error?: boolean
  rootRef?: Ref<HTMLDivElement>
  rootProps?: InputRootProps
  fieldProps?: FieldProps
  ref?: Ref<HTMLInputElement>
} & Pick<FieldProps, "label" | "helperText" | "error" | "required" | "labelDecorator" | "helperTextDecorator" | "hasCharacterCount">

export type NormalInputProps = Omit<
  InputHTMLAttributes<HTMLInputElement>,
  "size"
>

export type InputRootProps = HTMLAttributes<HTMLDivElement>

export type InputProps = NormalInputProps & BaseInputProps
