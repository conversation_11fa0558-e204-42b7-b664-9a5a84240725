import React, { Fragment, useCallback, useState } from "react"
import { Field as BaseField } from "@base-ui-components/react"
import classNames from "classnames"

import { Field } from "../field"
import styles from "./input.module.css"
import type { InputProps } from "./InputProps"

export function Input({
  label,
  helperText,
  ref,
  rootRef,
  className,
  error,
  fullWidth,
  startDecorator,
  endDecorator,
  rootProps,
  fieldProps,
  required,
  startInputAddOn,
  endInputAddOn,
  inputWrapper,
  size = "medium",
  labelDecorator,
  helperTextDecorator,
  hasCharacterCount,
  onChange,
  ...props
}: InputProps) {
  const sizeStyles = {
    small: styles.inputRootSmall,
    medium: styles.inputRootMedium,
  }

  const inputRef = React.useRef<HTMLInputElement>(null)
  const [characterCount, setCharacterCount] = useState<number>(0)

  const handleClick = () => {
    inputRef.current?.focus()
  }

  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(event)
      setCharacterCount(event.target.value.length)
    },
    [onChange]
  )

  const Wrapper = inputWrapper ?? Fragment

  return (
    <Field
      {...fieldProps}
      label={label}
      required={required}
      helperText={helperText}
      error={error}
      labelDecorator={labelDecorator}
      helperTextDecorator={helperTextDecorator}
      hasCharacterCount={hasCharacterCount}
      characterCount={characterCount}
      maxLength={props?.maxLength}
      className={classNames(
        "ApolloInput-fieldRoot",
        {
          [styles.inputRootFullWidth]: fullWidth,
        },
        fieldProps?.className
      )}
    >
      <BaseField.Control
        {...props}
        render={(fieldProps, fieldState) => (
          <div
            className={classNames(
              "ApolloInput-controlRoot",
              styles.input,
              styles.inputRoot,
              styles.inputRootFullWidth,
              {
                [styles.inputRootError]: fieldState?.valid
                  ? !fieldState?.valid
                  : error,
                [styles.inputElementDisabled]:
                  fieldState?.disabled || props?.disabled,
              },
              sizeStyles[size],
              className
            )}
            {...rootProps}
            ref={rootRef}
            onClick={handleClick}
          >
            {startDecorator && (
              <span
                className={classNames(
                  "ApolloInput-startDecorator",
                  styles.inputStartDecorator
                )}
              >
                {startDecorator}
              </span>
            )}
            <Wrapper>
              {startInputAddOn}
              <input
                {...fieldProps}
                onChange={handleChange}
                onFocus={fieldProps?.onFocus}
                onBlur={fieldProps?.onBlur}
                className={classNames(styles.inputElement, {
                  [styles.inputSmall]: size === "small",
                })}
                ref={(el) => {
                  inputRef.current = el
                  if (typeof ref === "function") ref(el)
                  else if (ref) ref.current = el
                }}
              />
              {endInputAddOn}
            </Wrapper>
            {endDecorator && (
              <span
                className={classNames(
                  "ApolloInput-endDecorator",
                  styles.inputEndDecorator
                )}
              >
                {endDecorator}
              </span>
            )}
          </div>
        )}
      />
    </Field>
  )
}
