import type { ComponentProps } from "react"

export function Calendar(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.795 1.312 C 6.762 1.320,6.711 1.347,6.683 1.373 C 6.635 1.415,6.630 1.500,6.630 2.312 L 6.630 3.204 4.297 3.215 L 1.965 3.225 1.795 3.308 C 1.609 3.400,1.447 3.561,1.350 3.750 L 1.288 3.872 1.299 12.923 C 1.307 19.419,1.320 22.020,1.344 22.135 C 1.381 22.314,1.562 22.544,1.720 22.615 C 1.775 22.639,2.031 22.664,2.382 22.681 C 3.130 22.716,20.870 22.716,21.618 22.681 C 21.969 22.664,22.225 22.639,22.280 22.615 C 22.438 22.544,22.619 22.314,22.656 22.135 C 22.680 22.020,22.693 19.419,22.701 12.923 L 22.712 3.872 22.650 3.750 C 22.553 3.561,22.391 3.400,22.205 3.308 L 22.035 3.225 19.703 3.215 L 17.370 3.204 17.370 2.308 L 17.370 1.411 17.289 1.358 C 17.217 1.311,17.113 1.304,16.385 1.296 C 15.570 1.287,15.561 1.288,15.491 1.354 L 15.420 1.420 15.420 2.315 L 15.420 3.210 12.000 3.210 L 8.580 3.210 8.580 2.315 L 8.580 1.420 8.511 1.355 C 8.443 1.291,8.423 1.290,7.648 1.293 C 7.212 1.295,6.828 1.304,6.795 1.312 M6.630 5.829 L 6.630 6.499 6.716 6.581 L 6.802 6.664 7.615 6.654 C 8.415 6.645,8.430 6.644,8.497 6.577 C 8.563 6.510,8.565 6.489,8.575 5.834 L 8.584 5.160 12.002 5.160 L 15.420 5.160 15.420 5.826 L 15.420 6.492 15.501 6.576 L 15.581 6.660 16.395 6.660 L 17.209 6.660 17.289 6.576 L 17.370 6.492 17.370 5.826 L 17.370 5.160 19.080 5.160 L 20.790 5.160 20.790 6.975 L 20.790 8.790 12.000 8.790 L 3.210 8.790 3.210 6.975 L 3.210 5.160 4.920 5.160 L 6.630 5.160 6.630 5.829 M20.790 15.705 L 20.790 20.790 12.000 20.790 L 3.210 20.790 3.210 15.705 L 3.210 10.620 12.000 10.620 L 20.790 10.620 20.790 15.705 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
