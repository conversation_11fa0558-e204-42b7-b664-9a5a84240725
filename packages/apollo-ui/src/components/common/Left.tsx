import type { ComponentProps } from "react"

export function Left(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.127 2.090 C 15.998 2.922,6.423 10.402,5.396 11.254 C 5.015 11.571,4.939 11.694,4.941 12.000 C 4.942 12.339,4.995 12.416,5.540 12.862 C 7.076 14.118,17.234 22.033,17.419 22.117 C 17.464 22.138,17.508 22.134,17.569 22.105 L 17.655 22.064 17.663 20.882 C 17.670 19.790,17.667 19.696,17.618 19.641 C 17.555 19.571,16.159 18.469,13.695 16.545 C 11.789 15.057,9.380 13.170,8.467 12.451 C 8.165 12.214,7.922 12.010,7.927 11.998 C 7.942 11.957,8.984 11.136,13.140 7.890 C 16.207 5.493,17.459 4.508,17.572 4.401 L 17.670 4.308 17.670 3.182 C 17.669 2.562,17.661 2.026,17.652 1.991 C 17.637 1.935,17.529 1.860,17.463 1.860 C 17.450 1.860,17.299 1.963,17.127 2.090 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
