import type { ComponentProps } from "react"

export function CaretDown(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.135 7.049 C 4.618 7.061,4.079 7.074,4.032 7.101 C 3.921 7.165,3.784 7.345,3.763 7.454 C 3.722 7.676,3.588 7.504,5.836 10.125 C 8.116 12.785,11.139 16.269,11.598 16.766 C 11.734 16.913,11.760 16.929,11.902 16.942 C 12.215 16.973,12.224 16.967,12.713 16.416 C 13.914 15.065,18.694 9.528,19.898 8.093 C 20.074 7.882,20.227 7.676,20.238 7.635 C 20.283 7.453,20.147 7.196,19.953 7.098 C 19.890 7.066,18.776 7.056,14.025 7.045 C 10.807 7.038,7.257 7.040,6.135 7.049 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
