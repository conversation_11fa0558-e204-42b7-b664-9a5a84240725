import { ComponentProps } from "react"

export function DeleteOutlined(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.025 1.317 L 7.245 1.335 6.983 1.463 C 6.489 1.705,6.113 2.194,6.032 2.700 C 6.014 2.810,6.000 3.382,6.000 4.008 L 6.000 5.121 4.177 5.136 C 2.511 5.150,2.345 5.156,2.249 5.204 C 2.066 5.298,1.906 5.448,1.814 5.614 L 1.725 5.775 1.725 6.364 C 1.725 6.902,1.730 6.959,1.782 7.017 C 1.838 7.078,1.863 7.080,2.690 7.080 C 3.479 7.080,3.540 7.084,3.540 7.132 C 3.540 7.161,3.554 7.435,3.570 7.740 C 3.586 8.045,3.694 10.320,3.810 12.795 C 4.194 21.010,4.206 21.221,4.306 21.535 C 4.461 22.025,4.829 22.400,5.364 22.614 L 5.565 22.695 12.000 22.695 L 18.435 22.695 18.636 22.614 C 19.177 22.397,19.551 22.010,19.706 21.504 C 19.793 21.219,19.803 21.031,20.294 10.545 C 20.368 8.961,20.438 7.533,20.448 7.372 L 20.468 7.080 21.314 7.080 C 22.137 7.080,22.162 7.078,22.218 7.017 C 22.270 6.959,22.275 6.902,22.275 6.364 L 22.275 5.775 22.186 5.614 C 22.094 5.448,21.934 5.298,21.751 5.204 C 21.655 5.156,21.489 5.150,19.825 5.136 L 18.004 5.121 17.993 3.888 C 17.981 2.549,17.983 2.569,17.796 2.201 C 17.619 1.855,17.196 1.502,16.806 1.377 L 16.605 1.312 12.705 1.305 C 10.560 1.301,8.454 1.307,8.025 1.317 M12.004 3.195 C 15.292 3.195,16.062 3.188,16.073 3.156 C 16.083 3.127,16.096 3.126,16.127 3.152 C 16.162 3.181,16.162 3.193,16.124 3.230 C 16.087 3.267,16.080 3.425,16.080 4.202 L 16.080 5.130 12.000 5.130 L 7.920 5.130 7.920 4.202 C 7.920 3.436,7.913 3.267,7.877 3.231 C 7.845 3.199,7.843 3.179,7.868 3.154 C 7.893 3.129,7.908 3.130,7.925 3.158 C 7.943 3.187,8.801 3.195,12.004 3.195 M18.497 7.433 C 18.480 7.728,17.955 18.918,17.892 20.332 L 17.871 20.790 12.000 20.790 L 6.129 20.790 6.109 20.363 C 5.742 12.501,5.518 7.738,5.503 7.447 L 5.483 7.080 12.000 7.080 L 18.517 7.080 18.497 7.433 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
