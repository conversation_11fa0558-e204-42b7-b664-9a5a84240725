import type { ComponentProps } from "react"

export function CaretUp(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.790 7.071 C 11.765 7.080,11.713 7.116,11.674 7.151 C 11.519 7.289,8.904 10.299,6.117 13.545 C 3.938 16.083,3.787 16.266,3.764 16.390 C 3.728 16.582,3.840 16.797,4.031 16.898 L 4.155 16.965 12.000 16.965 L 19.845 16.965 19.969 16.898 C 20.091 16.834,20.191 16.707,20.234 16.565 C 20.284 16.402,20.225 16.295,19.822 15.815 C 18.692 14.468,13.676 8.655,12.667 7.522 C 12.479 7.312,12.287 7.119,12.239 7.095 C 12.156 7.052,11.883 7.037,11.790 7.071 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
