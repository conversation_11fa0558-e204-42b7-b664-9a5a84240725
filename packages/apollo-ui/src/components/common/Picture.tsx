import type { ComponentProps } from "react"

export function Picture(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M0.481 2.672 C 0.269 2.788,0.207 2.849,0.101 3.039 L 0.015 3.195 0.007 11.977 L -0.002 20.759 0.063 20.887 C 0.172 21.101,0.289 21.226,0.475 21.325 L 0.653 21.420 11.998 21.420 L 23.342 21.420 23.469 21.356 C 23.682 21.248,23.806 21.131,23.906 20.943 L 24.002 20.764 23.993 11.979 L 23.985 3.195 23.896 3.035 C 23.796 2.853,23.694 2.758,23.487 2.653 L 23.342 2.580 11.993 2.581 L 0.645 2.581 0.481 2.672 M22.076 9.758 C 22.074 12.649,22.064 15.228,22.053 15.488 L 22.035 15.960 21.568 15.413 C 21.312 15.111,20.306 13.921,19.333 12.767 C 16.298 9.167,16.066 8.897,15.981 8.867 C 15.819 8.810,16.114 8.479,12.064 13.275 C 10.549 15.069,9.681 16.080,9.657 16.080 C 9.634 16.080,8.917 15.247,7.529 13.605 C 5.985 11.779,5.726 11.490,5.632 11.490 C 5.602 11.490,5.529 11.541,5.471 11.602 C 5.291 11.793,3.997 13.306,2.955 14.544 L 1.965 15.720 1.946 15.398 C 1.936 15.220,1.926 12.696,1.924 9.787 L 1.920 4.500 12.000 4.500 L 22.080 4.500 22.076 9.758 M6.059 5.818 C 5.388 5.911,4.703 6.395,4.357 7.020 C 4.133 7.424,4.036 7.998,4.108 8.492 C 4.213 9.211,4.690 9.878,5.339 10.213 C 5.725 10.412,5.993 10.477,6.435 10.478 C 6.865 10.480,7.069 10.433,7.455 10.245 C 8.187 9.889,8.655 9.228,8.766 8.394 C 8.808 8.073,8.772 7.741,8.655 7.390 C 8.410 6.654,7.737 6.031,7.009 5.866 C 6.776 5.813,6.275 5.788,6.059 5.818 M6.777 7.489 C 7.157 7.678,7.284 8.184,7.041 8.537 C 6.895 8.748,6.733 8.852,6.518 8.871 C 6.265 8.894,6.069 8.824,5.914 8.655 C 5.756 8.484,5.700 8.344,5.700 8.119 C 5.701 7.971,5.718 7.905,5.787 7.785 C 5.834 7.702,5.898 7.613,5.929 7.586 C 6.152 7.390,6.498 7.350,6.777 7.489 M15.972 11.472 C 15.990 11.496,16.417 11.998,16.921 12.590 C 17.426 13.181,18.792 14.796,19.959 16.178 L 22.080 18.691 22.080 19.096 L 22.080 19.500 12.000 19.500 L 1.920 19.500 1.920 18.967 L 1.920 18.435 3.033 17.115 C 4.672 15.170,5.614 14.070,5.638 14.070 C 5.660 14.070,6.279 14.793,8.511 17.428 C 9.118 18.145,9.629 18.736,9.647 18.741 C 9.681 18.752,10.327 18.000,12.541 15.375 C 13.697 14.004,15.052 12.406,15.739 11.604 C 15.896 11.421,15.920 11.407,15.972 11.472 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
