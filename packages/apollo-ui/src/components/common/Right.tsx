import type { ComponentProps } from "react"

export function Right(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.428 1.941 C 6.380 2.015,6.375 2.119,6.375 3.104 C 6.375 3.698,6.385 4.221,6.398 4.265 C 6.427 4.371,7.214 4.999,12.165 8.866 C 13.872 10.198,15.553 11.517,16.042 11.907 C 16.096 11.950,16.140 11.994,16.140 12.005 C 16.140 12.016,16.049 12.096,15.938 12.184 C 15.260 12.717,13.541 14.062,11.745 15.465 C 7.685 18.636,6.436 19.627,6.404 19.703 C 6.372 19.779,6.349 21.330,6.374 21.701 C 6.401 22.097,6.444 22.175,6.601 22.110 C 6.820 22.019,7.542 21.469,11.460 18.409 C 17.838 13.427,18.938 12.552,19.014 12.399 C 19.153 12.120,19.157 11.928,19.027 11.628 C 18.964 11.481,18.498 11.105,14.655 8.096 C 7.719 2.666,6.668 1.860,6.524 1.860 C 6.501 1.860,6.457 1.897,6.428 1.941 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
