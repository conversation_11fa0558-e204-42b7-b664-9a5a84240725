import type { ComponentProps } from "react"

export function PaperClip(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.890 1.709 C 13.183 1.779,12.374 2.021,11.685 2.368 C 10.864 2.781,10.798 2.836,9.130 4.467 C 6.785 6.760,2.977 10.568,2.956 10.641 C 2.945 10.679,2.958 10.749,2.987 10.806 C 3.060 10.951,4.020 11.901,4.119 11.926 C 4.186 11.943,4.227 11.926,4.343 11.831 C 4.421 11.768,5.957 10.251,7.755 8.460 C 11.020 5.210,11.673 4.578,12.019 4.331 C 12.281 4.144,12.800 3.889,13.136 3.782 C 13.533 3.656,13.812 3.602,14.240 3.570 C 15.080 3.505,15.917 3.676,16.681 4.067 C 17.862 4.671,18.690 5.717,19.042 7.050 C 19.109 7.301,19.119 7.414,19.132 7.995 C 19.148 8.738,19.119 8.992,18.957 9.502 C 18.844 9.859,18.598 10.375,18.391 10.687 C 18.178 11.009,16.743 12.475,12.673 16.527 C 9.187 20.000,9.168 20.017,8.716 20.220 C 8.304 20.405,8.170 20.430,7.608 20.430 C 7.047 20.430,6.905 20.406,6.527 20.249 C 5.787 19.941,5.197 19.281,4.945 18.479 C 4.882 18.277,4.875 18.200,4.876 17.685 C 4.876 17.139,4.880 17.105,4.960 16.875 C 5.061 16.587,5.240 16.244,5.407 16.023 C 5.551 15.831,10.936 10.417,11.999 9.396 C 12.402 9.008,12.814 8.631,12.914 8.557 L 13.095 8.424 13.380 8.435 C 13.625 8.446,13.687 8.459,13.825 8.535 C 14.007 8.634,14.101 8.735,14.207 8.943 C 14.268 9.065,14.280 9.131,14.280 9.355 C 14.280 9.658,14.264 9.691,13.954 10.050 C 13.862 10.157,12.577 11.460,11.099 12.945 C 9.622 14.430,8.361 15.709,8.298 15.787 C 8.204 15.904,8.187 15.944,8.204 16.012 C 8.231 16.121,9.262 17.149,9.371 17.177 C 9.421 17.189,9.476 17.180,9.518 17.152 C 9.633 17.077,12.474 14.252,14.089 12.607 C 15.000 11.680,15.674 10.969,15.739 10.867 C 16.342 9.931,16.329 8.721,15.708 7.788 C 15.323 7.211,14.664 6.756,13.999 6.608 C 13.672 6.535,13.017 6.544,12.675 6.627 C 12.307 6.715,11.909 6.918,11.622 7.162 C 11.317 7.423,6.353 12.365,5.008 13.747 C 3.901 14.885,3.675 15.153,3.465 15.570 C 3.066 16.364,2.901 17.202,2.969 18.091 C 3.042 19.054,3.457 20.031,4.089 20.727 C 4.746 21.450,5.354 21.851,6.200 22.119 C 6.704 22.279,7.049 22.331,7.605 22.332 C 8.363 22.332,8.968 22.189,9.675 21.840 C 10.384 21.490,10.308 21.561,14.655 17.208 C 16.833 15.027,18.865 12.979,19.170 12.659 C 19.815 11.981,20.050 11.653,20.385 10.965 C 20.844 10.025,21.044 9.156,21.042 8.115 C 21.041 7.107,20.845 6.239,20.418 5.355 C 20.085 4.664,19.758 4.192,19.246 3.664 C 18.250 2.637,16.925 1.945,15.591 1.754 C 15.216 1.701,14.236 1.675,13.890 1.709 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
