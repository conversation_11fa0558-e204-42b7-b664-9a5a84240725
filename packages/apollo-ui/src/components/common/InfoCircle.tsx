import type { ComponentProps } from "react"

export function InfoCircle(props: ComponentProps<"svg">) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.205 0.032 C 9.054 0.177,7.007 0.890,5.205 2.123 C 4.467 2.628,3.915 3.107,3.208 3.855 C 1.486 5.679,0.374 8.084,0.075 10.635 C 0.009 11.193,-0.016 12.345,0.027 12.855 C 0.261 15.665,1.331 18.124,3.191 20.127 C 5.073 22.154,7.377 23.390,10.132 23.852 C 11.131 24.019,12.750 24.033,13.710 23.882 C 15.896 23.537,17.790 22.714,19.500 21.363 C 19.819 21.111,20.696 20.262,21.078 19.836 C 22.010 18.795,22.883 17.268,23.371 15.825 C 23.616 15.101,23.838 14.122,23.929 13.370 C 23.986 12.893,23.995 11.262,23.942 10.755 C 23.862 9.982,23.637 8.964,23.373 8.183 C 22.416 5.350,20.446 2.983,17.835 1.528 C 16.216 0.625,14.427 0.106,12.733 0.046 C 11.725 0.011,11.554 0.009,11.205 0.032 M12.387 2.071 C 13.192 2.102,13.495 2.138,14.175 2.285 C 15.813 2.640,17.473 3.503,18.690 4.633 C 19.696 5.567,20.342 6.427,20.924 7.605 C 21.328 8.423,21.575 9.146,21.763 10.057 C 21.915 10.802,21.937 11.088,21.921 12.180 C 21.908 13.092,21.900 13.224,21.829 13.612 C 21.522 15.310,20.954 16.628,19.940 18.000 C 19.569 18.502,18.536 19.552,18.069 19.901 C 16.697 20.927,15.265 21.547,13.590 21.840 C 12.772 21.983,11.228 21.983,10.410 21.840 C 8.452 21.498,6.673 20.638,5.322 19.380 C 3.593 17.771,2.588 15.933,2.172 13.616 C 2.102 13.231,2.093 13.084,2.079 12.230 C 2.062 11.199,2.082 10.890,2.207 10.200 C 2.444 8.903,2.948 7.652,3.683 6.540 C 4.705 4.995,6.034 3.833,7.713 3.017 C 8.781 2.498,9.940 2.185,11.214 2.072 C 11.589 2.038,11.555 2.038,12.387 2.071 M11.730 6.040 C 11.076 6.189,10.624 6.832,10.732 7.462 C 10.863 8.224,11.627 8.736,12.319 8.525 C 13.379 8.202,13.621 6.899,12.741 6.251 C 12.465 6.048,12.063 5.964,11.730 6.040 M11.295 10.311 C 11.262 10.319,11.211 10.347,11.182 10.373 C 11.133 10.417,11.130 10.634,11.130 14.145 L 11.130 17.871 11.193 17.928 C 11.252 17.981,11.311 17.985,12.000 17.985 C 12.689 17.985,12.748 17.981,12.807 17.928 L 12.870 17.871 12.870 14.145 L 12.870 10.419 12.807 10.362 C 12.749 10.310,12.686 10.305,12.049 10.300 C 11.667 10.297,11.328 10.302,11.295 10.311 "
        fill="currentColor"
        stroke="none"
        fillRule="evenodd"
      ></path>
    </svg>
  )
}
