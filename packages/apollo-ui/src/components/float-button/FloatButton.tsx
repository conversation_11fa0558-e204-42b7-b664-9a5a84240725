import { useMemo } from "react"
import classNames from "classnames"

import { But<PERSON> } from "../button"
import styles from "./floatButton.module.css"
import type { FloatButtonProps } from "./FloatButtonProps"

export function FloatButton({
  label,
  icon,
  isExpanded,
  iconSide = "start",
  ref,
  ...buttonProps
}: FloatButtonProps) {
  const iconElement = useMemo(
    () => (
      <span className={classNames("ApolloFloatButton-icon", styles.icon)}>
        {icon}
      </span>
    ),
    [icon]
  )

  const adornmentProps =
    iconSide === "start"
      ? { startDecorator: iconElement }
      : { endDecorator: iconElement }

  return (
    <Button
      {...buttonProps}
      className={classNames(
        "ApolloFloatButton-root",
        styles.floatButton,
        styles.root,
        isExpanded ? styles.expanded : styles.collapsed,
        iconSide === "start" ? styles.iconStart : styles.iconEnd,
        buttonProps?.className
      )}
      color={buttonProps?.color ?? "primary"}
      ref={ref}
      variant={buttonProps?.variant ?? "filled"}
      {...adornmentProps}
    >
      <span className={classNames("ApolloFloatButton-label", styles.label)}>
        {label}
      </span>
    </Button>
  )
}
