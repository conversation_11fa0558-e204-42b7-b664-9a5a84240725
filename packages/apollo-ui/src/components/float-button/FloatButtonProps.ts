import type { ReactNode } from "react"

import type {
  BaseButtonProps,
  LinkButtonProps,
  NormalButtonProps,
} from "../button"

export type FloatButtonBaseProps = {
  icon: ReactNode
  label: ReactNode
  isExpanded?: boolean
  /**
   * @default 'start'
   */
  iconSide?: "start" | "end"
} & Omit<BaseButtonProps, "startDecorator" | "endDecorator">

export type FloatButtonProps = (
  | ({ href: string } & LinkButtonProps)
  | ({
      href?: never
    } & NormalButtonProps)
) &
  FloatButtonBaseProps
