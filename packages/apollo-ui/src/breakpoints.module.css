/* Screen size breakpoints similar to Tailwind CSS */
@value sm-up: (min-width: 640px);
@value md-up: (min-width: 768px);
@value lg-up: (min-width: 1024px);
@value xl-up: (min-width: 1280px);
@value 2xl-up: (min-width: 1536px);

/* Common device category breakpoints */
@value mobile: (max-width: 639px);
@value tablet: (min-width: 640px) and (max-width: 1023px);
@value desktop-up: (min-width: 1024px);

/* Max-width breakpoints for targeting specific ranges */
@value xs-only: (max-width: 639px);
@value sm-only: (min-width: 640px) and (max-width: 767px);
@value md-only: (min-width: 768px) and (max-width: 1023px);
@value lg-only: (min-width: 1024px) and (max-width: 1279px);
@value xl-only: (min-width: 1280px) and (max-width: 1535px);
@value 2xl-only: (min-width: 1536px);

/* Max-width breakpoints */
@value sm-down: (max-width: 767px);
@value md-down: (max-width: 1023px);
@value lg-down: (max-width: 1279px);
@value xl-down: (max-width: 1535px);