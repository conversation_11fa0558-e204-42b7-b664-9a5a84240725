import React from "react"
import { Smile } from "@design-systems/apollo-icons"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import {
  FloatButton,
  type FloatButtonProps,
} from "../src/components/float-button"

const MockIcon = () => <Smile />

describe("FloatButton component", () => {
  const getComponent = (props: FloatButtonProps) => (
    <ThemeProvider>
      <FloatButton {...props} />
    </ThemeProvider>
  )

  test("renders a float button with default props", () => {
    const { container } = render(
      getComponent({ label: "Test", icon: <MockIcon /> })
    )
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/ApolloFloatButton-root/)
  })

  test("renders a float button with custom icon side", () => {
    const { container } = render(
      getComponent({ label: "Test", icon: <MockIcon />, iconSide: "end" })
    )
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/iconEnd/)
  })

  test("renders a disabled float button", () => {
    const { container } = render(
      getComponent({ label: "Test", icon: <MockIcon />, disabled: true })
    )
    const button = container.querySelector("button")
    expect(button).toBeDisabled()
  })

  test("handles click events", () => {
    let clicked = false
    const handleClick = () => {
      clicked = true
    }
    const { container } = render(
      getComponent({ label: "Test", icon: <MockIcon />, onClick: handleClick })
    )
    const button = container.querySelector("button")
    button?.click()
    expect(clicked).toBe(true)
  })

  test("renders an expanded float button", () => {
    const { container } = render(
      getComponent({ label: "Test", icon: <MockIcon />, isExpanded: true })
    )
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/expanded/)
  })
})
