import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import "@vitest/browser/matchers.d.ts"

import { ThemeProvider } from "../src"
import { Typography, type TypographyProps } from "../src/components/typography"

describe("Typography component", () => {
  const getComponent = (props?: TypographyProps) => (
    <ThemeProvider>
      <Typography {...props} />
    </ThemeProvider>
  )

  test("renders a Typography with default props", () => {
    const { container } = render(
      getComponent({ children: "Default Typography" })
    )
    const element = container.querySelector("p")
    expect(element).toBeInTheDocument()
    expect(element).toHaveProperty("tagName", "P")
  })

  test("renders a Typography with custom level", () => {
    const { container } = render(
      getComponent({ level: "h1", children: "Heading 1" })
    )
    const element = container.querySelector("h1")
    expect(element).toBeInTheDocument()
    expect(element).toHaveProperty("tagName", "H1")
  })

  test("renders a Typography with custom alignment", () => {
    const { container } = render(
      getComponent({ align: "center", children: "Centered Text" })
    )
    const element = container.querySelector("p")
    expect(element).toHaveStyle("text-align: center")
  })

  test("renders a Typography with custom color", () => {
    const { container } = render(
      getComponent({ color: "primary", children: "Colored Text" })
    )
    const element = container.querySelector("p")
    expect(element).toBeInTheDocument()
    expect(element?.className).toMatch(/colorPrimary/)
  })

  test("renders a Typography with gutterBottom", () => {
    const { container } = render(
      getComponent({ gutterBottom: true, children: "Text with Gutter Bottom" })
    )
    const element = container.querySelector("p")
    expect(element).toBeInTheDocument()
    expect(element?.className).toMatch(/gutterBottom/)
  })

  test("renders a Typography with noWrap", () => {
    const { container } = render(
      getComponent({ noWrap: true, children: "No Wrap Text" })
    )
    const element = container.querySelector("p")
    expect(element).toBeInTheDocument()
    expect(element?.className).toMatch(/noWrap/)
  })

  test("applies custom styles", () => {
    const { container } = render(
      getComponent({ style: { fontSize: "20px" }, children: "Custom Style" })
    )
    const element = container.querySelector("p")
    expect(element).toHaveStyle("font-size: 20px")
  })

  test("applies custom className", () => {
    const { container } = render(
      getComponent({ className: "custom-class", children: "Custom Class" })
    )
    const element = container.querySelector("p")
    expect(element).toBeInTheDocument()
    expect(element?.className).toMatch(/custom-class/)
  })

  test("renders a Typography with textlink level and href", () => {
    const { container } = render(
      getComponent({ level: "textlink", href: "#", children: "Text Link" })
    )
    const element = container.querySelector("a")
    expect(element).toBeInTheDocument()
    expect(element).toHaveProperty("tagName", "A")
    expect(element).toHaveAttribute("href", "#")
  })
})
