import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Button, type ButtonProps } from "../src/components/button"

describe("Button component", () => {
  const getComponent = (props?: ButtonProps) => (
    <ThemeProvider>
      <Button {...props} />
    </ThemeProvider>
  )

  test("renders a button with default props", () => {
    const { container } = render(getComponent({ children: "Click me" }))
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/buttonRoot/)
    expect(button?.className).toMatch(/buttonFilled/)
    expect(button?.className).toMatch(/buttonLarge/)
    expect(button?.className).toMatch(/buttonPrimary/)
  })

  test("renders a button with custom props", () => {
    const { container } = render(
      getComponent({
        variant: "outline",
        size: "small",
        color: "negative",
        children: "Click me",
      })
    )
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/buttonOutline/)
    expect(button?.className).toMatch(/buttonSmall/)
    expect(button?.className).toMatch(/buttonNegative/)
  })

  test("renders a full-width button", () => {
    const { container } = render(
      getComponent({ fullWidth: true, children: "Click me" })
    )
    const button = container.querySelector("button")
    expect(button?.className).toMatch(/buttonFullWidth/)
  })

  test("renders a button with start and end decorators", () => {
    const { container } = render(
      getComponent({
        startDecorator: <span>Start</span>,
        endDecorator: <span>End</span>,
        children: "Click me",
      })
    )
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.innerHTML).toContain("<span>Start</span>")
    expect(button?.innerHTML).toContain("<span>End</span>")
  })

  test("renders an anchor element when href is provided", () => {
    const { container } = render(
      getComponent({ href: "https://example.com", children: "Link" })
    )
    const link = container.querySelector("a")
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute("href", "https://example.com")
  })

  test("handles click events", () => {
    let clicked = false
    const handleClick = () => {
      clicked = true
    }
    const { container } = render(
      getComponent({ onClick: handleClick, children: "Click me" })
    )
    const button = container.querySelector("button")
    button?.click()
    expect(clicked).toBe(true)
  })

  test("is disabled when the disabled prop is true", () => {
    const { container } = render(
      getComponent({ disabled: true, children: "Click me" })
    )
    const button = container.querySelector("button")
    expect(button).toBeDisabled()
  })
})
