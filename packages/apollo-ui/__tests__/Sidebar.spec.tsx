import React from "react"
import { Home, User } from "@design-systems/apollo-icons"
import Settings from "react-multi-date-picker/plugins/settings"
import { describe, expect, test, vi } from "vitest"
import { fireEvent, render, screen } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Sidebar, type SidebarProps } from "../src/components/sidebar"

describe("Sidebar component", () => {
  const defaultMenus = [
    {
      key: "menu1",
      label: "Menu Section",
      items: [
        {
          key: "home",
          label: "Home",
          icon: <Home />,
        },
        {
          key: "settings",
          label: "Settings",
          icon: <Settings />,
          children: [
            {
              key: "profile",
              label: "Profile Settings",
            },
            {
              key: "account",
              label: "Account Settings",
            },
          ],
        },
      ],
    },
    {
      key: "menu2",
      items: [
        {
          key: "user",
          label: "User",
          icon: <User />,
        },
      ],
    },
  ]

  const getComponent = (props?: Partial<SidebarProps>) => (
    <ThemeProvider>
      <Sidebar
        menus={defaultMenus}
        title="Test Sidebar"
        width="240px"
        {...props}
      />
    </ThemeProvider>
  )

  test("renders a sidebar with default props", () => {
    const { container } = render(getComponent())

    expect(screen.getByText("Test Sidebar")).toBeInTheDocument()
    expect(screen.getByText("Menu Section")).toBeInTheDocument()
    expect(screen.getByText("Home")).toBeInTheDocument()
    expect(screen.getByText("Settings")).toBeInTheDocument()
    expect(screen.getByText("User")).toBeInTheDocument()
  })

  test("renders a collapsible sidebar and toggles collapsed state", () => {
    const onCollapsedChange = vi.fn()
    const { container } = render(
      getComponent({
        collapsible: true,
        onCollapsedChange,
      })
    )

    const closeButton = container
      .querySelector("button svg[data-testid='Close']")
      ?.closest("button")
    expect(closeButton).toBeInTheDocument()

    fireEvent.click(closeButton!)
    expect(onCollapsedChange).toHaveBeenCalledWith(true)

    // Simulate collapsed state
    render(
      getComponent({
        collapsible: true,
        collapsed: true,
      })
    )

    // Title should not be visible in collapsed state
    expect(screen.queryByText("Test Sidebar")).not.toBeInTheDocument()
  })

  test("calls onSelectMenu when a menu item is clicked", () => {
    const onSelectMenu = vi.fn()
    const { container } = render(
      getComponent({
        onSelectMenu,
      })
    )

    const homeMenuItem = screen.getByText("Home")
    fireEvent.click(homeMenuItem)
    expect(onSelectMenu).toHaveBeenCalledWith("home", null)
  })

  test("renders a custom header", () => {
    render(
      getComponent({
        header: <div data-testid="custom-header">Custom Header</div>,
      })
    )

    expect(screen.getByTestId("custom-header")).toBeInTheDocument()
    expect(screen.getByText("Custom Header")).toBeInTheDocument()
  })

  test("renders a footer and logout button", () => {
    const onLogOut = vi.fn()
    render(
      getComponent({
        footer: [
          {
            key: "footer1",
            label: "Footer Section",
            items: [
              {
                key: "footerItem",
                label: "Footer Item",
              },
            ],
          },
        ],
        onLogOut,
        logOutButtonLabel: "Sign Out",
      })
    )

    expect(screen.getByText("Footer Section")).toBeInTheDocument()
    expect(screen.getByText("Footer Item")).toBeInTheDocument()
    expect(screen.getByText("Sign Out")).toBeInTheDocument()

    fireEvent.click(screen.getByText("Sign Out"))
    expect(onLogOut).toHaveBeenCalled()
  })
})
