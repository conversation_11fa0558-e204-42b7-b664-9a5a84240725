import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { RadioGroup, type RadioGroupProps } from "../src/components/radio"
import { Radio } from "../src/components/radio/Radio"

describe("RadioGroup component", () => {
  const getComponent = (props?: RadioGroupProps) => (
    <ThemeProvider>
      <RadioGroup {...props}>
        <Radio value="1">Option 1</Radio>
        <Radio value="2">Option 2</Radio>
      </RadioGroup>
    </ThemeProvider>
  )

  test("renders a radio group with default props", () => {
    const { container } = render(getComponent())
    const group = container.querySelector(".ApolloRadioGroup-root")
    expect(group).toBeInTheDocument()
  })

  test("renders radio buttons inside the group", () => {
    const { container } = render(getComponent())
    const radios = container.querySelectorAll(".ApolloRadio-root")
    expect(radios).toHaveLength(2)
  })

  test("applies custom className", () => {
    const { container } = render(getComponent({ className: "custom-class" }))
    const group = container.querySelector(".ApolloRadioGroup-root")
    expect(group?.className).toContain("custom-class")
  })

  test("renders with horizontal direction", () => {
    const { container } = render(getComponent({ direction: "horizontal" }))
    const group = container.querySelector(".ApolloRadioGroup-root")
    expect(group?.className).toContain("radioGroupHorizontal")
  })
})
