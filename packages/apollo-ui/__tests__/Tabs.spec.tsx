import React from "react"
import { userEvent } from "@vitest/browser/context"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Tabs } from "../src/components/tabs"

describe("Tabs components", () => {
  // Test Tabs.Root
  describe("TabsRoot component", () => {
    test("renders with default props", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <div>Tabs content</div>
          </Tabs.Root>
        </ThemeProvider>
      )

      const root = container.querySelector(".ApolloTabs-root")
      expect(root).toBeInTheDocument()
    })

    test("applies fullWidth class when fullWidth prop is true", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1" fullWidth>
            <div>Tabs content</div>
          </Tabs.Root>
        </ThemeProvider>
      )

      const root = container.querySelector(".ApolloTabs-root")
      expect(root?.className).toContain("tabsRootFullWidth")
    })

    test("applies custom className", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1" className="custom-class">
            <div>Tabs content</div>
          </Tabs.Root>
        </ThemeProvider>
      )

      const root = container.querySelector(".ApolloTabs-root")
      expect(root?.className).toContain("custom-class")
    })
  })

  // Test Tabs.List
  describe("TabsList component", () => {
    test("renders correctly", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const list = container.querySelector(".ApolloTabs-list")
      expect(list).toBeInTheDocument()
    })

    test("applies custom className", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List className="custom-list-class">
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const list = container.querySelector(".ApolloTabs-list")
      expect(list?.className).toContain("custom-list-class")
    })
  })

  // Test Tabs.Tab
  describe("Tab component", () => {
    test("renders correctly", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const tabs = container.querySelectorAll(".ApolloTabs-tab")
      expect(tabs).toHaveLength(2)
      expect(tabs[0].textContent).toBe("Tab 1")
      expect(tabs[1].textContent).toBe("Tab 2")
    })

    test("applies fitContent class when fitContent prop is true", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1" fitContent>
                Tab 1
              </Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const tab = container.querySelector(".ApolloTabs-tab")
      expect(tab?.className).toContain("tabsFitContent")
    })

    test("applies alignment classes based on align prop", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1" align="left">
                Left
              </Tabs.Tab>
              <Tabs.Tab value="tab2" align="center">
                Center
              </Tabs.Tab>
              <Tabs.Tab value="tab3" align="right">
                Right
              </Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const tabs = container.querySelectorAll(".ApolloTabs-tab")
      expect(tabs[0].className).toContain("tabAlignLeft")
      expect(tabs[1].className).not.toContain("tabAlignLeft")
      expect(tabs[1].className).not.toContain("tabAlignRight")
      expect(tabs[2].className).toContain("tabAlignRight")
    })

    test("applies custom className", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1" className="custom-tab-class">
                Tab 1
              </Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const tab = container.querySelector(".ApolloTabs-tab")
      expect(tab?.className).toContain("custom-tab-class")
    })

    test("handles tab selection", async () => {
      const onValueChange = vi.fn()
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1" onValueChange={onValueChange}>
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const tabs = container.querySelectorAll(".ApolloTabs-tab")
      await userEvent.click(tabs[1])
      expect(onValueChange).toHaveBeenCalledWith("tab2", expect.anything())
    })
  })

  describe("TabsIndicator component", () => {
    test("renders correctly", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const indicator = container.querySelector(".ApolloTabs-indicator")
      expect(indicator).toBeInTheDocument()
    })

    test("applies custom className", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Indicator className="custom-indicator-class" />
            </Tabs.List>
          </Tabs.Root>
        </ThemeProvider>
      )

      const indicator = container.querySelector(".ApolloTabs-indicator")
      expect(indicator?.className).toContain("custom-indicator-class")
    })
  })

  // Test Tabs.Panel
  describe("TabsPanel component", () => {
    test("renders correctly", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
            </Tabs.List>
            <Tabs.Panel value="tab1">Panel 1 Content</Tabs.Panel>
          </Tabs.Root>
        </ThemeProvider>
      )

      const panel = container.querySelector(".ApolloTabs-panel")
      expect(panel).toBeInTheDocument()
      expect(panel?.textContent).toBe("Panel 1 Content")
    })

    test("applies custom className", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
            </Tabs.List>
            <Tabs.Panel value="tab1" className="custom-panel-class">
              Content
            </Tabs.Panel>
          </Tabs.Root>
        </ThemeProvider>
      )

      const panel = container.querySelector(".ApolloTabs-panel")
      expect(panel?.className).toContain("custom-panel-class")
    })
  })

  // Test complete Tabs functionality
  describe("Complete Tabs integration", () => {
    test("renders full tabs component and switches between panels", async () => {
      const { getByText } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1">
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="tab1">Panel 1 Content</Tabs.Panel>
            <Tabs.Panel value="tab2">Panel 2 Content</Tabs.Panel>
          </Tabs.Root>
        </ThemeProvider>
      )

      // Initially, panel 1 should be visible
      expect(getByText("Panel 1 Content").element()).toBeInTheDocument()

      // Click on tab 2
      const tab2 = getByText("Tab 2")
      await userEvent.click(tab2)

      // Now panel 2 should be visible
      expect(getByText("Panel 2 Content").element()).toBeInTheDocument()
    })

    test("renders tabs with fullWidth", () => {
      const { container } = render(
        <ThemeProvider>
          <Tabs.Root defaultValue="tab1" fullWidth>
            <Tabs.List>
              <Tabs.Tab value="tab1">Tab 1</Tabs.Tab>
              <Tabs.Tab value="tab2">Tab 2</Tabs.Tab>
              <Tabs.Indicator />
            </Tabs.List>
            <Tabs.Panel value="tab1">Panel 1 Content</Tabs.Panel>
            <Tabs.Panel value="tab2">Panel 2 Content</Tabs.Panel>
          </Tabs.Root>
        </ThemeProvider>
      )

      const root = container.querySelector(".ApolloTabs-root")
      expect(root?.className).toContain("tabsRootFullWidth")
    })
  })
})
