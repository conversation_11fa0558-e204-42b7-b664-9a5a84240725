import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import {
  ProductCard,
  type ProductCardProps,
} from "../src/components/product-card"

describe("ProductCard component", () => {
  const getComponent = (props?: Partial<ProductCardProps>) => (
    <ThemeProvider>
      <ProductCard title="Product Label" {...props} />
    </ThemeProvider>
  )

  test("renders a product card with default props", () => {
    const { container } = render(getComponent())
    const article = container.querySelector("article")
    expect(article).toBeInTheDocument()
    expect(article?.className).toContain("ApolloProductCard-root")
  })

  test("renders the image when imageSrc is provided", () => {
    const { container } = render(getComponent({ imageSrc: "test-image.jpg" }))
    const img = container.querySelector("img")
    expect(img).toBeInTheDocument()
    expect(img).toHaveAttribute("src", "test-image.jpg")
  })

  test("renders the default noImageBox when no imageSrc is provided", () => {
    const { container } = render(getComponent())
    const noImageBox = container.querySelector(".ApolloProductCard-noImageBox")
    expect(noImageBox).toBeInTheDocument()
  })

  test("renders the image overlay when imageOverlay is provided as string", () => {
    const { container } = render(
      getComponent({
        imageSrc: "test-image.jpg",
        imageOverlay: "Overlay Text",
      })
    )
    expect(container.textContent).toContain("Overlay Text")
  })

  test("renders the custom image overlay when imageOverlay is provided as JSX", () => {
    const { container } = render(
      getComponent({
        imageSrc: "test-image.jpg",
        imageOverlay: <div>Custom Overlay</div>,
      })
    )
    expect(container.textContent).toContain("Custom Overlay")
  })

  test("renders the title when title prop is provided", () => {
    const { container } = render(getComponent({ title: "Product Title" }))
    expect(container.textContent).toContain("Product Title")
  })

  test("renders the body when body prop is provided", () => {
    const { container } = render(
      getComponent({ body: <div>Product Body</div> })
    )
    expect(container.textContent).toContain("Product Body")
  })

  test("renders the footer when footer prop is provided", () => {
    const { container } = render(
      getComponent({ footer: <div>Product Footer</div> })
    )
    expect(container.textContent).toContain("Product Footer")
  })

  test("renders the product card with custom size", () => {
    const { container } = render(getComponent({ size: "300px" }))
    const article = container.querySelector("article")
    expect(article).toHaveStyle("width: 300px")
    expect(article).toHaveStyle("min-width: 300px")
  })

  test("renders the product card with responsive size when size is set to fill", () => {
    const { container } = render(getComponent({ size: "fill" }))
    const article = container.querySelector("article")
    expect(article?.className).toContain("fillSize")
  })

  test("renders the extra content when extra prop is provided", () => {
    const { container } = render(
      getComponent({ extra: <div>Extra Content</div> })
    )
    expect(container.textContent).toContain("Extra Content")
  })
})
