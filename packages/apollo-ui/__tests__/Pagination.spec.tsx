import React from "react"
import { userEvent } from "@vitest/browser/context"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Pagination } from "../src/components/pagination"

describe("Pagination component", () => {
  const getComponent = (props = {}) => (
    <ThemeProvider>
      <Pagination {...props} />
    </ThemeProvider>
  )

  test("renders a pagination with default props", () => {
    const { container } = render(getComponent())
    const pagination = container.querySelector("nav")
    expect(pagination).toBeInTheDocument()
    expect(pagination?.getAttribute("class")).toMatch(/paginationRoot/)
  })

  test("renders correct number of pages", () => {
    const { container } = render(getComponent({ count: 5 }))
    const paginationItems = container.querySelectorAll(
      "[class*='paginationItem']"
    )
    expect(paginationItems.length).toBe(5)
  })

  test("handles page changes", async () => {
    const onChange = vi.fn()
    const { container } = render(getComponent({ count: 10, onChange }))
    const paginationItems = container.querySelectorAll(
      "[class*='paginationItem']"
    )

    // Click on page 3
    await userEvent.click(paginationItems[2])
    expect(onChange).toHaveBeenCalledWith(expect.anything(), 3)
  })

  test("respects controlled page prop", () => {
    const { container } = render(getComponent({ count: 10, page: 5 }))
    const selectedPage = container.querySelector(
      "[class*='paginationItemSelected']"
    )
    expect(selectedPage).toHaveTextContent("5")
  })

  test("handles previous and next buttons", async () => {
    const onChange = vi.fn()
    const { container } = render(getComponent({ count: 10, page: 5, onChange }))

    const prevButton = container.querySelector('[name="prev-button"]')
    const nextButton = container.querySelector('[name="next-button"]')

    await userEvent.click(prevButton!)
    expect(onChange).toHaveBeenCalledWith(expect.anything(), 4)

    await userEvent.click(nextButton!)
    expect(onChange).toHaveBeenCalledWith(expect.anything(), 6)
  })

  test("disables previous button on first page", () => {
    const { container } = render(getComponent({ count: 10, page: 1 }))
    const prevButton = container.querySelector('[name="prev-button"]')
    expect(prevButton).toBeDisabled()
  })

  test("disables next button on last page", () => {
    const { container } = render(getComponent({ count: 10, page: 10 }))
    const nextButton = container.querySelector('[name="next-button"]')
    expect(nextButton).toBeDisabled()
  })

  test("can hide previous and next buttons", () => {
    const { container } = render(
      getComponent({
        count: 10,
        showPrevPageButton: false,
        showNextPageButtonButton: false,
      })
    )

    const prevButton = container.querySelector('[name="prev-button"]')
    const nextButton = container.querySelector('[name="next-button"]')

    expect(prevButton).not.toBeInTheDocument()
    expect(nextButton).not.toBeInTheDocument()
  })

  test("can disable the entire pagination", () => {
    const { container } = render(getComponent({ count: 5, disabled: true }))
    const paginationItems = container.querySelectorAll(
      "[class*='paginationItem']"
    )

    paginationItems.forEach((item) => {
      expect(item).toBeDisabled()
    })

    const prevButton = container.querySelector('[name="prev-button"]')
    const nextButton = container.querySelector('[name="next-button"]')

    expect(prevButton).toBeDisabled()
    expect(nextButton).toBeDisabled()
  })
})
