import React from "react"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { CapsuleTab, type CapsuleTabProps } from "../src/components/capsule-tab"

describe("CapsuleTab component", () => {
  const getComponent = (props: CapsuleTabProps) => (
    <ThemeProvider>
      <CapsuleTab {...props} />
    </ThemeProvider>
  )

  test("renders CapsuleTab with default props", () => {
    const { container } = render(
      getComponent({
        tabs: [
          { id: "tab1", label: "Tab 1" },
          { id: "tab2", label: "Tab 2" },
        ],
        selectedIndex: 0,
        onSelect: () => {},
      })
    )
    const root = container.querySelector(".ApolloCapsuleTab-root")
    expect(root).toBeInTheDocument()
  })

  test("renders tabs correctly", () => {
    const { container } = render(
      getComponent({
        tabs: [
          { id: "tab1", label: "Tab 1" },
          { id: "tab2", label: "Tab 2" },
        ],
        selectedIndex: 0,
        onSelect: () => {},
      })
    )
    const tabs = container.querySelectorAll(".ApolloCapsuleTab-item")
    expect(tabs).toHaveLength(2)
    expect(tabs[0].textContent).toBe("Tab 1")
    expect(tabs[1].textContent).toBe("Tab 2")
  })

  test("applies selected class to the selected tab", () => {
    const { container } = render(
      getComponent({
        tabs: [
          { id: "tab1", label: "Tab 1" },
          { id: "tab2", label: "Tab 2" },
        ],
        selectedIndex: 1,
        onSelect: () => {},
      })
    )
    const selectedTab = container.querySelector(
      ".ApolloCapsuleTab-itemSelected"
    )
    expect(selectedTab).toBeInTheDocument()
    expect(selectedTab?.textContent).toBe("Tab 2")
  })

  test("calls onSelect when a tab is clicked", () => {
    const handleSelect = vi.fn()
    const { container } = render(
      getComponent({
        tabs: [
          { id: "tab1", label: "Tab 1" },
          { id: "tab2", label: "Tab 2" },
        ],
        selectedIndex: 0,
        onSelect: handleSelect,
      })
    )
    const secondTab = container.querySelectorAll(".ApolloCapsuleTab-item")[1]
    secondTab?.dispatchEvent(new MouseEvent("click", { bubbles: true }))
    expect(handleSelect).toHaveBeenCalledWith(1)
  })

  test("renders custom className", () => {
    const { container } = render(
      getComponent({
        tabs: [
          { id: "tab1", label: "Tab 1" },
          { id: "tab2", label: "Tab 2" },
        ],
        selectedIndex: 0,
        onSelect: () => {},
        className: "custom-class",
      })
    )
    const root = container.querySelector(".ApolloCapsuleTab-root")
    expect(root?.className).toContain("custom-class")
  })
})
