import React from "react"
import { userEvent } from "@vitest/browser/context"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Select, type SelectProps } from "../src/components/select"

describe("Select Component", () => {
  const mockOptions = [
    { label: "Option 1", value: "1" },
    { label: "Option 2", value: "2" },
    { label: "Option 3", value: "3" },
  ]

  const getComponent = (props?: Partial<SelectProps<string>>) => (
    <ThemeProvider>
      <Select {...props}>
        {mockOptions.map((option) => (
          <Select.Option
            key={option.value}
            label={option.label}
            value={option.value}
          />
        ))}
      </Select>
    </ThemeProvider>
  )

  const getPortalContainer = () => document.querySelector(".ApolloSelect-popup")

  const waitForOptionsToRender = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(getPortalContainer())
      }, 100) // Wait for 100ms to ensure options are rendered
    })
  }

  test("renders select component correctly", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const select = container.querySelector(".ApolloSelect-trigger")
    expect(select).toBeInTheDocument()
  })

  test("renders the label correctly", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const label = container.querySelector(".ApolloField-label")
    expect(label).toBeInTheDocument()
    expect(label?.textContent).toBe("Test Label")
  })

  test("opens dropdown on click", async () => {
    const { container } = render(getComponent())
    const trigger = container.querySelector(
      ".ApolloSelect-trigger"
    ) as HTMLElement

    trigger.click()

    const portalContainer = (await waitForOptionsToRender()) as HTMLElement
    const options = portalContainer.querySelectorAll(".ApolloSelect-option")
    expect(options).toHaveLength(mockOptions.length)
  })

  test("selects an option", async () => {
    const onChange = vi.fn()
    const { container } = render(getComponent({ onChange }))

    const trigger = container.querySelector(
      ".ApolloSelect-trigger"
    ) as HTMLElement
    await userEvent.click(trigger)

    const portalContainer = (await waitForOptionsToRender()) as HTMLElement
    const option = portalContainer.querySelector(
      ".ApolloSelect-option"
    ) as HTMLElement
    await userEvent.click(option)

    expect(onChange).toHaveBeenCalledWith("1")
  })

  test("displays placeholder text", () => {
    const { container } = render(
      getComponent({ placeholder: "Select an option" })
    )
    const inputElement = container.querySelector("input")
    expect(inputElement).toHaveAttribute("placeholder", "Select an option")
  })

  test("renders in disabled state", () => {
    const { container } = render(getComponent({ disabled: true }))
    const input = container.querySelector("input")
    expect(input).toBeDisabled()
  })

  test("shows error state when error prop is set", () => {
    const { container } = render(getComponent({ error: true }))
    const inputRoot = container.querySelector(".ApolloInput-controlRoot")
    expect(inputRoot?.className).toMatch(/inputRootError/)
  })

  test("renders helper text when provided", () => {
    const helperText = "Helper text"
    const { container } = render(getComponent({ helperText }))
    const helperTextElement = container.querySelector(".ApolloField-helperText")
    expect(helperTextElement).toBeInTheDocument()
    expect(helperTextElement).toHaveTextContent(helperText)
  })

  test("renders full width when fullWidth prop is set", () => {
    const { container } = render(getComponent({ fullWidth: true }))
    const fieldRoot = container.querySelector(".ApolloSelect-triggerContainer")
    expect(fieldRoot?.className).toMatch(/selectRootFullWidth/)
  })

  test("handles required prop correctly", () => {
    const { container } = render(
      getComponent({ label: "Test Label", required: true })
    )
    const label = container.querySelector("label")
    expect(label).toBeInTheDocument()
    expect(label?.innerHTML).toContain("*")
  })

  test("handles custom field props", () => {
    const { container } = render(
      getComponent({
        fieldProps: { className: "custom-field-class" },
      })
    )
    const fieldRoot = container.querySelector(".ApolloSelect-fieldRoot")
    expect(fieldRoot?.className).toContain("custom-field-class")
  })

  test("renders selected value in input", async () => {
    const { container } = render(getComponent({ defaultValue: "2" }))

    // Wait for the value to be properly set in the input
    await new Promise((resolve) => setTimeout(resolve, 50))

    const input = container.querySelector("input") as HTMLInputElement
    expect(input.value).toBe("Option 2")
  })

  test("renders end decorator with chevron icon", () => {
    const { container } = render(getComponent())
    const endDecorator = container.querySelector(".ApolloInput-endDecorator")
    expect(endDecorator).toBeInTheDocument()
    expect(endDecorator?.querySelector("svg")).toBeInTheDocument()
  })
})
