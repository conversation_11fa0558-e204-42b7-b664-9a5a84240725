import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import {
  SortingIcon,
  type SortingIconProps,
} from "../src/components/sorting-icon"

describe("SortingIcon component", () => {
  const getComponent = (
    props?: SortingIconProps & { "data-testid"?: string }
  ) => (
    <ThemeProvider>
      <SortingIcon {...props} />
    </ThemeProvider>
  )

  test("renders a sorting icon with default status", () => {
    const { container } = render(getComponent())

    // Check if the component root element is rendered
    const iconRoot = container.querySelector(".ApolloSortingIcon-root")
    expect(iconRoot).toBeInTheDocument()

    // Check if both icons are rendered
    const ascIcon = container.querySelector(".ApolloSortingIcon-iconASC")
    const descIcon = container.querySelector(".ApolloSortingIcon-iconDESC")
    expect(ascIcon).toBeInTheDocument()
    expect(descIcon).toBeInTheDocument()

    // Verify neither icon has the active class in default state
    expect(ascIcon?.getAttribute("class")).not.toMatch("sortingIconActive")
    expect(descIcon?.getAttribute("class")).not.toMatch("sortingIconActive")
  })

  test("highlights the ascending icon when status is 'asc'", () => {
    const { container } = render(getComponent({ status: "asc" }))

    const ascIcon = container.querySelector(".ApolloSortingIcon-iconASC")
    const descIcon = container.querySelector(".ApolloSortingIcon-iconDESC")

    // Verify the ascending icon has the active class
    expect(ascIcon?.getAttribute("class")).toMatch("sortingIconActive")
    // Verify the descending icon does not have the active class
    expect(descIcon?.getAttribute("class")).not.toMatch("sortingIconActive")
  })

  test("highlights the descending icon when status is 'desc'", () => {
    const { container } = render(getComponent({ status: "desc" }))

    const ascIcon = container.querySelector(".ApolloSortingIcon-iconASC")
    const descIcon = container.querySelector(".ApolloSortingIcon-iconDESC")

    // Verify the descending icon has the active class
    expect(descIcon?.getAttribute("class")).toMatch("sortingIconActive")
    // Verify the ascending icon does not have the active class
    expect(ascIcon?.getAttribute("class")).not.toMatch("sortingIconActive")
  })

  test("applies custom className to the root element", () => {
    const testClass = "custom-class"
    const { container } = render(getComponent({ className: testClass }))

    const iconRoot = container.querySelector(".ApolloSortingIcon-root")
    expect(iconRoot?.getAttribute("class")).toContain(testClass)
  })

  test("applies additional HTML attributes to the span element", () => {
    const { container } = render(
      getComponent({
        "data-testid": "sorting-icon",
        title: "Sort column",
      })
    )

    const iconRoot = container.querySelector("span")
    expect(iconRoot).toHaveAttribute("data-testid", "sorting-icon")
    expect(iconRoot).toHaveAttribute("title", "Sort column")
  })

  test("renders both CaretUp and CaretDown icons with proper aria-labels", () => {
    const { container } = render(getComponent())

    const ascIcon = container.querySelector('svg[aria-label="ascending"]')
    const descIcon = container.querySelector('svg[aria-label="descending"]')

    expect(ascIcon).toBeInTheDocument()
    expect(descIcon).toBeInTheDocument()
  })
})
