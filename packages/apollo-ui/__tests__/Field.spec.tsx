import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Field, type FieldProps } from "../src/components/field"

describe("Field Component", () => {
  const getComponent = (props?: FieldProps) => (
    <ThemeProvider>
      <Field {...props} />
    </ThemeProvider>
  )

  test("should render the field component correctly", () => {
    const { container } = render(
      getComponent({
        label: "Test Label",
        children: <input type="text" />,
      })
    )
    const field = container.querySelector(".ApolloField-root")
    const input = container.querySelector("input")
    expect(field).toBeInTheDocument()
    expect(input).toBeInTheDocument()
  })

  test("should display the label correctly", () => {
    const { container } = render(
      getComponent({
        label: "Test Label",
        children: <input type="text" />,
      })
    )
    const label = container.querySelector(".ApolloField-label")
    expect(label).toBeInTheDocument()
    expect(label!.textContent).toBe("Test Label")
  })

  test("should display the helper text correctly", () => {
    const { container } = render(
      getComponent({
        helperText: "Helper text",
        children: <input type="text" />,
      })
    )
    const helperText = container.querySelector(".ApolloField-helperText")
    expect(helperText).toBeInTheDocument()
    expect(helperText!.textContent).toBe("Helper text")
  })

  test("should show error state when the error prop is set", () => {
    const { container } = render(
      getComponent({
        error: true,
        helperText: "Error text",
        children: <input type="text" />,
      })
    )
    const errorText = container.querySelector(".ApolloField-errorMessage")
    expect(errorText).toBeInTheDocument()
    expect(errorText!.textContent).toBe("Error text")
  })

  test("should handle required prop correctly", () => {
    const { container } = render(
      getComponent({
        label: "Test Label",
        required: true,
        children: <input type="text" />,
      })
    )
    const label = container.querySelector(".ApolloField-label")
    expect(label).toBeInTheDocument()
    expect(label!.innerHTML).toContain("*")
  })

  test("should handle custom class names", () => {
    const { container } = render(
      getComponent({
        className: "custom-class",
        children: <input type="text" />,
      })
    )
    const fieldRoot = container.querySelector(".ApolloField-root")
    expect(fieldRoot!.className).toMatch(/custom-class/)
  })
})
