import React from "react"
import { userEvent } from "@vitest/browser/context"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Switch, type SwitchProps } from "../src/components/switch"

describe("Switch Component", () => {
  const getComponent = (props: SwitchProps) => (
    <ThemeProvider>
      <Switch {...props} />
    </ThemeProvider>
  )

  test("renders the switch component correctly", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const switchElement = container.querySelector('button[role="switch"]')
    expect(switchElement).toBeInTheDocument()
  })

  test("renders the label correctly", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const label = container.querySelector(".ApolloField-label")
    expect(label).toBeInTheDocument()
    expect(label?.textContent).toBe("Test Label")
  })

  test("handles onCheckedChange events", async () => {
    const handleCheckedChange = vi.fn()
    const { container } = render(
      getComponent({ onCheckedChange: handleCheckedChange })
    )
    const switchElement = container.querySelector(
      'button[role="switch"]'
    ) as HTMLButtonElement
    await userEvent.click(switchElement)
    expect(handleCheckedChange).toHaveBeenCalledWith(true, expect.any(Object))
  })

  test("renders disabled switch", () => {
    const { container } = render(getComponent({ disabled: true }))
    const switchElement = container.querySelector('button[role="switch"]')
    expect(switchElement).toHaveAttribute("disabled")
  })

  test("renders actionText correctly", () => {
    const { container } = render(
      getComponent({ actionText: "Action Text", disabled: false })
    )
    const actionText = container.querySelector(".ApolloSwitch-actionText")
    expect(actionText).toBeInTheDocument()
    expect(actionText?.textContent).toBe("Action Text")
  })

  test("disables actionText when switch is disabled", () => {
    const { container } = render(
      getComponent({ actionText: "Action Text", disabled: true })
    )
    const actionText = container.querySelector(".ApolloSwitch-actionText")
    expect(actionText?.className).toMatch(/actionTextDisabled/)
  })
})
