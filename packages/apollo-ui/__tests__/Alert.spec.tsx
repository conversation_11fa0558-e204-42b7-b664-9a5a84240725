import React from "react"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Alert, type AlertProps } from "../src/components/alert"

describe("Alert component", () => {
  const getComponent = (props?: AlertProps) => (
    <ThemeProvider>
      <Alert title="Alert Title" description="Alert Description" {...props} />
    </ThemeProvider>
  )

  test("renders with default props", () => {
    const { container } = render(getComponent())
    const title = container.querySelector(".ApolloAlert-title")
    const description = container.querySelector(".ApolloAlert-description")

    expect(title).toBeInTheDocument()
    expect(title?.textContent).toBe("Alert Title")
    expect(description).toBeInTheDocument()
    expect(description?.textContent).toBe("Alert Description")
  })

  test("renders startDecorator if provided", () => {
    const { container } = render(
      getComponent({
        startDecorator: <span data-testid="start-decorator">🌟</span>,
      })
    )
    const decorator = container.querySelector("[data-testid='start-decorator']")
    expect(decorator).toBeInTheDocument()
  })

  test("renders endDecorator if provided", () => {
    const { container } = render(
      getComponent({
        endDecorator: <span data-testid="end-decorator">End Decorator</span>,
      })
    )
    const endDecorator = container.querySelector(
      "[data-testid='end-decorator']"
    )
    expect(endDecorator).toBeInTheDocument()
  })

  test("calls onClose when close button is clicked", () => {
    const onCloseMock = vi.fn()
    const { container } = render(getComponent({ onClose: onCloseMock }))
    const closeButton = container.querySelector("button") as HTMLButtonElement
    closeButton?.click()
    expect(onCloseMock).toHaveBeenCalledTimes(1)
  })

  test("renders with full width if fullWidth prop is true", () => {
    const { container } = render(getComponent({ fullWidth: true }))
    const alert = container.querySelector('[role="alert"]')
    expect(alert?.className).toMatch(/alertFull/)
  })

  test("applies additional custom classes", () => {
    const { container } = render(getComponent({ className: "custom-class" }))
    const alert = container.querySelector('[role="alert"]')
    expect(alert?.className).toContain("custom-class")
  })

  test("applies appropriate type classes based on type prop", () => {
    const { container } = render(getComponent({ type: "warning" }))
    const alert = container.querySelector('[role="alert"]')
    expect(alert?.className).toMatch(/alertWarning/)
  })

  test("does not render close button if onClose is not provided", () => {
    const { container } = render(getComponent())
    const closeButton = container.querySelector("button")
    expect(closeButton).not.toBeInTheDocument()
  })
})
