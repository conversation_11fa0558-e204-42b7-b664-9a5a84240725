import React from "react"
import userEvent from "@testing-library/user-event"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Textarea, TextareaProps } from "../src/components/textarea"

describe("Textarea Component", () => {
  const getComponent = (props: TextareaProps) => (
    <ThemeProvider>
      <Textarea {...props} />
    </ThemeProvider>
  )

  test("should render the textarea component correctly", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const textarea = container.querySelector(".ApolloTextarea-textarea")
    expect(textarea).toBeInTheDocument()
  })

  test("should handle textarea value changes", async () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const textarea = container.querySelector(
      ".ApolloTextarea-textarea"
    ) as HTMLTextAreaElement
    await userEvent.type(textarea!, "test content")
    expect(textarea!.value).toBe("test content")
  })

  test("should be disabled when the disabled prop is set", () => {
    const { container } = render(getComponent({ disabled: true }))
    const textarea = container.querySelector(".ApolloTextarea-textarea")
    expect(textarea).toBeDisabled()
  })

  test("should show error state when the error prop is set", () => {
    const { container } = render(getComponent({ error: true }))
    const textareaRoot = container.querySelector(".ApolloTextarea-controlRoot")
    expect(textareaRoot!.className).toMatch(/textareaRootError/)
  })

  test("should render with different sizes", () => {
    const { container: containerSmall } = render(
      getComponent({ size: "small" })
    )
    const textarea = containerSmall.querySelector(".ApolloTextarea-textarea")
    expect(textarea!.className).toMatch(/textareaSmall/)

    const { container: containerMedium } = render(
      getComponent({ size: "medium" })
    )
    const textareaMedium = containerMedium.querySelector(
      ".ApolloTextarea-textarea"
    )
    expect(textareaMedium!.className).not.toMatch(/textareaSmall/)
  })

  test("should render full width when fullWidth prop is set", () => {
    const { container } = render(getComponent({ fullWidth: true }))
    const textareaRoot = container.querySelector(".ApolloTextarea-controlRoot")
    expect(textareaRoot!.className).toMatch(/textareaRootFullWidth/)
  })

  test("should handle focus and blur events", async () => {
    const handleFocus = vi.fn()
    const handleBlur = vi.fn()
    const { container } = render(
      getComponent({ onFocus: handleFocus, onBlur: handleBlur })
    )
    const textarea = container.querySelector(".ApolloTextarea-textarea")
    await userEvent.click(textarea!)
    expect(handleFocus).toHaveBeenCalled()
    await userEvent.tab()
    expect(handleBlur).toHaveBeenCalled()
  })

  test("should handle minRows and maxRows props correctly", () => {
    const { container } = render(getComponent({ minRows: 4, maxRows: 8 }))
    const textarea = container.querySelector(".ApolloTextarea-textarea")
    expect(textarea).toHaveAttribute("rows", "4")
  })

  test("should handle required prop correctly", () => {
    const { container } = render(
      getComponent({ label: "Test Label", required: true })
    )
    const label = container.querySelector("label")
    expect(label).toBeInTheDocument()
    expect(label!.innerHTML).toContain("*")
  })

  test("should display helper text when provided", () => {
    const helperText = "Helper text"
    const { container } = render(getComponent({ helperText }))
    const helperTextElement = container.querySelector(".ApolloField-helperText")
    expect(helperTextElement).toBeInTheDocument()
    expect(helperTextElement).toHaveTextContent(helperText)
  })

  test("should handle custom class names", () => {
    const { container } = render(getComponent({ className: "custom-class" }))
    const textareaRoot = container.querySelector(".ApolloTextarea-controlRoot")
    expect(textareaRoot!.className).toMatch(/custom-class/)
  })
})
