import React from "react"
import { Heart } from "@design-systems/apollo-icons"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { IconButton, type IconButtonProps } from "../src/components/icon-button"

const MockIcon = () => <Heart />

describe("IconButton component", () => {
  const getComponent = (props: IconButtonProps) => (
    <ThemeProvider>
      <IconButton {...props} />
    </ThemeProvider>
  )

  test("renders an icon button with default props", () => {
    const { container } = render(getComponent({ children: <MockIcon /> }))
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/ApolloIconButton-root/)
    expect(button?.className).toMatch(/iconButtonLarge/)
  })

  test("renders an icon button with custom size", () => {
    const { container } = render(
      getComponent({ size: "small", children: <MockIcon /> })
    )
    const button = container.querySelector("button")
    expect(button).toBeInTheDocument()
    expect(button?.className).toMatch(/iconButtonSmall/)
  })

  test("renders a disabled icon button", () => {
    const { container } = render(
      getComponent({ disabled: true, children: <MockIcon /> })
    )
    const button = container.querySelector("button")
    expect(button).toBeDisabled()
  })

  test("renders an anchor element when href is provided", () => {
    const { container } = render(
      getComponent({ href: "https://example.com", children: <MockIcon /> })
    )
    const link = container.querySelector("a")
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute("href", "https://example.com")
  })

  test("handles click events", () => {
    let clicked = false
    const handleClick = () => {
      clicked = true
    }
    const { container } = render(
      getComponent({ onClick: handleClick, children: <MockIcon /> })
    )
    const button = container.querySelector("button")
    button?.click()
    expect(clicked).toBe(true)
  })
})
