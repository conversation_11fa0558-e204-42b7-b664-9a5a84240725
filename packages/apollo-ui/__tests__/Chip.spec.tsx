import React from "react"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Chip, type ChipProps } from "../src/components/chip"

describe("Chip component", () => {
  const getComponent = ({ label, ...props }: ChipProps) => (
    <ThemeProvider>
      <Chip label={label} {...props} />
    </ThemeProvider>
  )

  test("renders a chip with default props", () => {
    const { container } = render(getComponent({ label: "Test Chip" }))
    const chip = container.querySelector(".ApolloChip-root")
    expect(chip).toBeInTheDocument()
    expect(chip?.className).toMatch(/chipRoot/)
    expect(chip?.textContent).toBe("Test Chip")
  })

  test("renders a small chip", () => {
    const { container } = render(
      getComponent({ label: "Small Chip", size: "small" })
    )
    const chip = container.querySelector(".ApolloChip-root")
    expect(chip).toBeInTheDocument()
    expect(chip?.className).toMatch(/chipRootSmall/)
  })

  test("renders a disabled chip", () => {
    const { container } = render(
      getComponent({ label: "Disabled Chip", disabled: true })
    )
    const chip = container.querySelector(".ApolloChip-root")
    expect(chip).toBeInTheDocument()
    expect(chip?.className).toMatch(/chipDisabled/)
  })

  test("renders a chip with close button when onClose is provided", () => {
    const { container } = render(
      getComponent({ label: "Closeable Chip", onClose: () => {} })
    )
    const closeButton = container.querySelector(".ApolloChip-closeIcon")
    expect(closeButton).toBeInTheDocument()
  })

  test("handles close button click", () => {
    const handleClose = vi.fn()
    const { container } = render(
      getComponent({ label: "Closeable Chip", onClose: handleClose })
    )
    const closeButton = container.querySelector(
      ".ApolloChip-closeIcon"
    ) as HTMLButtonElement
    closeButton?.click()
    expect(handleClose).toHaveBeenCalledTimes(1)
  })

  test("close button is disabled when chip is disabled", () => {
    const { container } = render(
      getComponent({
        label: "Disabled Chip",
        onClose: () => {},
        disabled: true,
      })
    )
    const closeButton = container.querySelector(".ApolloChip-closeIcon")
    expect(closeButton).toHaveAttribute("disabled")
  })

  test("renders with custom className", () => {
    const { container } = render(
      getComponent({ label: "Custom Chip", className: "custom-class" })
    )
    const chip = container.querySelector(".ApolloChip-root")
    expect(chip?.className).toContain("custom-class")
  })

  test("renders chip label with Typography component", () => {
    const { container } = render(getComponent({ label: "Typography Test" }))
    const label = container.querySelector(".ApolloChip-label")
    expect(label).toBeInTheDocument()
  })
})
