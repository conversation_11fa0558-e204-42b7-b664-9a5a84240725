import React from "react"
import { userEvent } from "@vitest/browser/context"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Accordion, type AccordionProps } from "../src/components/accordion"

describe("Accordion component", () => {
  const getComponent = (props?: AccordionProps) => (
    <ThemeProvider>
      <Accordion label="Accordion Label" {...props} />
    </ThemeProvider>
  )

  // Helper function to wait for panel animation to complete
  const waitForPanelAnimation = async () => {
    return new Promise((resolve) => {
      setTimeout(resolve, 300) // Wait for 100ms to ensure animation is complete
    })
  }

  test("renders an accordion with default props", () => {
    const { container } = render(
      getComponent({
        children: <div data-testid="content">Accordion Content</div>,
      })
    )

    const accordion = container.querySelector(".ApolloAccordion-container")
    expect(accordion).toBeInTheDocument()
    expect(accordion?.className).toMatch(/container/)
    expect(accordion?.className).not.toMatch(/containerFullWidth/)
    expect(accordion?.className).not.toMatch(/borderless/)
    expect(accordion?.className).not.toMatch(/error/)
  })

  test("renders an accordion with error variant", () => {
    const { container } = render(
      getComponent({
        variant: "error",
        children: <div>Accordion Content</div>,
      })
    )

    const accordion = container.querySelector(".ApolloAccordion-container")
    expect(accordion).toBeInTheDocument()
    expect(accordion?.className).toMatch(/error/)
  })

  test("renders an accordion with fullWidth", () => {
    const { container } = render(
      getComponent({
        fullWidth: true,
        children: <div>Accordion Content</div>,
      })
    )

    const accordion = container.querySelector(".ApolloAccordion-container")
    expect(accordion).toBeInTheDocument()
    expect(accordion?.className).toMatch(/containerFullWidth/)
  })

  test("renders an accordion with borderless style", () => {
    const { container } = render(
      getComponent({
        borderless: true,
        children: <div>Accordion Content</div>,
      })
    )

    const accordion = container.querySelector(".ApolloAccordion-container")
    expect(accordion).toBeInTheDocument()
    expect(accordion?.className).toMatch(/borderless/)
  })

  test("handles open state changes", async () => {
    const { container } = render(
      getComponent({
        children: <div data-testid="content">Accordion Content</div>,
      })
    )

    const trigger = container.querySelector(".ApolloAccordion-trigger")
    expect(trigger).toBeInTheDocument()

    // Initially, the accordion should be closed
    let panel = container.querySelector(".ApolloAccordion-panel")
    expect(panel).toBeNull()

    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()

    panel = container.querySelector(".ApolloAccordion-panel")
    expect(panel).toBeVisible()

    // Click to close
    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()

    panel = container.querySelector(".ApolloAccordion-panel")
    expect(panel).toBeNull()
  })

  test("renders with defaultOpen", async () => {
    const { container } = render(
      getComponent({
        defaultOpen: true,
        children: <div data-testid="content">Accordion Content</div>,
      })
    )

    await waitForPanelAnimation()
    const panel = container.querySelector(".ApolloAccordion-panel")
    expect(panel).toBeVisible()
  })

  test.skip("renders in disabled state", () => {
    const { container } = render(
      getComponent({
        disabled: true,
        children: <div>Accordion Content</div>,
      })
    )

    const root = container.querySelector("[data-disabled=true]")
    expect(root).toBeInTheDocument()
  })

  test("renders with different icon positions", () => {
    // Test start position
    const { container: startContainer } = render(
      getComponent({
        iconPosition: "start",
        children: <div>Accordion Content</div>,
      })
    )

    let triggerContent = startContainer.querySelector(
      ".ApolloAccordion-trigger-content"
    )
    // Verify icon is before content
    expect(triggerContent?.previousElementSibling).toBeInTheDocument()
    expect(triggerContent?.nextElementSibling).not.toBeInTheDocument()

    // Test both positions
    const { container: bothContainer } = render(
      getComponent({
        iconPosition: "both",
        children: <div>Accordion Content</div>,
      })
    )

    triggerContent = bothContainer.querySelector(
      ".ApolloAccordion-trigger-content"
    )
    // Verify icons on both sides
    expect(triggerContent?.previousElementSibling).toBeInTheDocument()
    expect(triggerContent?.nextElementSibling).toBeInTheDocument()
  })

  test("renders with primary icon variant", () => {
    const { container } = render(
      getComponent({
        iconVariant: "primary",
        children: <div>Accordion Content</div>,
      })
    )

    const icon = container.querySelector(".ApolloAccordion-trigger svg")

    expect(icon?.getAttribute("class")).toMatch(/iconPrimary/)
  })

  test("calls onOpenChange when toggled", async () => {
    const handleOpenChange = vi.fn()
    const { container } = render(
      getComponent({
        onOpenChange: handleOpenChange,
        children: <div>Accordion Content</div>,
      })
    )

    const trigger = container.querySelector(".ApolloAccordion-trigger")
    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()
    expect(handleOpenChange).toHaveBeenCalledWith(true)

    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()
    expect(handleOpenChange).toHaveBeenCalledWith(false)
  })

  test("keeps content in DOM when keepMounted is true", async () => {
    const { container, rerender } = render(
      getComponent({
        keepMounted: true,
        children: <div data-testid="content">Accordion Content</div>,
      })
    )

    // Check content is in DOM but not visible
    let content = container.querySelector("[data-testid=content]")
    expect(content).toBeInTheDocument()

    const trigger = container.querySelector(".ApolloAccordion-trigger")

    // Open accordion and check content is visible
    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()
    expect(content).toBeVisible()

    // Close accordion
    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()
    content = container.querySelector("[data-testid=content]")
    expect(content).toBeInTheDocument()

    // Test with keepMounted=false
    rerender(
      getComponent({
        keepMounted: false,
        children: <div data-testid="content">Accordion Content</div>,
      })
    )

    // Open and close again
    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()
    await userEvent.click(trigger as HTMLElement)
    await waitForPanelAnimation()

    // Check the behavior of the content visibility
    // Note: This may need adjustment based on actual Collapsible implementation
  })
})
