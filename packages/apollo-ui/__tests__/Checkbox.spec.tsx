import React from "react"

import "@vitest/browser/matchers.d.ts"

import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Checkbox, CheckboxProps } from "../src/components/checkbox"

describe("Checkbox component", () => {
  const getComponent = (props?: CheckboxProps) => (
    <ThemeProvider>
      <Checkbox {...props} />
    </ThemeProvider>
  )
  test("renders a checkbox with default props", () => {
    const { container } = render(getComponent())
    const checkbox = container.querySelector('button[role="checkbox"]')
    expect(checkbox).toBeInTheDocument()
    expect(checkbox?.className).toContain("ApolloCheckbox-checkbox")
  })

  test("renders a checkbox with label", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const label = container.querySelector(".ApolloCheckbox-label")
    expect(label).toBeInTheDocument()
    expect(label?.textContent).toBe("Test Label")
  })

  test("renders a disabled checkbox", () => {
    const { container } = render(getComponent({ disabled: true }))
    const checkbox = container.querySelector('button[role="checkbox"]')
    expect(checkbox).toHaveAttribute("data-disabled")
    expect(checkbox?.parentElement).toHaveAttribute("data-disabled")
  })

  test("renders an indeterminate checkbox", () => {
    const { container } = render(getComponent({ indeterminate: true }))
    const checkbox = container.querySelector('button[role="checkbox"]')
    expect(checkbox).toHaveAttribute("data-indeterminate")
  })

  test("handles checked state", () => {
    const { container } = render(getComponent({ checked: true }))
    const checkbox = container.querySelector('button[role="checkbox"]')
    expect(checkbox).toHaveAttribute("aria-checked", "true")
    expect(checkbox).toHaveAttribute("data-checked")
  })

  test("handles onChange events", () => {
    let changed = false
    const handleChange = () => {
      changed = true
    }
    const { container } = render(getComponent({ onChange: handleChange }))
    const checkbox = container.querySelector(
      'button[role="checkbox"]'
    ) as HTMLButtonElement
    checkbox?.click()
    expect(changed).toBe(true)
  })

  test("applies custom className", () => {
    const { container } = render(getComponent({ className: "custom-class" }))
    const root = container.querySelector(".ApolloCheckbox-root")
    expect(root?.className).toContain("custom-class")
  })

  test("renders disabled checkbox with label styles", () => {
    const { container } = render(
      getComponent({ disabled: true, label: "Disabled Checkbox" })
    )
    const label = container.querySelector(".ApolloCheckbox-label")
    expect(label?.className).toContain("checkboxLabelDisabled")
  })
})
