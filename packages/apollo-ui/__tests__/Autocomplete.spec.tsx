/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react"
import { userEvent } from "@vitest/browser/context"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import {
  Autocomplete,
  type AutocompleteProps,
} from "../src/components/autocomplete"

const mockOptions = [
  { label: "Option 1", value: 1 },
  { label: "Option 2", value: 2 },
  { label: "Option 3", value: 3 },
]

describe("Autocomplete component", () => {
  const getComponent = (props?: Partial<AutocompleteProps<any>>) => (
    <ThemeProvider>
      <Autocomplete options={mockOptions as any} fullWidth {...props} />
    </ThemeProvider>
  )

  const getPortalContainer = () =>
    document.querySelector(".ApolloAutocomplete-menuPopup")

  const waitForOptionsToRender = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(getPortalContainer())
      }, 100) // Wait for 100ms to ensure options are rendered
    })
  }

  describe("Single mode", () => {
    test("renders with default props", () => {
      const { container } = render(getComponent({}))
      const root = container.querySelector(".ApolloAutocomplete-root")
      expect(root).toBeInTheDocument()
    })

    test("opens dropdown on click", async () => {
      const { container } = render(getComponent({}))
      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      const options = portalContainer.querySelectorAll(".ApolloMenuItem-root")
      expect(options).toHaveLength(mockOptions.length)
    })

    test("selects an option", async () => {
      const onChange = vi.fn()
      const { container } = render(getComponent({ onChange }))

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      const option = portalContainer.querySelector(
        ".ApolloMenuItem-root"
      ) as HTMLElement
      option.click()

      expect(onChange).toHaveBeenCalledWith(1, expect.any(Object))
    })

    test("filters options when searching", async () => {
      const { container } = render(getComponent({ disableSearch: false }))

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      const input = portalContainer.querySelector(
        "input[data-focused]"
      ) as HTMLInputElement

      if (input) {
        await userEvent.fill(input, "1")
        const options = portalContainer.querySelectorAll(".ApolloMenuItem-root")
        expect(options).toHaveLength(1)
        expect(portalContainer.textContent).toContain("Option 1")
      }
    })

    test("shows loading state", async () => {
      const { container } = render(getComponent({ loading: true }))

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      expect(portalContainer.textContent).toContain("Loading...")
    })
  })

  describe("Multiple mode", () => {
    test("renders with multiple selection mode", () => {
      const { container } = render(getComponent({ multiple: true }))
      const root = container.querySelector(".ApolloAutocomplete-root")
      expect(root).toBeInTheDocument()
    })

    test("selects multiple options", async () => {
      const onChange = vi.fn()
      const { container } = render(
        <div style={{ minWidth: 500 }}>
          {getComponent({
            multiple: true,
            fullWidth: true,
            value: [1],
            onChange,
          })}
        </div>
      )

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      const option2 = portalContainer.querySelector(
        '.ApolloMenuItem-root[value="2"]'
      ) as HTMLElement

      option2.click()

      expect(onChange).toHaveBeenCalledWith([1, 2], expect.any(Object))
    })

    test("removes selected options via chip close button", async () => {
      const onChange = vi.fn()
      const { container } = render(
        getComponent({
          multiple: true,
          onChange,
          value: [1, 2],
        })
      )

      const autocomplete = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLInputElement

      await userEvent.hover(autocomplete)

      const closeButton = container.querySelector(
        ".ApolloAutocomplete-chipContainer .ApolloButton-root"
      ) as HTMLElement

      closeButton.click()

      expect(onChange).toHaveBeenCalledWith([2], expect.any(Object))
    })

    test("handles select all functionality", async () => {
      const onChange = vi.fn()
      const { container } = render(
        getComponent({
          multiple: true,
          showSelectAll: true,
          onChange,
        })
      )

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      const selectAll = portalContainer.querySelector(
        ".ApolloMenuItem-root"
      ) as HTMLElement
      selectAll.click()

      expect(onChange).toHaveBeenCalledWith([1, 2, 3], expect.any(Object))
    })

    test("clears all selected options", () => {
      const onChange = vi.fn()
      const { container } = render(
        getComponent({
          multiple: true,
          value: [1, 2],
          onChange,
        })
      )

      const clearButton = container.querySelector(
        ".ApolloAutocomplete-clearButton"
      ) as HTMLElement
      clearButton.click()

      expect(onChange).toHaveBeenCalledWith([], expect.any(Object))
    })
  })

  describe("Common functionality", () => {
    test("renders with custom placeholder", () => {
      const { container } = render(
        getComponent({ placeholder: "Select options" })
      )
      const input = container.querySelector(
        'input[placeholder="Select options"]'
      )
      expect(input).toBeInTheDocument()
    })

    test("renders in disabled state", () => {
      const { container } = render(getComponent({ disabled: true }))
      const trigger = container.querySelector(
        ".ApolloAutocomplete-inputWrapper input"
      ) as HTMLElement
      expect(trigger).toHaveAttribute("disabled")
    })

    test("handles custom loading component", async () => {
      const LoadingComponent = () => (
        <div data-testid="custom-loading">Custom loading...</div>
      )
      const { container } = render(
        getComponent({
          loading: true,
          loadingComponent: <LoadingComponent />,
        })
      )

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      const customLoading = portalContainer.querySelector(
        '[data-testid="custom-loading"]'
      )
      expect(customLoading).toBeInTheDocument()
    })

    test("handles empty state", async () => {
      const { container } = render(getComponent({ options: [] }))

      const trigger = container.querySelector(
        ".ApolloAutocomplete-trigger"
      ) as HTMLElement
      trigger.click()

      const portalContainer = (await waitForOptionsToRender()) as HTMLElement
      expect(portalContainer.textContent).toContain("Not found")
    })

    test("renders with error state", () => {
      const { container } = render(getComponent({ error: true }))
      const errorElement = container.querySelector(
        ".ApolloAutocomplete-inputWrapper input"
      )
      expect(errorElement).toHaveAttribute("data-invalid")
    })
  })
})
