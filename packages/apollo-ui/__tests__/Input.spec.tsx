import React from "react"
import userEvent from "@testing-library/user-event"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Input, type InputProps } from "../src/components/input"

describe("Input Component", () => {
  const getComponent = (props?: InputProps) => (
    <ThemeProvider>
      <Input {...props} />
    </ThemeProvider>
  )

  test("should render the input component correctly", () => {
    const { container } = render(getComponent({ label: "Test Label" }))
    const input = container.querySelector("input")
    expect(input).toBeInTheDocument()
  })

  test("should handle input value changes", async () => {
    const { container } = render(getComponent())
    const input = container.querySelector("input")
    await userEvent.type(input!, "test")
    expect(input!.value).toBe("test")
  })

  test("should display placeholder text", () => {
    const { container } = render(getComponent({ placeholder: "Enter text" }))
    const input = container.querySelector("input")
    expect(input!.placeholder).toBe("Enter text")
  })

  test("should be disabled when the disabled prop is set", () => {
    const { container } = render(getComponent({ disabled: true }))
    const input = container.querySelector("input")
    expect(input).toBeDisabled()
  })

  test("should show error state when the error prop is set", () => {
    const { container } = render(getComponent({ error: true }))
    const inputRoot = container.querySelector(".ApolloInput-controlRoot")
    expect(inputRoot!.className).toMatch(/inputRootError/)
  })

  test("should render with different sizes", () => {
    const { container: containerSmall } = render(
      getComponent({ size: "small" })
    )
    const inputRootSmall = containerSmall.querySelector(
      ".ApolloInput-controlRoot"
    )
    expect(inputRootSmall!.className).toMatch(/inputRootSmall/)

    const { container: containerMedium } = render(
      getComponent({ size: "medium" })
    )
    const inputRootMedium = containerMedium.querySelector(
      ".ApolloInput-controlRoot"
    )
    expect(inputRootMedium!.className).toMatch(/inputRootMedium/)
  })

  test("should render full width when fullWidth prop is set", () => {
    const { container } = render(getComponent({ fullWidth: true }))
    const inputRoot = container.querySelector(".ApolloInput-controlRoot")
    expect(inputRoot!.className).toMatch(/inputRootFullWidth/)
  })

  test("should render startDecorator correctly", () => {
    const { container } = render(
      getComponent({ startDecorator: <span>Start</span> })
    )
    const startDecorator = container.querySelector(
      ".ApolloInput-startDecorator"
    )
    expect(startDecorator).toBeInTheDocument()
    expect(startDecorator!.innerHTML).toBe("<span>Start</span>")
  })

  test("should render endDecorator correctly", () => {
    const { container } = render(
      getComponent({ endDecorator: <span>End</span> })
    )
    const endDecorator = container.querySelector(".ApolloInput-endDecorator")
    expect(endDecorator).toBeInTheDocument()
    expect(endDecorator!.innerHTML).toBe("<span>End</span>")
  })

  test("should handle focus and blur events", async () => {
    const handleFocus = vi.fn()
    const handleBlur = vi.fn()
    const { container } = render(
      getComponent({ onFocus: handleFocus, onBlur: handleBlur })
    )
    const input = container.querySelector("input")
    await userEvent.click(input!)
    expect(handleFocus).toHaveBeenCalled()
    await userEvent.tab()
    expect(handleBlur).toHaveBeenCalled()
  })

  test("should handle autoFocus prop correctly", () => {
    const { container } = render(getComponent({ autoFocus: true }))
    const input = container.querySelector("input")
    expect(document.activeElement).toBe(input)
  })

  test("should handle readOnly prop correctly", () => {
    const { container } = render(getComponent({ readOnly: true }))
    const input = container.querySelector("input")
    expect(input!.readOnly).toBe(true)
  })

  test("should handle required prop correctly", () => {
    const { container } = render(
      getComponent({ label: "Test Label", required: true })
    )
    const label = container.querySelector("label")
    expect(label).toBeInTheDocument()
    expect(label!.innerHTML).toContain("*")
  })

  test("should handle custom class names", () => {
    const { container } = render(getComponent({ className: "custom-class" }))
    const inputRoot = container.querySelector(".ApolloInput-controlRoot")
    expect(inputRoot!.className).toMatch(/custom-class/)
  })
})
