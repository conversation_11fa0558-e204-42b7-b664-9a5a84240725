import React from "react"
import userEvent from "@testing-library/user-event"
import { describe, expect, test, vi } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { UploadBox, type UploadBoxProps } from "../src/components/upload-box"

describe("UploadBox Component", () => {
  const getComponent = <Multiple extends boolean = false>(
    props: UploadBoxProps<Multiple>
  ) => (
    <ThemeProvider>
      <UploadBox {...props} />
    </ThemeProvider>
  )

  test("should render the upload button", () => {
    const { container } = render(getComponent({}))
    const buttons = Array.from(container.querySelectorAll("button"))
    const uploadButton = buttons.find((button) =>
      button.textContent?.includes("Upload File")
    )
    expect(uploadButton).toBeInTheDocument()
  })

  test("should disable upload button when disabled prop is true", () => {
    const { container } = render(getComponent({ disabled: true }))
    const buttons = Array.from(container.querySelectorAll("button"))
    const uploadButton = buttons.find((button) =>
      button.textContent?.includes("Upload File")
    )
    expect(uploadButton).toBeDisabled()
  })

  test("should display file conditions by default", () => {
    const { container } = render(getComponent({}))
    const html = container.innerHTML
    expect(html.includes("File support")).toBeTruthy()
    expect(html.includes("Size not more than")).toBeTruthy()
  })

  test("should use custom allowed extensions", () => {
    const allowedExtensions = ["pdf", "docx"]
    const { container } = render(
      getComponent({ allowedFilesExtension: allowedExtensions })
    )
    const html = container.innerHTML
    expect(html.includes("File support .pdf, .docx only")).toBeTruthy()
  })

  test("should use custom max file size", () => {
    const maxSize = 10 * 1024 * 1024 // 10MB
    const { container } = render(getComponent({ maxFileSizeInBytes: maxSize }))
    const html = container.innerHTML
    expect(html.includes("Size not more than 10 MB")).toBeTruthy()
  })

  test("should show error message when provided", () => {
    const errorMessage = "Test error message"
    const { container } = render(getComponent({ errorMessage }))
    const html = container.innerHTML
    expect(html.includes(errorMessage)).toBeTruthy()
  })

  test("should call onUpload callback when a file is selected", async () => {
    const onUpload = vi.fn()
    const { container } = render(getComponent({ onUpload }))

    const file = new File(["test content"], "test.jpg", { type: "image/jpeg" })
    const input = container.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement

    await userEvent.upload(input, file)

    expect(onUpload).toHaveBeenCalledWith(file)
  })

  test("should display uploaded single file when value is provided", () => {
    const fileName = "test-file.jpg"
    const { container } = render(
      getComponent({
        value: { name: fileName },
        onDelete: vi.fn(),
      })
    )

    const html = container.innerHTML
    expect(html.includes(fileName)).toBeTruthy()
  })

  test("should display file count in multiple mode", () => {
    const { container } = render(
      getComponent({
        multiple: true,
        value: [{ name: "file1.jpg" }, { name: "file2.jpg" }],
      })
    )

    const html = container.innerHTML
    expect(html.includes("(2 Files uploaded)")).toBeTruthy()
  })

  test("should display files list in multiple mode", () => {
    const { container } = render(
      getComponent({
        multiple: true,
        value: [{ name: "file1.jpg" }, { name: "file2.jpg" }],
        onDelete: vi.fn(),
      })
    )

    const html = container.innerHTML
    expect(html.includes("file1.jpg")).toBeTruthy()
    expect(html.includes("file2.jpg")).toBeTruthy()
  })

  test("should call onDelete callback when delete button is clicked", async () => {
    const onDelete = vi.fn()
    const { container } = render(
      getComponent({
        value: { name: "test-file.jpg" },
        onDelete,
      })
    )

    // Find the file item and its delete button
    const fileItems = container.querySelectorAll("div")
    // Look for the div containing the filename text
    let fileItem
    for (const item of Array.from(fileItems)) {
      if (item?.textContent?.includes("test-file.jpg")) {
        fileItem = item
        break
      }
    }
    const deleteButton = fileItem?.querySelector("button")
    if (!deleteButton) {
      throw new Error("Delete button not found")
    }
    await userEvent.click(deleteButton)

    expect(onDelete).toHaveBeenCalled()
  })

  test("should display uploading state when fileState.uploading is true", () => {
    const { container } = render(
      getComponent({
        value: { name: "test-file.jpg" },
        fileState: { key: "test-file.jpg", uploading: true },
        onDelete: vi.fn(),
        onCancelUpload: vi.fn(),
      })
    )

    const html = container.innerHTML
    expect(html.includes("Uploading")).toBeTruthy()
  })

  test("should disable upload button when file limit is reached in multiple mode", () => {
    const { container } = render(
      getComponent({
        multiple: true,
        fileLimit: 2,
        value: [{ name: "file1.jpg" }, { name: "file2.jpg" }],
      })
    )

    const buttons = Array.from(container.querySelectorAll("button"))
    const uploadButton = buttons.find((button) =>
      button.textContent?.includes("Upload File")
    )
    expect(uploadButton).toBeDisabled()
  })
})
