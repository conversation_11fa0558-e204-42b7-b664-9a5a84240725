import React from "react"
import { describe, expect, test } from "vitest"
import { render } from "vitest-browser-react"

import { ThemeProvider } from "../src"
import { Radio, type RadioProps } from "../src/components/radio"

describe("Radio component", () => {
  const getComponent = (props?: RadioProps) => (
    <ThemeProvider>
      <Radio value="defaultValue" {...props} />
    </ThemeProvider>
  )

  test("renders a radio button with default props", () => {
    const { container } = render(
      getComponent({ value: "default", children: "Option 1" })
    )
    const radio = container.querySelector(".ApolloRadio-root")
    expect(radio).toBeInTheDocument()
    expect(radio?.className).toContain("ApolloRadio-root")
  })

  test("renders a radio button with a label", () => {
    const { container } = render(
      getComponent({ value: "label", children: "Option 1" })
    )
    const label = container.querySelector(".ApolloRadio-labelText")
    expect(label).toBeInTheDocument()
    expect(label?.textContent).toBe("Option 1")
  })

  test("applies custom className", () => {
    const { container } = render(
      getComponent({
        value: "custom-class",
        className: "custom-class",
        children: "Option 1",
      })
    )
    const radio = container.querySelector(".ApolloRadio-root")
    expect(radio?.className).toContain("custom-class")
  })

  test("renders with custom labelProps", () => {
    const { container } = render(
      getComponent({
        value: "custom-label",
        labelProps: { className: "custom-label" },
        children: "Option 1",
      })
    )
    const label = container.querySelector(".ApolloRadio-label")
    expect(label?.className).toMatch("custom-label")
  })
})
