/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, type ComponentType, type ReactNode } from "react"
import classNames from "classnames"

import { DESIGN_SYSTEM_PREFIX } from "../constants/component"
import type {
  ComponentSlotProps as BaseComponentSlotProps,
  GetSlotProps,
  GetSlotState,
} from "../types/component"

export type UseSlotPropsOptions<ComponentSlotProps> = {
  slotKey: string
  componentName: string
  keyPrefix?: string
  render: (props: GetSlotProps<ComponentSlotProps>) => ReactNode
  props: GetSlotProps<ComponentSlotProps>
  state?: GetSlotState<ComponentSlotProps>
  slotProps?: ComponentSlotProps
}

export function useSlotProps<
  SlotProps extends BaseComponentSlotProps<object, any>,
>({
  componentName,
  slotKey,
  props,
  state,
  slotProps,
  render,
  keyPrefix = DESIGN_SYSTEM_PREFIX,
}: UseSlotPropsOptions<SlotProps>): {
  render: ReactNode
  Component: ComponentType<any>
} {
  const baseClassName = useMemo(
    () => `${keyPrefix}${componentName}-${slotKey}`,
    [componentName, keyPrefix, slotKey]
  )

  const slotPropsWithOutRender = useMemo(() => {
    if (slotProps?.render === undefined) {
      return slotProps
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { render: _, ...rest } = slotProps
    return rest
  }, [slotProps])

  return useMemo(() => {
    const renderProps = {
      ...props,
      ...slotPropsWithOutRender,
      className: classNames(
        baseClassName,
        props?.className,
        slotPropsWithOutRender?.className
      ),
    }
    const Component = slotProps?.render
      ? ({ children, ...componentProps }: any) => {
          const childrenProp = children ?? renderProps?.children
          return (
            slotProps.render?.(
              {
                ...renderProps,
                ...componentProps,
                children: childrenProp,
              },
              state
            ) ?? childrenProp
          )
        }
      : ({ children, ...componentProps }: any) => {
          const childrenProp = children ?? renderProps?.children
          return (
            render({
              ...renderProps,
              ...componentProps,
              children: childrenProp,
            }) ?? childrenProp
          )
        }

    return {
      render: slotProps?.render?.(props, state) ?? render(props),
      Component,
    }
  }, [slotProps, props, state, render, slotPropsWithOutRender, baseClassName])
}
