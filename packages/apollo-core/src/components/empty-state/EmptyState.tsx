import { ComponentType } from "react"

import { useSlotProps } from "../../hooks"
import { Illustration, IllustrationProps } from "../illustration"
import type {
  EmptyStateContainerSlotProps,
  EmptyStateContentWrapperSlotProps,
  EmptyStateDescritionSloptProps,
  EmptyStateErrorMessageSlotProps,
  EmptyStateIllustrationSlotProps,
  EmptyStateIllustrationWrapperSlotProps,
  EmptyStateProps,
  EmptyStateTitleSlotProps,
} from "./EmptyStateProps"

const COMPONENT_NAME = "EmptyState"

export function EmptyState({
  title,
  description,
  name,
  actions,
  errorMessage,
  slots,
}: EmptyStateProps) {
  const { Component: Container } = useSlotProps<EmptyStateContainerSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "container",
    props: {},
    slotProps: slots?.container,
    render: ({ className, children, ...props }) => (
      <div {...props} className={className}>
        {children}
      </div>
    ),
  })

  const { Component: IllustrationWrapper } =
    useSlotProps<EmptyStateIllustrationWrapperSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "illustrationWrapper",
      props: {},
      slotProps: slots?.illustrationWrapper,
      render: ({ className, children, ...props }) => (
        <div {...props} className={className}>
          {children}
        </div>
      ),
    })

  const { Component: IllustrationComponent } =
    useSlotProps<EmptyStateIllustrationSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "illustration",
      props: { name },
      slotProps: slots?.illustration,
      render: ({ className, name, ...props }) => {
        const IllustrationComponent = Illustration[
          name as keyof typeof Illustration
        ] as ComponentType<IllustrationProps>
        if (!IllustrationComponent) {
          return null
        }
        return <IllustrationComponent {...props} className={className} />
      },
    })

  const { Component: Title } = useSlotProps<EmptyStateTitleSlotProps>({
    componentName: COMPONENT_NAME,
    slotKey: "title",
    props: { title },
    slotProps: slots?.title,
    render: ({ title, className, ...props }) =>
      title ? (
        <p {...props} className={className}>
          {title}
        </p>
      ) : null,
  })

  const { Component: Description } =
    useSlotProps<EmptyStateDescritionSloptProps>({
      componentName: COMPONENT_NAME,
      slotKey: "description",
      props: { description },
      slotProps: slots?.description,
      render: ({ description, className, ...props }) =>
        description ? (
          <p {...props} className={className}>
            {description}
          </p>
        ) : null,
    })

  const { Component: ContentWrapper } =
    useSlotProps<EmptyStateContentWrapperSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "contentWrapper",
      props: {},
      slotProps: slots?.contentWrapper,
      render: ({ className, children, ...props }) =>
        title && description ? (
          <div {...props} className={className}>
            {children}
          </div>
        ) : null,
    })

  const { Component: ActionWrapper } =
    useSlotProps<EmptyStateContainerSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "actionWrapper",
      props: {},
      slotProps: slots?.actionWrapper,
      render: ({ className, children, ...props }) =>
        actions ? (
          <div {...props} className={className}>
            {children}
          </div>
        ) : null,
    })

  const { Component: ErrorMessage } =
    useSlotProps<EmptyStateErrorMessageSlotProps>({
      componentName: COMPONENT_NAME,
      slotKey: "errorMessage",
      props: { errorMessage },
      slotProps: slots?.errorMessage,
      render: ({ errorMessage, className, ...props }) =>
        errorMessage ? (
          <div {...props} className={className}>
            {errorMessage}
          </div>
        ) : null,
    })

  return (
    <Container>
      <IllustrationWrapper>
        <IllustrationComponent />
      </IllustrationWrapper>
      <ContentWrapper>
        <Title />
        <ErrorMessage />
        <Description />
      </ContentWrapper>
      <ActionWrapper>{actions}</ActionWrapper>
    </Container>
  )
}
