import { ComponentProps } from "react"

import type { ComponentSlotProps } from "../../types"
import * as Illustration from "../illustration/illustration"

export type EmptyStateProps = {
  title?: string
  description?: string
  name?: keyof typeof Illustration
  actions?: React.ReactNode
  errorMessage?: React.ReactNode
  slots: EmptyStateSlots
}

export type EmptyStateSlots = {
  illustrationWrapper?: ComponentSlotProps<EmptyStateIllustrationWrapperSlotProps>
  illustration?: ComponentSlotProps<EmptyStateIllustrationSlotProps>
  title?: ComponentSlotProps<EmptyStateTitleSlotProps>
  description?: ComponentSlotProps<EmptyStateDescritionSloptProps>
  container?: ComponentSlotProps<EmptyStateContainerSlotProps>
  contentWrapper?: ComponentSlotProps<EmptyStateContentWrapperSlotProps>
  actionWrapper?: ComponentSlotProps<EmptyStateActionWrapperSlotProps>
  errorMessage?: ComponentSlotProps<EmptyStateErrorMessageSlotProps>
}

export type EmptyStateActionWrapperSlotProps = {
  className?: string
} & ComponentProps<"div">

export type EmptyStateIllustrationWrapperSlotProps = {
  className?: string
} & ComponentProps<"div">

export type EmptyStateIllustrationSlotProps = {
  name?: string
} & ComponentProps<"svg">

export type EmptyStateTitleSlotProps = {
  title?: string
} & ComponentProps<"p">

export type EmptyStateDescritionSloptProps = {
  description?: string
} & ComponentProps<"p">

export type EmptyStateContainerSlotProps = {
  className?: string
} & ComponentProps<"div">

export type EmptyStateContentWrapperSlotProps = {
  className?: string
} & ComponentProps<"div">

export type EmptyStateErrorMessageSlotProps = {
  errorMessage?: React.ReactNode
} & ComponentProps<"div">
