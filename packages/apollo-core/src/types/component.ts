import type { PropsWithChildren, ReactNode } from "react"

export type WithBaseSlotProps<Props> = PropsWithChildren<
  {
    className?: string
  } & Props
>

export type ComponentSlotProps<Props, State = object> = {
  render?: SlotRenderFunction<WithBaseSlotProps<Props>, State>
} & WithBaseSlotProps<Props>

export type GetSlotProps<T> =
  T extends ComponentSlotProps<infer P, object> ? WithBaseSlotProps<P> : never

// Extract the State type from Slot<Props, State>
export type GetSlotState<T> =
  T extends ComponentSlotProps<object, infer S> ? S : never

export type SlotRenderFunction<Props, State> = (
  props: Props,
  state: State
) => ReactNode
